-- SAT模考系统 - 创建模考相关表
-- V53__create_mock_exam_tables.sql
-- 完整的模考数据流：题库→组卷库→试卷库→API→按模块发题→记录session_id→session库→考试临时状态库→答题结果库→分数库→能力统计库
-- 重点支持考试状态恢复功能和历史记录管理

BEGIN;

-- ============================================================================
-- 1. 创建模考会话表（Session库）
-- ============================================================================

CREATE TABLE IF NOT EXISTS "public"."t_sat_mock_exam_session" (
    "session_id" VARCHAR(255) PRIMARY KEY,
    "user_id" BIGINT NOT NULL,
    "paper_id" BIGINT NOT NULL,
    "exam_type" VARCHAR(20) NOT NULL CHECK ("exam_type" IN ('full', 'math', 'reading')),
    "current_subject" VARCHAR(20) DEFAULT 'reading',
    "current_module_type" VARCHAR(10),
    "session_status" VARCHAR(20) NOT NULL DEFAULT 'in_progress' CHECK ("session_status" IN ('in_progress', 'completed')),
    "break_start_time" TIMESTAMPTZ,
    "break_end_time" TIMESTAMPTZ,
    "total_time_seconds" INTEGER DEFAULT 0,
    "reading_time_seconds" INTEGER DEFAULT 0,
    "math_time_seconds" INTEGER DEFAULT 0,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completed_at" TIMESTAMPTZ
);

-- 创建索引
CREATE INDEX "idx_mock_exam_session_user_id" ON "public"."t_sat_mock_exam_session"("user_id");
CREATE INDEX "idx_mock_exam_session_paper_id" ON "public"."t_sat_mock_exam_session"("paper_id");
CREATE INDEX "idx_mock_exam_session_status" ON "public"."t_sat_mock_exam_session"("session_status");
CREATE INDEX "idx_mock_exam_session_created_at" ON "public"."t_sat_mock_exam_session"("created_at");

-- 添加字段注释
COMMENT ON TABLE "public"."t_sat_mock_exam_session" IS 'SAT模考会话表，管理模考的整体状态和进度，支持状态恢复';
COMMENT ON COLUMN "public"."t_sat_mock_exam_session"."session_id" IS '会话ID，唯一标识一次模考';
COMMENT ON COLUMN "public"."t_sat_mock_exam_session"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."t_sat_mock_exam_session"."paper_id" IS '试卷ID，关联t_sat_paper表';
COMMENT ON COLUMN "public"."t_sat_mock_exam_session"."exam_type" IS '考试类型：full=全长模考, math=仅数学, reading=仅语言';
COMMENT ON COLUMN "public"."t_sat_mock_exam_session"."current_subject" IS '当前所在subject：reading=语言部分, math=数学部分';
COMMENT ON COLUMN "public"."t_sat_mock_exam_session"."current_module_type" IS '当前模块类型：1=第一模块, 2E=第二模块简单版, 2H=第二模块困难版';
COMMENT ON COLUMN "public"."t_sat_mock_exam_session"."session_status" IS '会话状态：in_progress=进行中, break=休息中, completed=已完成, abandoned=已放弃';
COMMENT ON COLUMN "public"."t_sat_mock_exam_session"."break_start_time" IS '休息开始时间';
COMMENT ON COLUMN "public"."t_sat_mock_exam_session"."break_end_time" IS '休息结束时间';
COMMENT ON COLUMN "public"."t_sat_mock_exam_session"."total_time_seconds" IS '总用时（秒）';
COMMENT ON COLUMN "public"."t_sat_mock_exam_session"."reading_time_seconds" IS '语言部分用时（秒）';
COMMENT ON COLUMN "public"."t_sat_mock_exam_session"."math_time_seconds" IS '数学部分用时（秒）';

-- ============================================================================
-- 2. 创建模考答题记录表（答题结果库）
-- ============================================================================

CREATE TABLE IF NOT EXISTS "public"."t_sat_mock_exam_answer" (
    "id" BIGSERIAL PRIMARY KEY,
    "session_id" VARCHAR(255) NOT NULL,
    "user_id" BIGINT NOT NULL,
    "paper_id" BIGINT NOT NULL,
    "question_id" INTEGER NOT NULL,
    "module_type" VARCHAR(10) NOT NULL,
    "module_sequence" INTEGER NOT NULL,
    "user_answer" VARCHAR(255),
    "is_correct" BOOLEAN,
    "response_time_seconds" INTEGER,
    "answer_status" VARCHAR(20) NOT NULL DEFAULT 'unanswered' CHECK ("answer_status" IN ('answered', 'unanswered', 'skipped')),
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX "idx_mock_exam_answer_session_id" ON "public"."t_sat_mock_exam_answer"("session_id");
CREATE INDEX "idx_mock_exam_answer_user_id" ON "public"."t_sat_mock_exam_answer"("user_id");
CREATE INDEX "idx_mock_exam_answer_question_id" ON "public"."t_sat_mock_exam_answer"("question_id");
CREATE INDEX "idx_mock_exam_answer_module_type" ON "public"."t_sat_mock_exam_answer"("module_type");
CREATE UNIQUE INDEX "idx_mock_exam_answer_unique" ON "public"."t_sat_mock_exam_answer"("session_id", "question_id");

-- 添加字段注释
COMMENT ON TABLE "public"."t_sat_mock_exam_answer" IS 'SAT模考答题记录表，记录每道题的答题情况';
COMMENT ON COLUMN "public"."t_sat_mock_exam_answer"."session_id" IS '会话ID，关联t_sat_mock_exam_session表';
COMMENT ON COLUMN "public"."t_sat_mock_exam_answer"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."t_sat_mock_exam_answer"."paper_id" IS '试卷ID';
COMMENT ON COLUMN "public"."t_sat_mock_exam_answer"."question_id" IS '题目ID，关联t_sat_question表';
COMMENT ON COLUMN "public"."t_sat_mock_exam_answer"."module_type" IS '模块类型：1, 2E, 2H';
COMMENT ON COLUMN "public"."t_sat_mock_exam_answer"."module_sequence" IS '题目在模块中的顺序';
COMMENT ON COLUMN "public"."t_sat_mock_exam_answer"."user_answer" IS '用户答案';
COMMENT ON COLUMN "public"."t_sat_mock_exam_answer"."is_correct" IS '是否正确';
COMMENT ON COLUMN "public"."t_sat_mock_exam_answer"."response_time_seconds" IS '答题用时（秒）';
COMMENT ON COLUMN "public"."t_sat_mock_exam_answer"."answer_status" IS '答题状态：answered=已答, unanswered=未答, skipped=跳过';

-- ============================================================================
-- 3. 创建模考模块进度表（考试临时状态库）
-- ============================================================================

CREATE TABLE IF NOT EXISTS "public"."t_sat_mock_exam_module_progress" (
    "id" BIGSERIAL PRIMARY KEY,
    "session_id" VARCHAR(255) NOT NULL,
    "user_id" BIGINT NOT NULL,
    "paper_id" BIGINT NOT NULL,
    "module_type" VARCHAR(10) NOT NULL,
    "subject" VARCHAR(50) NOT NULL,
    "total_questions" INTEGER NOT NULL,
    "answered_questions" INTEGER DEFAULT 0,
    "correct_questions" INTEGER DEFAULT 0,
    "time_limit_seconds" INTEGER NOT NULL,
    "time_used_seconds" INTEGER DEFAULT 0,
    "remaining_time_seconds" INTEGER,
    "module_status" VARCHAR(20) NOT NULL DEFAULT 'not_started' CHECK ("module_status" IN ('not_started', 'in_progress', 'completed', 'submitted')),
    "started_at" TIMESTAMPTZ,
    "completed_at" TIMESTAMPTZ,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX "idx_mock_exam_module_session_id" ON "public"."t_sat_mock_exam_module_progress"("session_id");
CREATE INDEX "idx_mock_exam_module_user_id" ON "public"."t_sat_mock_exam_module_progress"("user_id");
CREATE INDEX "idx_mock_exam_module_status" ON "public"."t_sat_mock_exam_module_progress"("module_status");
CREATE UNIQUE INDEX "idx_mock_exam_module_unique" ON "public"."t_sat_mock_exam_module_progress"("session_id", "module_type");

-- 添加字段注释
COMMENT ON TABLE "public"."t_sat_mock_exam_module_progress" IS 'SAT模考模块进度表，跟踪每个模块的完成情况，支持状态恢复';
COMMENT ON COLUMN "public"."t_sat_mock_exam_module_progress"."session_id" IS '会话ID';
COMMENT ON COLUMN "public"."t_sat_mock_exam_module_progress"."module_type" IS '模块类型：1, 2E, 2H';
COMMENT ON COLUMN "public"."t_sat_mock_exam_module_progress"."subject" IS '学科：reading, math';
COMMENT ON COLUMN "public"."t_sat_mock_exam_module_progress"."total_questions" IS '模块总题数';
COMMENT ON COLUMN "public"."t_sat_mock_exam_module_progress"."answered_questions" IS '已答题数';
COMMENT ON COLUMN "public"."t_sat_mock_exam_module_progress"."correct_questions" IS '答对题数';
COMMENT ON COLUMN "public"."t_sat_mock_exam_module_progress"."time_limit_seconds" IS '时间限制（秒）';
COMMENT ON COLUMN "public"."t_sat_mock_exam_module_progress"."time_used_seconds" IS '已用时间（秒）';
COMMENT ON COLUMN "public"."t_sat_mock_exam_module_progress"."remaining_time_seconds" IS '剩余时间（秒）- 状态恢复的关键字段';
COMMENT ON COLUMN "public"."t_sat_mock_exam_module_progress"."module_status" IS '模块状态：not_started=未开始, in_progress=进行中, completed=已完成, submitted=已提交';

-- ============================================================================
-- 4. 创建模考历史记录表（分数库/历史记录库）
-- ============================================================================

CREATE TABLE IF NOT EXISTS "public"."t_sat_mock_exam_history" (
    "id" BIGSERIAL PRIMARY KEY,
    "session_id" VARCHAR(255) NOT NULL UNIQUE,
    "user_id" BIGINT NOT NULL,
    "paper_id" BIGINT NOT NULL,
    "paper_name" VARCHAR(255),
    "exam_type" VARCHAR(20) NOT NULL CHECK ("exam_type" IN ('full', 'math', 'reading')),

    -- 分数相关字段
    "total_score" INTEGER,
    "reading_score" INTEGER,
    "math_score" INTEGER,
    "max_possible_score" INTEGER,

    -- 统计字段
    "total_questions" INTEGER NOT NULL DEFAULT 0,
    "answered_questions" INTEGER NOT NULL DEFAULT 0,
    "correct_questions" INTEGER NOT NULL DEFAULT 0,
    "skipped_questions" INTEGER NOT NULL DEFAULT 0,
    "accuracy_rate" DECIMAL(5,4) DEFAULT 0,

    -- 时间统计
    "total_time_seconds" INTEGER DEFAULT 0,
    "reading_time_seconds" INTEGER DEFAULT 0,
    "math_time_seconds" INTEGER DEFAULT 0,
    "avg_response_time_seconds" DECIMAL(8,2),

    -- 学科细分统计
    "reading_total_questions" INTEGER DEFAULT 0,
    "reading_correct_questions" INTEGER DEFAULT 0,
    "reading_accuracy_rate" DECIMAL(5,4) DEFAULT 0,
    "math_total_questions" INTEGER DEFAULT 0,
    "math_correct_questions" INTEGER DEFAULT 0,
    "math_accuracy_rate" DECIMAL(5,4) DEFAULT 0,

    -- 考试状态
    "exam_status" VARCHAR(20) NOT NULL DEFAULT 'completed' CHECK ("exam_status" IN ('completed', 'abandoned', 'timeout')),
    "completion_percentage" DECIMAL(5,2) DEFAULT 0,

    -- 时间记录
    "started_at" TIMESTAMPTZ NOT NULL,
    "completed_at" TIMESTAMPTZ,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX "idx_mock_exam_history_user_id" ON "public"."t_sat_mock_exam_history"("user_id");
CREATE INDEX "idx_mock_exam_history_paper_id" ON "public"."t_sat_mock_exam_history"("paper_id");
CREATE INDEX "idx_mock_exam_history_exam_type" ON "public"."t_sat_mock_exam_history"("exam_type");
CREATE INDEX "idx_mock_exam_history_total_score" ON "public"."t_sat_mock_exam_history"("total_score");
CREATE INDEX "idx_mock_exam_history_completed_at" ON "public"."t_sat_mock_exam_history"("completed_at");
CREATE INDEX "idx_mock_exam_history_user_completed" ON "public"."t_sat_mock_exam_history"("user_id", "completed_at");

-- 添加字段注释
COMMENT ON TABLE "public"."t_sat_mock_exam_history" IS 'SAT模考历史记录表，记录每个用户的考试记录和分数';
COMMENT ON COLUMN "public"."t_sat_mock_exam_history"."session_id" IS '关联的会话ID，唯一标识一次考试';
COMMENT ON COLUMN "public"."t_sat_mock_exam_history"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."t_sat_mock_exam_history"."paper_id" IS '试卷ID';
COMMENT ON COLUMN "public"."t_sat_mock_exam_history"."paper_name" IS '试卷名称，冗余存储便于查询';
COMMENT ON COLUMN "public"."t_sat_mock_exam_history"."total_score" IS '总分';
COMMENT ON COLUMN "public"."t_sat_mock_exam_history"."reading_score" IS '语言部分分数';
COMMENT ON COLUMN "public"."t_sat_mock_exam_history"."math_score" IS '数学部分分数';
COMMENT ON COLUMN "public"."t_sat_mock_exam_history"."max_possible_score" IS '满分';
COMMENT ON COLUMN "public"."t_sat_mock_exam_history"."accuracy_rate" IS '总体正确率';
COMMENT ON COLUMN "public"."t_sat_mock_exam_history"."exam_status" IS '考试状态：completed=完成, abandoned=放弃, timeout=超时';
COMMENT ON COLUMN "public"."t_sat_mock_exam_history"."completion_percentage" IS '完成百分比';
COMMENT ON COLUMN "public"."t_sat_mock_exam_history"."reading_accuracy_rate" IS '语言部分正确率';
COMMENT ON COLUMN "public"."t_sat_mock_exam_history"."math_accuracy_rate" IS '数学部分正确率';

-- ============================================================================
-- 5. 创建模考章节能力统计表（能力统计库）
-- ============================================================================

CREATE TABLE IF NOT EXISTS "public"."t_sat_mock_exam_chapter_ability" (
    "id" BIGSERIAL PRIMARY KEY,
    "session_id" VARCHAR(255) NOT NULL,
    "user_id" BIGINT NOT NULL,
    "chapter_id" INTEGER NOT NULL,
    "chapter_name" VARCHAR(100) NOT NULL,
    "subject_id" INTEGER NOT NULL,

    -- 章节答题统计
    "total_questions" INTEGER NOT NULL DEFAULT 0,
    "answered_questions" INTEGER NOT NULL DEFAULT 0,
    "correct_questions" INTEGER NOT NULL DEFAULT 0,
    "skipped_questions" INTEGER NOT NULL DEFAULT 0,

    -- 章节能力计算
    "accuracy_rate" DECIMAL(5,4) DEFAULT 0,
    "question_percentage" DECIMAL(5,2) DEFAULT 0,  -- 该章节题量占比
    "ability_level" INTEGER DEFAULT 0 CHECK ("ability_level" BETWEEN 0 AND 7),  -- 7等份能力等级

    -- 时间统计
    "total_time_seconds" INTEGER DEFAULT 0,
    "avg_response_time_seconds" DECIMAL(8,2),

    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX "idx_mock_exam_chapter_session_id" ON "public"."t_sat_mock_exam_chapter_ability"("session_id");
CREATE INDEX "idx_mock_exam_chapter_user_id" ON "public"."t_sat_mock_exam_chapter_ability"("user_id");
CREATE INDEX "idx_mock_exam_chapter_chapter_id" ON "public"."t_sat_mock_exam_chapter_ability"("chapter_id");
CREATE INDEX "idx_mock_exam_chapter_subject_id" ON "public"."t_sat_mock_exam_chapter_ability"("subject_id");
CREATE UNIQUE INDEX "idx_mock_exam_chapter_unique" ON "public"."t_sat_mock_exam_chapter_ability"("session_id", "chapter_id");

-- 添加字段注释
COMMENT ON TABLE "public"."t_sat_mock_exam_chapter_ability" IS 'SAT模考章节能力统计表，记录每个章节的答题正确率和能力等级';
COMMENT ON COLUMN "public"."t_sat_mock_exam_chapter_ability"."session_id" IS '关联的会话ID';
COMMENT ON COLUMN "public"."t_sat_mock_exam_chapter_ability"."chapter_id" IS '章节ID，关联t_sat_chapter表';
COMMENT ON COLUMN "public"."t_sat_mock_exam_chapter_ability"."chapter_name" IS '章节名称，冗余存储便于查询';
COMMENT ON COLUMN "public"."t_sat_mock_exam_chapter_ability"."accuracy_rate" IS '章节答题正确率 = 答对题量/总题量';
COMMENT ON COLUMN "public"."t_sat_mock_exam_chapter_ability"."question_percentage" IS '该章节题量占整个考试的百分比';
COMMENT ON COLUMN "public"."t_sat_mock_exam_chapter_ability"."ability_level" IS '能力等级(0-7)，根据正确率按7等份计算：0=0%, 1=0-14.3%, 2=14.3-28.6%, ..., 7=85.7-100%';

-- 为章节能力表创建更新触发器
CREATE OR REPLACE FUNCTION update_mock_exam_chapter_ability_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_mock_exam_chapter_ability_updated_at
    BEFORE UPDATE ON t_sat_mock_exam_chapter_ability
    FOR EACH ROW
    EXECUTE FUNCTION update_mock_exam_chapter_ability_updated_at();

-- ============================================================================
-- 6. 创建自动更新时间戳的触发器
-- ============================================================================

-- 为模考会话表创建更新触发器
CREATE OR REPLACE FUNCTION update_mock_exam_session_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_mock_exam_session_updated_at
    BEFORE UPDATE ON t_sat_mock_exam_session
    FOR EACH ROW
    EXECUTE FUNCTION update_mock_exam_session_updated_at();

-- 为模考答题记录表创建更新触发器
CREATE OR REPLACE FUNCTION update_mock_exam_answer_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_mock_exam_answer_updated_at
    BEFORE UPDATE ON t_sat_mock_exam_answer
    FOR EACH ROW
    EXECUTE FUNCTION update_mock_exam_answer_updated_at();

-- 为模考模块进度表创建更新触发器
CREATE OR REPLACE FUNCTION update_mock_exam_module_progress_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_mock_exam_module_progress_updated_at
    BEFORE UPDATE ON t_sat_mock_exam_module_progress
    FOR EACH ROW
    EXECUTE FUNCTION update_mock_exam_module_progress_updated_at();

-- 为历史记录表创建更新触发器
CREATE OR REPLACE FUNCTION update_mock_exam_history_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_mock_exam_history_updated_at
    BEFORE UPDATE ON t_sat_mock_exam_history
    FOR EACH ROW
    EXECUTE FUNCTION update_mock_exam_history_updated_at();

-- ============================================================================
-- 7. 创建模考结果统计视图（能力统计库）
-- ============================================================================

CREATE OR REPLACE VIEW v_sat_mock_exam_summary AS
SELECT
    s.session_id,
    s.user_id,
    s.paper_id,
    s.exam_type,
    s.session_status,
    s.total_time_seconds,
    s.reading_time_seconds,
    s.math_time_seconds,
    s.created_at,
    s.completed_at,
    -- 总体统计
    COUNT(a.id) as total_questions,
    COUNT(CASE WHEN a.answer_status = 'answered' THEN 1 END) as answered_questions,
    COUNT(CASE WHEN a.is_correct = true THEN 1 END) as correct_questions,
    COUNT(CASE WHEN a.answer_status = 'skipped' THEN 1 END) as skipped_questions,
    -- 正确率
    CASE
        WHEN COUNT(CASE WHEN a.answer_status = 'answered' THEN 1 END) > 0
        THEN ROUND(COUNT(CASE WHEN a.is_correct = true THEN 1 END)::DECIMAL / COUNT(CASE WHEN a.answer_status = 'answered' THEN 1 END), 4)
        ELSE 0
    END as accuracy_rate,
    -- 平均答题时间
    ROUND(AVG(CASE WHEN a.response_time_seconds > 0 THEN a.response_time_seconds END), 2) as avg_response_time
FROM t_sat_mock_exam_session s
LEFT JOIN t_sat_mock_exam_answer a ON s.session_id = a.session_id
GROUP BY s.session_id, s.user_id, s.paper_id, s.exam_type, s.session_status,
         s.total_time_seconds, s.reading_time_seconds, s.math_time_seconds,
         s.created_at, s.completed_at;

COMMENT ON VIEW v_sat_mock_exam_summary IS 'SAT模考结果汇总视图，提供模考的整体统计信息';

-- 创建用户历史成绩视图
CREATE OR REPLACE VIEW v_sat_user_exam_history AS
SELECT
    h.user_id,
    h.session_id,
    h.paper_name,
    h.exam_type,
    h.total_score,
    h.reading_score,
    h.math_score,
    h.accuracy_rate,
    h.total_time_seconds,
    h.exam_status,
    h.completed_at,
    -- 排名信息（按总分）
    ROW_NUMBER() OVER (PARTITION BY h.user_id ORDER BY h.completed_at DESC) as exam_sequence,
    ROW_NUMBER() OVER (PARTITION BY h.user_id ORDER BY h.total_score DESC) as score_rank
FROM t_sat_mock_exam_history h
WHERE h.exam_status = 'completed'
ORDER BY h.user_id, h.completed_at DESC;

COMMENT ON VIEW v_sat_user_exam_history IS 'SAT用户考试历史视图，提供用户的历史成绩和排名信息';

-- ============================================================================
-- 8. 输出创建结果
-- ============================================================================

DO $$
BEGIN
    RAISE NOTICE '✅ SAT模考系统表创建完成';
    RAISE NOTICE '📋 已创建表:';
    RAISE NOTICE '   - t_sat_mock_exam_session (模考会话表/Session库)';
    RAISE NOTICE '   - t_sat_mock_exam_answer (模考答题记录表/答题结果库)';
    RAISE NOTICE '   - t_sat_mock_exam_module_progress (模考模块进度表/考试临时状态库)';
    RAISE NOTICE '   - t_sat_mock_exam_history (模考历史记录表/分数库)';
    RAISE NOTICE '   - t_sat_mock_exam_chapter_ability (模考章节能力统计表/能力统计库)';
    RAISE NOTICE '📊 已创建视图:';
    RAISE NOTICE '   - v_sat_mock_exam_summary (模考结果汇总视图/能力统计库)';
    RAISE NOTICE '   - v_sat_user_exam_history (用户考试历史视图)';
    RAISE NOTICE '🔧 已创建触发器用于自动更新时间戳';
    RAISE NOTICE '📈 已创建相关索引优化查询性能';
    RAISE NOTICE '🎯 模考系统基于现有的 t_sat_paper 和 t_sat_paper_question 表';
    RAISE NOTICE '🔄 支持考试状态恢复功能';
    RAISE NOTICE '📚 完整数据流：题库→组卷库→试卷库→API→按模块发题→记录session_id→session库→考试临时状态库→答题结果库→分数库→能力统计库';
END $$;

COMMIT;
