# SAT推荐系统基础配置
# 该文件定义了所有环境共享的基础配置结构和默认值
# 环境特定的配置将覆盖这些值

app:
  name: "sat-recommendation"
  namespace: "default"
  log_level: "info" # 这个没用
  max_threads: 10
  shutdown_timeout_secs: 30
  # API服务配置
  host: "0.0.0.0"
  port: 8000       # 开发环境使用8020端口
  cache_ttl_seconds: 3600
  # 以下配置已废弃，保留是为了向后兼容
  # service_host: "0.0.0.0"
  # service_port: 8000

# 开发环境日志配置
logging:
  level: "info"
  file_enabled: true
  file_path: "logs/dev-app.log"  # 不使用日期后缀，让系统自动添加
  format: "text"  # 开发环境使用文本格式便于直接阅读
  console_enabled: true
  # 开发环境调整日志级别
  component_levels:
    # 将API配置日志调整为debug级别
    api::config: "debug"
    # 将核心缓存服务日志调整为warn级别，减少噪音
    core::services::cache: "warn"
    # 将API路由日志调整为debug级别
    api::router: "debug"
    # 将知识服务日志调整为warn级别，减少缓存操作噪音
    core::services::knowledge: "warn"
    # 将Nacos服务发现日志调整为warn级别
    nacos_sdk: "warn"
    # SQL查询完全关闭，只显示错误
    sqlx::query: "error"
    # 将算法日志级别调整为info，减少注册日志
    recommendation_core.algorithms: "info"

databases:
  # 主PostgreSQL数据库
  main:
    type: "postgres"
    url: "postgres://postgres:postgres@localhost:5432/sat"
    host: "localhost"
    port: "5432"
    user: "postgres"
    password: "postgres"
    name: "sat"
    prefix: "dev_"
    max_connections: 10
    min_connections: 2
    connection_timeout: 30
    idle_timeout: 600
    statement_timeout: 30
    application_name: "sat_rec_dev"
    is_read_only: false
    enabled: true
  
  # 只读副本数据库（用于报表和查询）
  readonly:
    enabled: false
  
  # ElasticSearch配置
  elasticsearch:
    type: "elasticsearch"
    hosts: 
      - "http://test.es.knowbuddy.fun:19200"
    index_prefix: "sat_rec_dev_"
    elasticsearch_host: "test.es.knowbuddy.fun"
    elasticsearch_port: 19200
    request_timeout: 30
    retry_on_conflict: 3
    enabled: false

cache:
  # 测试环境Redis配置
  redis:
    type: "redis"
    url: "redis://localhost:6379"
    # url: "redis://:E3CTCB2fhci0N7mY@**************:16379"
    pool_size: 20
    ttl: 3600
    cache_ttl: 3600
    prefix: "sat_rec:"
    database: 2
    enabled: true

storage:
  # 对象存储配置 (S3兼容)
  object_store:
    type: "s3"
    endpoint: "http://localhost:9000"
    bucket: "sat-rec"
    access_key: "minioadmin"
    secret_key: "minioadmin"
    region: "us-east-1"
    s3_endpoint: "http://localhost:9000"
    s3_access_key: "minioadmin"
    s3_secret_key: "minioadmin"
    s3_region: "us-east-1"
    enabled: false

service_discovery:
  enabled: true
  nacos:
    server_addr: "http://**************:8848"
    namespace: "5fd42f7e-1526-438f-b8dd-eeda74d6b5b2"
    group: "DEFAULT_GROUP"
    cluster_name: "DEFAULT"
    service_name: "sat-rec"
    weight: 1.0
    ephemeral: true
    heartbeat_interval_secs: 5
    metadata:
      version: "1.0.0"
      preserved.register.source: "SPRING_CLOUD"
      type: "recommendation"
      description: "SAT推荐系统API服务"

# 告警系统配置
notifications:
  # 总开关，是否启用告警
  enabled: true
  # 是否允许记录错误到日志
  log_errors: true
  # 飞书告警配置
  feishu:
    # 是否启用飞书告警
    enabled: true
    # 飞书机器人Webhook URL
    webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/7a1a26a4-6030-4d86-8cd2-f5fe2aca4fc7"
    # 飞书机器人密钥（可选，如启用了签名验证则需提供）
    secret: ""
    # 飞书机器人关键词（可选，多个关键词用英文逗号分隔）
    keywords:
      - "错误"
      - "告警"
      - "SAT"
    # 消息标题前缀
    title_prefix: "SAT推荐系统"
    # 最小告警级别: info, warning, error, critical
    min_level: "info"
    # 告警超时（秒）
    timeout_secs: 10
    # 是否重试
    retry_enabled: true
    # 最大重试次数
    max_retries: 3
    # 重试间隔（毫秒）
    retry_interval_ms: 1000


business:
  # 推荐系统业务配置
  recommendation:
    max_recommendations: 10
    default_difficulty_range: 0.5
    popularity_weight: 0.3
    knowledge_coverage_weight: 0.7
    elo_rating_enabled: true
    irt_model_enabled: true
    default_elo: 1700
    k_factor: 32
    problem_k_factor: 32
    scale_factor: 100
    min_k_ratio: 0.5
    use_dynamic_k: true
    knowledge_elo_cache_ttl: 3600
    # 添加ELO算法缺失的参数
    min_rating: 900.0
    max_rating: 2500.0
    max_k_factor: 150.0
    # 添加题目难度相关参数
    initial_question_rating: 1700.0
    min_question_rating: 900.0
    max_question_rating: 2100.0
    # 默认题目状态过滤值
    default_status: 5
    # 学习阶段阈值配置 - 开发环境使用默认阶段划分
    stage_thresholds:
      # 冷启动阶段最大题数(0-5题)
      cold_start_max: 5
      # 过渡阶段I最大题数(6-10题)
      transition_i_max: 10
      # 过渡阶段II最大题数(11-20题)
      transition_ii_max: 20
      # 常规学习阶段最大题数(21-50题)
      regular_max: 50
      # 稳定学习阶段最大题数(51-200题)
      stable_max: 200
    default_algorithm: "adaptive_mastery"
    cold_start_threshold: 10
    cold_start_algorithm: "adaptive_mastery"
    algorithm_weights:
      ability_match: 0.7
      random: 0.3

  
  # 训练配置
  training:
    irt_batch_size: 100
    min_data_points: 30
    training_schedule: "0 0 * * *"
    auto_training_enabled: true 

# AI服务配置 - 开发环境
ai_services:
  # 开发环境启用故障转移
  fallback_enabled: true

  # 缓存配置
  cache_config:
    enabled: true
    ttl: 300  # 5分钟
    max_size: 1000
    similarity_threshold: 0.95

  # 负载均衡策略
  load_balancing: "Priority"

  # 提供商配置列表
  providers:
    # 内部微服务（优先级最高）
    - provider_type: "Microservice"
      enabled: true
      priority: 1
      timeout: 30
      retry_attempts: 3
      health_check_enabled: false  # 开发环境禁用健康检查
      health_check_interval: 60
      provider_config:
        type: "Microservice"
        service_url: "http://localhost:8001"
        api_version: "v1"

    # OpenAI（开发环境可选启用）
    - provider_type: "OpenAI"
      enabled: false  # 开发环境默认禁用，需要时手动启用
      priority: 2
      timeout: 30
      retry_attempts: 3
      health_check_enabled: false
      health_check_interval: 60
      provider_config:
        type: "OpenAI"
        api_key: "${OPENAI_API_KEY}"
        organization_id: "${OPENAI_ORG_ID}"
        base_url: "${OPENAI_BASE_URL}"
        default_model: "gpt-3.5-turbo"

    # Google Gemini（开发环境配置）
    - provider_type: "Gemini"
      enabled: true  # 开发环境启用Gemini
      priority: 3
      timeout: 30
      retry_attempts: 3
      health_check_enabled: false
      health_check_interval: 60
      provider_config:
        type: "Gemini"
        api_key: "AIzaSyAnyJNajZvc-wV-04KM2M8Wl7H45zcSpkc"
        project_id: ""  # 可选
        default_model: "gemini-pro"
        proxy_host: "127.0.0.1"  # Clash代理主机
        proxy_port: 7890         # Clash代理端口

# 外部服务配置
external_services:
  # 胜利连胜API服务
  win_streak_api:
    # 服务URL (替代host、port和base_path)
    service_url: "http://**************:31201/alphaTest"
    # 以下配置已废弃，保留是为了向后兼容
    # host: "localhost"
    # port: 8000
    # base_path: "/api"
    # 请求超时时间（秒）
    timeout_secs: 5
    # 请求重试次数
    retry_count: 3
    # 开发环境中启用
    enabled: true