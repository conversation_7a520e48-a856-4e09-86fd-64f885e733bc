[package]
name = "recommendation-tests"
version = "0.1.0"
edition = "2024"

[lib]
name = "recommendation_tests"
path = "lib.rs"

[dependencies]
recommendation_core = { path = "../core" }
recommendation_api = { path = "../api" }
mockall = { workspace = true }
async-trait = { workspace = true }
serde_json = { workspace = true }
chrono = { workspace = true }
tempfile = { workspace = true }
sea-orm = { workspace = true }
tokio = { workspace = true }
actix-web = { workspace = true } 