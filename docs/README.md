# SAT 智能推荐系统 - 文档中心

欢迎来到 SAT 智能推荐系统的文档中心！这里包含了系统的完整技术文档。

## 📚 文档导航

### 🚀 快速开始
- [项目概述](../readme.md) - 项目介绍和快速开始指南
- [项目结构](../PROJECT_STRUCTURE.md) - 文件组织和架构说明

### 🏗️ 系统架构
- [DDD 架构分析](architecture/推荐系统DDD架构分析.md) - 领域驱动设计架构
- [模块边界分析](architecture/module_boundaries.md) - 模块职责划分

### 📋 系统设计
- [推荐系统设计](recommendation_system_design.md) - 推荐算法设计
- [ELO-IRT 算法](elo_irt_algorithms.md) - 能力评估算法
- [数据库设计](design/记忆卡片系统数据库设计文档.md) - 数据模型设计

### 🔧 算法文档
- [ELO 评分系统](elo_rating_system.md) - ELO 算法实现
- [IRT 模型](irt_elo_fusion.md) - 项目反应理论
- [推荐算法融合](hybrid_recommender_algorithm.md) - 多算法融合策略
- [随机推荐算法](random_recommender.md) - 随机推荐实现

### 📡 API 文档
- [API 概览](api/) - API 接口总览
- [题库接口](api/question_bank_api_summary.md) - 题库管理 API
- [知识树接口](knowledge_api.md) - 知识体系 API

### 🎯 功能特性
- [用户分层系统](feature/user_segmentation.md) - 智能用户分类
- [学习计划系统](study_plan_system_design.md) - 个性化学习规划
- [能力评估系统](ability_model_design.md) - 多维度能力建模

### 🔄 重构记录
- [QuestionBank 重构](refactor/REFACTORING_SUMMARY.md) - 题库模块重构
- [DDD 重构总结](refactor/question_bank_ddd_refactor_summary.md) - 领域驱动重构
- [字段迁移记录](refactor/edit_status_to_status_migration_summary.md) - 数据库字段迁移
- [架构重构分析](architecture_refactoring_summary.md) - 架构演进记录

### 🚀 性能优化
- [性能优化计划](performance_optimization_plan.md) - 系统性能优化
- [内存过滤优化](memory_filtering_optimization.md) - 内存使用优化

### 🧪 测试文档
- [数据库测试](DATABASE_TESTS_GUIDE.md) - 数据库测试指南
- [API 测试](testing/test_question_bank_api.md) - API 接口测试

### 🐛 故障排除
- [错误处理](错误处理总结.md) - 错误处理机制
- [调试指南](bug_log/) - 调试技巧和工具

### 📊 改进计划
- [系统改进路线图](improvements/) - 功能发展规划

## 🔍 快速查找

### 按开发阶段
- **项目启动**: 快速开始 → 系统架构 → API 文档
- **功能开发**: 系统设计 → 算法文档 → 测试文档
- **性能调优**: 性能优化 → 故障排除 → 改进计划

### 按功能模块
- **推荐系统**: algorithms/ + recommendation_system_design.md
- **用户分层**: feature/user_segmentation.md
- **记忆卡片**: design/ + fsrs_*.md
- **学习计划**: study_plan_*.md

## 📖 文档规范

### 文档组织
- 按功能模块分类存放
- 使用清晰的文件命名
- 保持文档结构一致

### 文档格式
- 使用 Markdown 格式
- 添加适当的 emoji 图标
- 包含必要的代码示例

### 维护指南
- 新增功能时同步更新文档
- 定期检查文档的准确性
- 保持文档与代码的一致性

## 📁 文档整理历史

### ✅ 完成的整理工作

#### 📡 API 文档整理
- 移动散落的 API 文档到 `docs/api/` 目录
- 统一 API 文档格式和命名规范
- 创建 API 文档索引

#### 🔄 重构文档整理
- 整理重构相关文档到 `docs/refactor/` 目录
- 记录 DDD 重构过程和经验
- 保留重要的迁移记录

#### 🧪 测试文档整理
- 集中测试相关文档到 `docs/testing/` 目录
- 包含 API 测试、单元测试等文档
- 提供测试脚本和示例

### 📊 整理效果

1. **根目录清洁**：移除了 10+ 个散落的文档文件
2. **文档分类**：按功能模块重新组织文档结构
3. **导航优化**：提供了清晰的文档索引和快速查找
4. **可维护性**：统一的文档规范和组织方式

---

**最后更新**: 2025-07-18
**文档版本**: v2.0.0
**整理完成**: 根目录文档已重新组织，提升项目专业度和可维护性