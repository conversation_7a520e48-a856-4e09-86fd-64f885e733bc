# SAT 题目选择 API

## 接口概述

新增的 SAT 题目选择接口，用于从 `t_sat_question` 表中根据指定的小节ID选择题目。支持组卷功能：
- **统一返回策略**：无论请求多少题目，都只返回前10题（如果不足10题则返回实际数量）
- **会话机制**：生成session_id，剩余题目通过 `/sat/next` 接口分批获取

## 接口信息

- **URL**: `POST /api/v1/question-bank/generate`
- **功能**: 从数据库中选择 SAT 题目
- **数据源**: `t_sat_question` 表

## 请求参数

```json
{
    "section_ids": [1, 2, 3],           // 小节ID列表
    "generate_type": "random",          // 选择类型: "random" 或 "adaptive"
    "question_count": 22,               // 题目数量: 10, 22, 或 27
    "subject": "math"                   // 学科: "math" 或 "reading"
}
```

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| section_ids | array | 是 | 小节ID列表，用于过滤题目 |
| generate_type | string | 是 | 选择类型，支持 "random" 和 "adaptive" |
| question_count | integer | 是 | 题目数量，只允许 10、22、27 |
| subject | string | 是 | 学科，支持 "math" 和 "reading" |

### generate_type 说明

- **random**: 随机选择题目（已实现）
- **adaptive**: 自适应选择题目（待实现，目前会降级为随机选择）

> 注意：已移除 `personal` 模式，因为 SAT 题目选择不需要个性化模式

## 响应格式

```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "questions": [
            {
                "id": "12345",
                "content": {...},           // 题目内容 JSON
                "options": {...},           // 选项 JSON
                "answer": {...},            // 答案 JSON
                "explanation": {...},       // 解释 JSON
                "knowledge_id": 101,        // 知识点ID
                "subject_id": 1,           // 学科ID
                "type_id": 1,              // 题型ID
                "difficulty": 3,           // 难度等级
                "elo_rating": 1600.0       // ELO评分
            }
        ],
        "total_count": 22,                 // 实际返回的题目数量
        "generate_type": "random",         // 使用的选择类型
        "subject": "math"                  // 学科
    }
}
```

## 使用示例

### 1. 随机选择数学题目

```bash
curl -X POST "http://localhost:8000/api/v1/question-bank/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "section_ids": [1, 2, 3],
    "generate_type": "random",
    "question_count": 22,
    "subject": "math"
  }'
```

### 2. 选择阅读题目（10题，直接返回所有）

```bash
curl -X POST "http://localhost:8000/api/v1/question-bank/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "section_ids": [14, 15],
    "generate_type": "random",
    "question_count": 10,
    "subject": "reading"
  }'
```

### 3. 22题组卷（返回前10题 + session_id）

```bash
curl -X POST "http://localhost:8000/api/v1/question-bank/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "section_ids": [1, 2, 3],
    "generate_type": "random",
    "question_count": 22,
    "subject": "math"
  }'
```

## 错误处理

### 常见错误

1. **参数验证错误**
```json
{
    "code": 400,
    "msg": "题目数量必须是 10、22 或 27，当前值: 15"
}
```

2. **未找到题目**
```json
{
    "code": 404,
    "msg": "未找到任何题目"
}
```

3. **不支持的选择类型**
```json
{
    "code": 400,
    "msg": "SAT 题目选择不支持个性化模式"
}
```

## 实现状态

- ✅ **Random 模式**: 已完成，从指定小节随机选择题目
- 🚧 **Adaptive 模式**: 待实现，目前降级为随机选择
- ✅ **参数验证**: 完成题目数量和小节数量验证
- ✅ **错误处理**: 完整的错误处理机制

## 技术实现

### 架构层次
```
API Handler -> QuestionBankService -> QuestionApplicationService -> Storage
```

### 核心方法
- `QuestionBankService::get_question_bank_recommendations()`: 主要业务逻辑
- `QuestionApplicationService::get_questions_by_section()`: 数据获取
- 随机选择算法：使用 `fastrand::shuffle()` 进行随机打乱

## 后续计划

1. **实现 Adaptive 模式**: 基于用户能力和题目难度的智能选择
2. **性能优化**: 针对大量题目的选择进行优化
3. **缓存机制**: 对频繁查询的小节题目进行缓存
4. **统计功能**: 添加题目选择的统计和分析功能
