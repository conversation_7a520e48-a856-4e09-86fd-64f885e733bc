# SAT 后续题目获取接口文档

## 接口概述

新增的 SAT 后续题目获取接口，用于根据会话ID和当前题目ID获取后续的题目。配合 SAT 题目选择接口使用，实现分批获取题目的功能。

## 接口信息

- **URL**: `/api/v1/question-bank/next`
- **方法**: `POST`
- **Content-Type**: `application/json`

## 请求参数

```json
{
    "session_id": "string",           // 会话ID（必填）
    "current_question_id": "string",  // 当前题目ID（必填）
    "count": 10                       // 需要获取的题目数量（可选，默认10题）
}
```

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| session_id | string | 是 | 从 `/generate` 接口获取的会话ID |
| current_question_id | string | 是 | 当前正在做的题目ID |
| count | integer | 否 | 需要获取的题目数量，默认10题 |

## 响应格式

### 成功响应 (200)

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "session_id": "550e8400-e29b-41d4-a716-************",
        "questions": [
            {
                "id": "question_123",
                "content": {
                    "question_text": "题目内容...",
                    "options": ["A选项", "B选项", "C选项", "D选项"]
                },
                "options": ["A", "B", "C", "D"],
                "difficulty": 1200,
                "section_id": 1,
                "knowledge_point_id": 101
            }
        ],
        "current_count": 10,      // 本次返回的题目数量
        "remaining_count": 12,    // 剩余题目数量
        "has_more": true          // 是否还有更多题目
    }
}
```

### 错误响应

#### 会话不存在或已过期 (404)
```json
{
    "code": 404,
    "message": "会话 550e8400-e29b-41d4-a716-************ 不存在或已过期"
}
```

#### 题目ID不存在 (404)
```json
{
    "code": 404,
    "message": "题目ID question_123 在会话中不存在"
}
```

#### 参数验证失败 (400)
```json
{
    "code": 400,
    "message": "参数验证失败: session_id 不能为空"
}
```

## 使用示例

### 1. 获取后续10题（默认）

```bash
curl -X POST "http://localhost:8000/api/v1/question-bank/next" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "550e8400-e29b-41d4-a716-************",
    "current_question_id": "question_10"
  }'
```

### 2. 获取后续5题

```bash
curl -X POST "http://localhost:8000/api/v1/question-bank/next" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "550e8400-e29b-41d4-a716-************",
    "current_question_id": "question_15",
    "count": 5
  }'
```

## 业务逻辑

### 1. 会话验证
- 检查会话是否存在
- 检查会话是否已过期（默认24小时）

### 2. 题目定位
- 根据 `current_question_id` 在会话的题目列表中定位位置
- 返回该位置之后的指定数量题目

### 3. 响应构建
- `current_count`: 本次实际返回的题目数量
- `remaining_count`: 当前题目之后剩余的题目数量
- `has_more`: 是否还有更多题目可获取

## 配合使用流程

### 完整的22题组卷流程

1. **第一步**: 调用 `/generate` 接口选择22题
   ```json
   {
     "section_ids": [1, 2, 3],
     "generate_type": "random",
     "question_count": 22,
     "subject": "math"
   }
   ```
   
   响应: 返回前10题 + session_id

2. **第二步**: 学生完成前10题后，调用 `/next` 接口获取后续题目
   ```json
   {
     "session_id": "550e8400-e29b-41d4-a716-************",
     "current_question_id": "question_10"
   }
   ```
   
   响应: 返回第11-20题

3. **第三步**: 继续获取最后2题
   ```json
   {
     "session_id": "550e8400-e29b-41d4-a716-************",
     "current_question_id": "question_20"
   }
   ```
   
   响应: 返回第21-22题

## 注意事项

1. **会话过期**: 会话默认24小时后过期，过期后无法获取后续题目
2. **题目顺序**: 题目按照组卷时的顺序返回，保证考试的连续性
3. **边界处理**: 如果请求的题目数量超过剩余数量，返回所有剩余题目
4. **错误处理**: 如果当前题目ID不在会话中，返回404错误

## 数据库设计

会话信息存储在 `t_sat_question_bank_session` 表中：
- `session_id`: 会话唯一标识
- `question_ids`: JSON数组，存储所有题目ID的顺序
- `expires_at`: 会话过期时间
- 其他元数据字段...
