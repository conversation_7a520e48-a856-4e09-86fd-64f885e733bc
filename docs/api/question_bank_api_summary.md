# 题库接口总结

## ✅ 已完成的功能

### 1. 题目生成接口
- **路径**: `POST /api/v1/question-bank/generate`
- **功能**: 根据小节ID生成题目会话，返回前10题
- **特点**: 
  - 支持 Random/Adaptive 两种生成类型
  - 支持 Math/Reading 两种学科
  - 无论请求多少题目，都只返回前10题
  - 生成 session_id 用于后续获取

### 2. 后续题目接口
- **路径**: `POST /api/v1/question-bank/next`
- **功能**: 根据会话ID和当前题目ID获取后续题目
- **特点**:
  - 默认返回10题，可自定义数量
  - 支持分批获取剩余题目
  - 会话24小时自动过期

## 🏗️ 架构实现

### DDD 分层架构
```
├── Domain Layer (领域层)
│   ├── QuestionBankSession 实体 - 包含业务逻辑
│   ├── GenerateType/Subject 值对象
│   └── QuestionBankSessionRepository 仓储接口
│
├── Application Layer (应用层)  
│   ├── QuestionBankService - 业务协调
│   ├── SatPaperGenerateResponse/SatNextQuestionsResponse - DTO
│   └── 类型转换和验证逻辑
│
├── Infrastructure Layer (基础设施层)
│   ├── QuestionBankSessionRepositoryImpl - 仓储实现
│   ├── SeaORM 数据库模型
│   └── 依赖注入工厂
│
└── API Layer (接口层)
    ├── 请求/响应处理
    ├── 路由配置
    └── 错误处理
```

### 数据库设计
- **表名**: `t_sat_question_bank_session`
- **主要字段**:
  - `session_id` - 会话唯一标识
  - `question_ids` - JSON数组存储题目顺序
  - `generate_type` - 生成类型 (random/adaptive)
  - `subject` - 学科 (math/reading)
  - `section_ids` - 小节ID列表
  - `expires_at` - 过期时间

## 🔧 配置管理

### 环境感知配置
- **开发环境**: `status: Some(1)` - 获取状态为1的题目
- **生产环境**: `status: Some(5)` - 获取状态为5的题目
- **测试环境**: `status: Some(5)` - 获取状态为5的题目

### 自动配置选择
```rust
// 根据运行环境自动选择配置
match env {
    "production" => QuestionBankConfig::production(),
    "test" => QuestionBankConfig::test(),
    _ => QuestionBankConfig::development(),
}
```

## 📊 使用流程

### 典型的22题组卷流程
1. **生成会话**: 调用 `/generate` 接口
   - 请求22题，返回前10题 + session_id
   
2. **获取后续**: 调用 `/next` 接口
   - 传入 session_id 和当前题目ID
   - 返回接下来的10题
   
3. **继续获取**: 再次调用 `/next` 接口
   - 获取最后2题

### 数据流转
```
前端请求 → API层 → 应用服务 → 领域实体 → 仓储接口 → 数据库
                ↓
前端响应 ← API层 ← 应用服务 ← 领域实体 ← 仓储实现 ← 数据库
```

## 🎯 核心特性

1. **统一返回策略**: 所有情况都只返回前10题
2. **会话管理**: 完整的会话生命周期管理
3. **分批获取**: 支持灵活的分批题目获取
4. **环境配置**: 根据环境自动选择题目状态过滤
5. **类型安全**: 强类型的领域模型和值对象
6. **错误处理**: 完善的错误处理和响应机制

## 📝 API 示例

### 生成题目会话
```bash
curl -X POST "http://localhost:8000/api/v1/question-bank/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "section_ids": [1, 2, 3],
    "generate_type": "random",
    "question_count": 22,
    "subject": "math"
  }'
```

### 获取后续题目
```bash
curl -X POST "http://localhost:8000/api/v1/question-bank/next" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "550e8400-e29b-41d4-a716-************",
    "current_question_id": "question_10"
  }'
```

## 🚀 技术栈

- **后端框架**: Rust + Actix-web
- **数据库**: PostgreSQL + SeaORM
- **架构模式**: DDD (领域驱动设计)
- **依赖注入**: 自定义工厂模式
- **序列化**: Serde
- **日志**: tracing

这个实现完全符合 DDD 架构原则，具有良好的可维护性和扩展性！
