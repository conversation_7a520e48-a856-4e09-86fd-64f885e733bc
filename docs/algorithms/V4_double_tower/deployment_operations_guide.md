# 双塔模型部署与运维指南

## 1. 部署架构

### 1.1 整体部署架构图

```
                    ┌─────────────────┐
                    │   Load Balancer │
                    └─────────┬───────┘
                              │
                    ┌─────────▼───────┐
                    │   API Gateway   │
                    └─────────┬───────┘
                              │
        ┌─────────────────────┼─────────────────────┐
        │                     │                     │
┌───────▼────────┐  ┌─────────▼────────┐  ┌────────▼────────┐
│ Recommendation │  │  Feature Service │  │  Model Service  │
│    Service     │  │                  │  │                 │
└───────┬────────┘  └─────────┬────────┘  └────────┬────────┘
        │                     │                     │
        └─────────────────────┼─────────────────────┘
                              │
        ┌─────────────────────┼─────────────────────┐
        │                     │                     │
┌───────▼────────┐  ┌─────────▼────────┐  ┌────────▼────────┐
│     Redis      │  │   PostgreSQL     │  │   Model Store   │
│    (Cache)     │  │   (Features)     │  │   (Artifacts)   │
└────────────────┘  └──────────────────┘  └─────────────────┘
```

### 1.2 服务组件说明

#### 1.2.1 推荐服务 (Recommendation Service)
- **职责**: 处理推荐请求，协调各组件
- **实例数**: 3-5个实例
- **资源配置**: 4 CPU, 8GB RAM
- **端口**: 8000

#### 1.2.2 特征服务 (Feature Service)
- **职责**: 特征提取、缓存管理
- **实例数**: 2-3个实例
- **资源配置**: 2 CPU, 4GB RAM
- **端口**: 8081

#### 1.2.3 模型服务 (Model Service)
- **职责**: 双塔模型推理
- **实例数**: 2-4个实例 (根据GPU资源)
- **资源配置**: 4 CPU, 8GB RAM, 1 GPU (可选)
- **端口**: 8082

## 2. 环境配置

### 2.1 开发环境

#### 2.1.1 Docker Compose 配置
```yaml
version: '3.8'
services:
  recommendation-service:
    build: 
      context: .
      dockerfile: Dockerfile.recommendation
    ports:
      - "8000:8000"
    environment:
      - RUST_ENV=development
      - DATABASE_URL=************************************/recommendation_dev
      - REDIS_URL=redis://redis:6379
      - MODEL_SERVICE_URL=http://model-service:8082
    depends_on:
      - postgres
      - redis
      - model-service

  feature-service:
    build:
      context: .
      dockerfile: Dockerfile.feature
    ports:
      - "8081:8081"
    environment:
      - RUST_ENV=development
      - DATABASE_URL=************************************/recommendation_dev
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis

  model-service:
    build:
      context: .
      dockerfile: Dockerfile.model
    ports:
      - "8082:8082"
    environment:
      - RUST_ENV=development
      - MODEL_PATH=/models/two_tower_model.onnx
    volumes:
      - ./models:/models
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: recommendation_dev
      POSTGRES_USER: user
      POSTGRES_PASSWORD: pass
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

#### 2.1.2 环境变量配置
```bash
# .env.development
RUST_ENV=development
RUST_LOG=debug

# 数据库配置
DATABASE_URL=postgresql://user:pass@localhost:5432/recommendation_dev
DATABASE_POOL_SIZE=10
DATABASE_TIMEOUT=30

# Redis配置
REDIS_URL=redis://localhost:6379
REDIS_POOL_SIZE=10
REDIS_TIMEOUT=5

# 模型配置
MODEL_PATH=/models/two_tower_model.onnx
MODEL_BATCH_SIZE=32
MODEL_TIMEOUT=1000

# 特征配置
FEATURE_CACHE_TTL=3600
FEATURE_BATCH_SIZE=100
ENABLE_FEATURE_CACHE=true

# 融合策略配置
TOWER_WEIGHT=0.7
ELO_WEIGHT=0.3
CONSISTENCY_THRESHOLD=0.3
```

### 2.2 生产环境

#### 2.2.1 Kubernetes 部署配置
```yaml
# recommendation-service-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: recommendation-service
  labels:
    app: recommendation-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: recommendation-service
  template:
    metadata:
      labels:
        app: recommendation-service
    spec:
      containers:
      - name: recommendation-service
        image: recommendation-system/recommendation-service:latest
        ports:
        - containerPort: 8000
        env:
        - name: RUST_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: redis-url
        resources:
          requests:
            memory: "4Gi"
            cpu: "2"
          limits:
            memory: "8Gi"
            cpu: "4"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: recommendation-service
spec:
  selector:
    app: recommendation-service
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8000
  type: ClusterIP
```

#### 2.2.2 模型服务 GPU 部署
```yaml
# model-service-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: model-service
  labels:
    app: model-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: model-service
  template:
    metadata:
      labels:
        app: model-service
    spec:
      containers:
      - name: model-service
        image: recommendation-system/model-service:latest
        ports:
        - containerPort: 8082
        env:
        - name: RUST_ENV
          value: "production"
        - name: MODEL_PATH
          value: "/models/two_tower_model.onnx"
        resources:
          requests:
            memory: "4Gi"
            cpu: "2"
            nvidia.com/gpu: 1
          limits:
            memory: "8Gi"
            cpu: "4"
            nvidia.com/gpu: 1
        volumeMounts:
        - name: model-storage
          mountPath: /models
      volumes:
      - name: model-storage
        persistentVolumeClaim:
          claimName: model-pvc
      nodeSelector:
        accelerator: nvidia-tesla-v100
```

## 3. 监控配置

### 3.1 Prometheus 监控配置

#### 3.1.1 指标定义
```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "recommendation_rules.yml"

scrape_configs:
  - job_name: 'recommendation-service'
    static_configs:
      - targets: ['recommendation-service:8000']
    metrics_path: /metrics
    scrape_interval: 10s

  - job_name: 'feature-service'
    static_configs:
      - targets: ['feature-service:8081']
    metrics_path: /metrics
    scrape_interval: 10s

  - job_name: 'model-service'
    static_configs:
      - targets: ['model-service:8082']
    metrics_path: /metrics
    scrape_interval: 10s

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

#### 3.1.2 告警规则
```yaml
# recommendation_rules.yml
groups:
- name: recommendation_system
  rules:
  # 高延迟告警
  - alert: HighRecommendationLatency
    expr: histogram_quantile(0.95, recommendation_request_duration_seconds) > 0.1
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "推荐服务延迟过高"
      description: "95%分位延迟超过100ms，当前值: {{ $value }}s"

  # 错误率告警
  - alert: HighErrorRate
    expr: rate(recommendation_errors_total[5m]) > 0.01
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "推荐服务错误率过高"
      description: "错误率超过1%，当前值: {{ $value }}"

  # 缓存命中率告警
  - alert: LowCacheHitRate
    expr: feature_cache_hit_rate < 0.8
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "特征缓存命中率过低"
      description: "缓存命中率低于80%，当前值: {{ $value }}"

  # GPU使用率告警
  - alert: HighGPUUsage
    expr: nvidia_gpu_utilization > 90
    for: 3m
    labels:
      severity: warning
    annotations:
      summary: "GPU使用率过高"
      description: "GPU使用率超过90%，当前值: {{ $value }}%"

  # 模型一致性告警
  - alert: ModelInconsistency
    expr: model_elo_consistency_score < 0.7
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "模型预测一致性过低"
      description: "双塔模型与ELO预测一致性低于70%，当前值: {{ $value }}"
```

### 3.2 Grafana 仪表板

#### 3.2.1 核心指标仪表板
```json
{
  "dashboard": {
    "title": "推荐系统核心指标",
    "panels": [
      {
        "title": "请求QPS",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(recommendation_requests_total[1m])",
            "legendFormat": "QPS"
          }
        ]
      },
      {
        "title": "延迟分布",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.50, recommendation_request_duration_seconds)",
            "legendFormat": "P50"
          },
          {
            "expr": "histogram_quantile(0.95, recommendation_request_duration_seconds)",
            "legendFormat": "P95"
          },
          {
            "expr": "histogram_quantile(0.99, recommendation_request_duration_seconds)",
            "legendFormat": "P99"
          }
        ]
      },
      {
        "title": "错误率",
        "type": "singlestat",
        "targets": [
          {
            "expr": "rate(recommendation_errors_total[5m]) * 100",
            "legendFormat": "错误率 (%)"
          }
        ]
      },
      {
        "title": "缓存命中率",
        "type": "singlestat",
        "targets": [
          {
            "expr": "feature_cache_hit_rate * 100",
            "legendFormat": "命中率 (%)"
          }
        ]
      }
    ]
  }
}
```

## 4. 运维操作手册

### 4.1 日常运维检查清单

#### 4.1.1 每日检查项目
```bash
#!/bin/bash
# daily_health_check.sh

echo "=== 推荐系统日常健康检查 ==="
echo "检查时间: $(date)"

# 1. 服务状态检查
echo "1. 检查服务状态..."
kubectl get pods -l app=recommendation-service
kubectl get pods -l app=feature-service
kubectl get pods -l app=model-service

# 2. 资源使用检查
echo "2. 检查资源使用..."
kubectl top pods -l app=recommendation-service
kubectl top pods -l app=feature-service
kubectl top pods -l app=model-service

# 3. 错误日志检查
echo "3. 检查错误日志..."
kubectl logs -l app=recommendation-service --since=24h | grep -i error | wc -l
kubectl logs -l app=feature-service --since=24h | grep -i error | wc -l
kubectl logs -l app=model-service --since=24h | grep -i error | wc -l

# 4. 性能指标检查
echo "4. 检查性能指标..."
curl -s "http://prometheus:9090/api/v1/query?query=rate(recommendation_requests_total[5m])" | jq '.data.result[0].value[1]'
curl -s "http://prometheus:9090/api/v1/query?query=histogram_quantile(0.95,recommendation_request_duration_seconds)" | jq '.data.result[0].value[1]'

# 5. 数据库连接检查
echo "5. 检查数据库连接..."
kubectl exec -it deployment/recommendation-service -- curl -f http://localhost:8000/health/db

# 6. 缓存状态检查
echo "6. 检查缓存状态..."
kubectl exec -it deployment/feature-service -- redis-cli ping

echo "=== 检查完成 ==="
```

#### 4.1.2 每周检查项目
```bash
#!/bin/bash
# weekly_maintenance.sh

echo "=== 推荐系统每周维护 ==="

# 1. 模型性能评估
echo "1. 模型性能评估..."
python scripts/evaluate_model_performance.py --days=7

# 2. 特征分布检查
echo "2. 特征分布检查..."
python scripts/check_feature_distribution.py --days=7

# 3. A/B测试结果分析
echo "3. A/B测试结果分析..."
python scripts/analyze_ab_test_results.py

# 4. 数据库性能优化
echo "4. 数据库性能优化..."
kubectl exec -it postgres-0 -- psql -c "ANALYZE;"
kubectl exec -it postgres-0 -- psql -c "VACUUM;"

# 5. 缓存清理
echo "5. 缓存清理..."
kubectl exec -it redis-0 -- redis-cli FLUSHDB

# 6. 日志归档
echo "6. 日志归档..."
kubectl logs deployment/recommendation-service --since=168h > logs/recommendation-$(date +%Y%m%d).log
kubectl logs deployment/feature-service --since=168h > logs/feature-$(date +%Y%m%d).log
kubectl logs deployment/model-service --since=168h > logs/model-$(date +%Y%m%d).log

echo "=== 维护完成 ==="
```

### 4.2 故障处理流程

#### 4.2.1 服务不可用处理
```bash
# 1. 快速诊断
kubectl get pods -l app=recommendation-service
kubectl describe pod <pod-name>
kubectl logs <pod-name> --tail=100

# 2. 重启服务
kubectl rollout restart deployment/recommendation-service

# 3. 检查依赖服务
kubectl get pods -l app=postgres
kubectl get pods -l app=redis

# 4. 降级到ELO系统
kubectl patch configmap recommendation-config -p '{"data":{"ENABLE_TWO_TOWER":"false"}}'
kubectl rollout restart deployment/recommendation-service
```

#### 4.2.2 性能问题处理
```bash
# 1. 检查资源使用
kubectl top pods
kubectl top nodes

# 2. 扩容服务
kubectl scale deployment recommendation-service --replicas=5

# 3. 检查缓存状态
kubectl exec -it redis-0 -- redis-cli info memory
kubectl exec -it redis-0 -- redis-cli info stats

# 4. 优化数据库查询
kubectl exec -it postgres-0 -- psql -c "SELECT * FROM pg_stat_activity WHERE state = 'active';"
```

### 4.3 模型更新流程

#### 4.3.1 模型版本管理
```bash
#!/bin/bash
# model_deployment.sh

MODEL_VERSION=$1
if [ -z "$MODEL_VERSION" ]; then
    echo "Usage: $0 <model_version>"
    exit 1
fi

echo "部署模型版本: $MODEL_VERSION"

# 1. 下载新模型
aws s3 cp s3://model-artifacts/two_tower_model_${MODEL_VERSION}.onnx /tmp/

# 2. 验证模型
python scripts/validate_model.py /tmp/two_tower_model_${MODEL_VERSION}.onnx

# 3. 创建新的ConfigMap
kubectl create configmap model-config-${MODEL_VERSION} \
    --from-file=/tmp/two_tower_model_${MODEL_VERSION}.onnx

# 4. 更新部署配置
kubectl patch deployment model-service -p \
    '{"spec":{"template":{"spec":{"volumes":[{"name":"model-storage","configMap":{"name":"model-config-'${MODEL_VERSION}'"}}]}}}}'

# 5. 滚动更新
kubectl rollout status deployment/model-service

# 6. 验证新模型
curl -X POST http://model-service:8082/predict \
    -H "Content-Type: application/json" \
    -d '{"user_features": {...}, "question_features": {...}}'

echo "模型部署完成"
```

#### 4.3.2 A/B测试部署
```bash
#!/bin/bash
# ab_test_deployment.sh

EXPERIMENT_ID=$1
TRAFFIC_RATIO=$2

echo "启动A/B测试: $EXPERIMENT_ID, 流量比例: $TRAFFIC_RATIO"

# 1. 创建实验配置
kubectl create configmap ab-test-config \
    --from-literal=experiment_id=$EXPERIMENT_ID \
    --from-literal=traffic_ratio=$TRAFFIC_RATIO \
    --from-literal=enable_ab_test=true

# 2. 更新服务配置
kubectl patch deployment recommendation-service -p \
    '{"spec":{"template":{"spec":{"containers":[{"name":"recommendation-service","envFrom":[{"configMapRef":{"name":"ab-test-config"}}]}]}}}}'

# 3. 监控实验指标
python scripts/monitor_ab_test.py --experiment_id=$EXPERIMENT_ID

echo "A/B测试启动完成"
```
