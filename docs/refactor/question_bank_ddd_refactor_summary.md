# QuestionBank DDD重构总结

## 重构概述

基于你的建议，我们将QuestionBank功能整合到现有的Question领域模块中，而不是创建独立的question_bank领域。这样做更符合DDD原则，因为QuestionBank本质上是Question的一个应用场景。

## 重构内容

### 1. 领域层扩展 (Domain Layer)

#### 新增值对象 (`core/src/domain/question/value_objects.rs`)
- `GenerateType`: 题库生成类型（随机/个性化）
- `Subject`: 学科类型（数学/阅读）
- `QuestionCount`: 题目数量值对象，包含业务验证
- `QuestionBankConfig`: 题库配置，与推荐系统保持一致
- `UserContext`: 用户上下文，支持个性化生成

#### 新增实体 (`core/src/domain/question/entity.rs`)
- `QuestionBank`: 题库聚合根实体
- `QuestionBankRequest`: 题库生成请求实体
- 扩展了原有的`Question`实体，添加了业务方法

#### 新增仓储接口 (`core/src/domain/question/repository.rs`)
- `QuestionBankRepository`: 题库数据访问接口
- `QuestionProvider`: 题目提供者接口，解耦题库生成与存储
- `UserContextProvider`: 用户上下文提供者接口

#### 新增领域服务 (`core/src/domain/question/service.rs`)
- `QuestionDomainService`: 包含题库生成的核心业务逻辑
  - 随机题库生成
  - 个性化题库生成
  - 业务规则验证
  - 降级策略处理

### 2. 应用层重构 (Application Layer)

#### 重构应用服务 (`core/src/application/question/question_bank_service.rs`)
- 移除了直接的存储依赖
- 现在依赖领域服务`QuestionDomainService`
- 专注于DTO转换和协调工作
- 简化了业务逻辑，将其移至领域层

#### 更新DTO (`core/src/application/question/question_bank_dto.rs`)
- 移除了`QuestionBankGenerateConfig`（已移至领域层）
- 保留了API层需要的DTO结构

### 3. 配置优化

#### 题库配置统一
- 将字段名从`edit_status`改为`status`，与推荐系统保持一致
- 默认`status`从5改为1，与development环境推荐系统保持一致
- 移除了分页逻辑，直接获取所有题目
- 需要实现基于`status`字段的题目过滤逻辑

## 架构优势

### 1. 清晰的职责分离
- **领域层**: 包含题库生成的核心业务逻辑和规则
- **应用层**: 协调领域服务，处理DTO转换
- **基础设施层**: 提供数据访问实现

### 2. 低耦合高内聚
- 应用层不再直接依赖存储层
- 通过接口依赖，易于测试和扩展
- 业务逻辑集中在领域服务中

### 3. 与现有架构一致
- 遵循Plan模块的DDD架构模式
- 保持整个项目的架构一致性
- 复用现有的Question领域概念

### 4. 可扩展性
- 新的生成策略可以在领域服务中扩展
- 新的题目提供者可以通过接口实现
- 支持个性化推荐的扩展

## 主要改进

### 1. 业务逻辑集中化
- 将题库生成逻辑从应用层移至领域层
- 统一了业务规则验证
- 提供了清晰的降级策略

### 2. 配置管理优化
- 与推荐系统配置保持一致
- 移除了硬编码的分页逻辑
- 支持环境相关的配置

### 3. 类型安全
- 使用值对象确保数据有效性
- 强类型的题目数量验证
- 清晰的生成类型定义

### 4. 错误处理改进
- 统一的错误处理机制
- 详细的错误信息
- 支持降级策略

## 使用示例

```rust
// 创建领域服务
let domain_service = QuestionDomainService::new(
    question_provider,
    user_context_provider,
    QuestionBankConfig::development()
);

// 创建应用服务
let app_service = QuestionBankService::new(Arc::new(domain_service));

// 生成题库
let request = QuestionBankGenerateRequestDto {
    section_ids: vec![1, 2, 3],
    generate_type: QuestionBankGenerateType::Random,
    question_count: 10,
    subject: SubjectType::Math,
};

let response = app_service.generate_question_bank(request).await?;
```

## 后续工作

1. **实现基础设施层**: 创建`QuestionProvider`和`UserContextProvider`的具体实现
2. **集成测试**: 编写端到端测试验证重构效果
3. **性能优化**: 根据实际使用情况优化查询性能
4. **文档更新**: 更新API文档和使用指南

## 总结

这次重构成功地将QuestionBank功能整合到Question领域中，遵循了DDD原则，提高了代码的可维护性和可扩展性。通过清晰的分层架构，我们实现了业务逻辑与技术实现的分离，为后续的功能扩展奠定了良好的基础。
