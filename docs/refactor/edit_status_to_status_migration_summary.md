# edit_status 到 status 字段迁移总结

## 迁移概述

根据用户要求，将所有代码中的 `edit_status` 字段改为 `status` 字段，以与推荐系统的配置保持一致。这个迁移涉及多个层次的代码修改。

## 修改内容

### 1. 数据库模型层 (Database Models)

#### 修改文件: `core/src/infrastructure/database/models/question.rs`
- **方法名修改**: `find_by_section_id_and_edit_status` → `find_by_section_id_and_status`
- **参数类型修改**: `edit_status: i16` → `status: i32`
- **字段引用修改**: `Column::EditStatus` → `Column::Status`

```rust
// 修改前
pub fn find_by_section_id_and_edit_status(section_id: i32, edit_status: i16) -> Select<Entity> {
    Self::find()
        .filter(Column::SectionId.eq(section_id))
        .filter(Column::EditStatus.eq(edit_status))
        .filter(Column::IsActive.eq(true))
}

// 修改后
pub fn find_by_section_id_and_status(section_id: i32, status: i32) -> Select<Entity> {
    Self::find()
        .filter(Column::SectionId.eq(section_id))
        .filter(Column::Status.eq(status))
        .filter(Column::IsActive.eq(true))
}
```

### 2. 存储层接口 (Storage Traits)

#### 修改文件: `core/src/storage/traits/question.rs`
- **方法名修改**: 
  - `get_questions_by_section_id_and_edit_status` → `get_questions_by_section_id_and_status`
  - `get_questions_by_section_id_and_edit_status_with_pagination` → `get_questions_by_section_id_and_status_with_pagination`
- **参数类型修改**: `edit_status: i16` → `status: i32`
- **文档注释更新**: "编辑状态" → "题目状态"

### 3. 存储层实现 (Storage Implementation)

#### 修改文件: `core/src/infrastructure/persistence/storage_impl/question_storage.rs`
- **方法实现修改**: 更新方法名和参数类型
- **调用修改**: 使用新的数据库模型方法名

```rust
// 修改前
async fn get_questions_by_section_id_and_edit_status(
    &self,
    section_id: i32,
    edit_status: i16,
) -> Result<Vec<QuestionContent>> {
    let models = entities::question::Entity::find_by_section_id_and_edit_status(section_id, edit_status)
        .all(&self.db)
        .await
        .map_err(|e| Error::storage(format!("获取小节特定状态题目失败: {}", e)))?;

    Ok(models.iter().map(Self::to_content_dto).collect())
}

// 修改后
async fn get_questions_by_section_id_and_status(
    &self,
    section_id: i32,
    status: i32,
) -> Result<Vec<QuestionContent>> {
    let models = entities::question::Entity::find_by_section_id_and_status(section_id, status)
        .all(&self.db)
        .await
        .map_err(|e| Error::storage(format!("获取小节特定状态题目失败: {}", e)))?;

    Ok(models.iter().map(Self::to_content_dto).collect())
}
```

### 4. 应用层服务 (Application Services)

#### 修改文件: `core/src/application/question/service.rs`
- **trait方法签名修改**: 参数名和类型更新
- **文档注释更新**: "编辑状态" → "题目状态"

#### 修改文件: `core/src/application/question/service_impl.rs`
- **方法实现修改**: 参数名和调用更新
- **日志信息更新**: 使用新的参数名

### 5. API层 (API Layer)

#### 修改文件: `api/src/handlers/question/status_questions.rs`
- **变量名修改**: `edit_status` → `status`
- **API文档更新**: 参数描述从"编辑状态值"改为"题目状态值"
- **参数类型更新**: `i16` → `i32`

#### 修改文件: `api/src/handlers/question/models.rs`
- **字段类型修改**: `pub status: i16` → `pub status: i32`
- **字段注释更新**: "编辑状态" → "题目状态"

### 6. 领域层配置 (Domain Configuration)

#### 修改文件: `core/src/domain/question/value_objects.rs`
- **配置字段修改**: `edit_status: Option<i32>` → `status: Option<i32>`
- **注释更新**: "编辑状态过滤" → "题目状态过滤"

## 类型统一

### 数据类型对齐
- **数据库字段**: 
  - `edit_status: i16` (编辑状态字段，保留)
  - `status: i32` (题目状态字段，推荐系统使用)
- **应用层统一使用**: `status: i32`

### 配置对齐
- **推荐系统配置**: `default_status: i32`
- **题库配置**: `status: Option<i32>`
- **环境配置**:
  - development: `status: 1`
  - test: `status: 5`
  - production: `status: 5`

## 架构改进

### 1. DDD架构完善
- 在领域层添加了题库相关的值对象和配置
- 保持了与推荐系统配置的一致性
- 实现了清晰的分层架构

### 2. API层适配
- 完整保留了题库生成功能
- 实现了临时的QuestionProvider和UserContextProvider
- 修复了所有类型不匹配问题

### 3. 编译验证
- ✅ core包编译通过
- ✅ api包编译通过
- ✅ 所有类型检查通过

## 测试建议

1. **单元测试**: 验证新的方法签名和参数类型
2. **集成测试**: 测试题库生成功能是否正常工作
3. **API测试**: 验证status参数的正确传递和处理
4. **配置测试**: 确认不同环境下的status值配置正确

## 后续工作

1. **完善QuestionProvider实现**: 将临时实现替换为完整的实现
2. **添加用户上下文支持**: 实现真正的个性化题库生成
3. **性能优化**: 根据实际使用情况优化查询性能
4. **文档更新**: 更新相关的API文档和使用指南

## 总结

本次迁移成功地将所有 `edit_status` 相关的代码修改为使用 `status` 字段，实现了：

- ✅ 与推荐系统配置的完全对齐
- ✅ 类型系统的统一 (i32)
- ✅ 清晰的分层架构
- ✅ 完整的功能保留
- ✅ 编译验证通过

这个迁移为后续的功能扩展和系统集成奠定了良好的基础。
