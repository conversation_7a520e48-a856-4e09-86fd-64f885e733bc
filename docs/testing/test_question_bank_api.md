# 题库接口测试文档

## 题库知识树接口

基于DDD设计模式的题库知识树接口，用于获取出题时的知识结构选择。

## 接口端点

### 主要题库知识树接口
```
GET /api/v1/question-bank/knowledge-tree?max_depth=4&subject_id=1&exclude_test_subjects=true
```

这是唯一的接口端点，支持所有查询参数：
- `max_depth`: 最大深度（1-4，默认4）
- `subject_id`: 学科ID（可选，不传则返回所有学科）
- `exclude_test_subjects`: 是否排除测试学科（默认true）

## 架构设计

### 领域层 (Domain Layer)
- **实体**: `Subject`, `Chapter`, `Section`, `KnowledgePoint`
- **值对象**: `KnowledgeTreeQuery`, `KnowledgeTreeDepth`
- **仓储接口**: `KnowledgeRepository`
- **领域服务**: `KnowledgeDomainService`

### 应用层 (Application Layer)
- **应用服务**: `KnowledgeApplicationService`
- **DTO**: `KnowledgeTreeResponseDto`, `SubjectDto`, `ChapterDto`, `SectionDto`, `KnowledgePointDto`

### 基础设施层 (Infrastructure Layer)
- **仓储实现**: `KnowledgeRepositoryImpl`
- **工厂方法**: `knowledge_factory`

### API层 (API Layer)
- **处理器**: `question_bank.rs`
- **路由器**: `QuestionBankRouter`

## 主要特性

1. **题库专用**: 专门为题库出题场景设计
2. **DDD架构**: 严格遵循领域驱动设计原则
3. **依赖注入**: 使用工厂模式管理依赖
4. **类型安全**: 完整的类型转换和验证
5. **性能监控**: 内置请求耗时统计
6. **错误处理**: 统一的错误处理机制
7. **选择状态**: 每个section包含is_selected字段，默认为true

## 数据结构特点

### Section字段说明
- `id`: 小节ID
- `chapter_id`: 章节ID
- `name`: 小节名称
- `code`: 小节代码
- `description`: 小节描述
- `is_selected`: 是否选中（默认true，用于出题选择）
- `knowledge_points`: 知识点列表

## 测试方法

启动服务器后，可以使用以下curl命令测试：

```bash
# 测试基本题库知识树接口（所有学科，完整深度）
curl "http://localhost:8000/api/v1/question-bank/knowledge-tree?max_depth=4"

# 测试指定学科（只返回数学学科）
curl "http://localhost:8000/api/v1/question-bank/knowledge-tree?subject_id=1&max_depth=3"

# 测试只到章节级别（深度2）
curl "http://localhost:8000/api/v1/question-bank/knowledge-tree?max_depth=2"

# 测试只到小节级别（深度3）
curl "http://localhost:8000/api/v1/question-bank/knowledge-tree?max_depth=3"
```
