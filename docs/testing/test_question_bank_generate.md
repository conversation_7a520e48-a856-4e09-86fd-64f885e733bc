# 题库生成接口测试

## 接口信息
- **URL**: `POST /api/v1/question-bank/generate`
- **Content-Type**: `application/json`

## 请求参数

```json
{
  "section_ids": [1, 2, 3],
  "generate_type": "random",
  "question_count": 10,
  "subject": "math"
}
```

### 参数说明
- `section_ids`: 小节ID列表（必填）
- `generate_type`: 生成类型，支持 `random` 和 `personal`（必填）
- `question_count`: 题目数量，支持 `10`、`22`、`27`（必填）
- `subject`: 学科类型，支持 `math` 和 `reading`（必填）

## 响应示例

### 成功响应
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "questions": [
      {
        "id": 1001,
        "content": {...},
        "options": {...},
        "answer": {...},
        "explanation": {...},
        "knowledge_id": 101,
        "subject_id": 1,
        "type_id": 1,
        "difficulty": 3,
        "elo_rating": 1500.0
      }
    ],
    "total_count": 10,
    "generate_type": "random",
    "subject": "math"
  }
}
```

### 错误响应
```json
{
  "code": 400,
  "msg": "请求参数错误",
  "data": null
}
```

## 测试用例

### 1. 正常请求
```bash
curl -X POST http://localhost:8000/api/v1/question-bank/generate \
  -H "Content-Type: application/json" \
  -d '{
    "section_ids": [1, 2],
    "generate_type": "random",
    "question_count": 10,
    "subject": "math"
  }'
```

### 2. 参数验证 - 空section_ids
```bash
curl -X POST http://localhost:8000/api/v1/question-bank/generate \
  -H "Content-Type: application/json" \
  -d '{
    "section_ids": [],
    "generate_type": "random",
    "question_count": 10,
    "subject": "math"
  }'
```

### 3. 参数验证 - 无效question_count
```bash
curl -X POST http://localhost:8000/api/v1/question-bank/generate \
  -H "Content-Type: application/json" \
  -d '{
    "section_ids": [1, 2],
    "generate_type": "random",
    "question_count": 15,
    "subject": "math"
  }'
```

### 4. 个性化推荐（暂未实现）
```bash
curl -X POST http://localhost:8000/api/v1/question-bank/generate \
  -H "Content-Type: application/json" \
  -d '{
    "section_ids": [1, 2],
    "generate_type": "personal",
    "question_count": 10,
    "subject": "math"
  }'
```

## 实现特点

1. **随机选择**: 从指定的section_ids中随机选择题目
2. **参数验证**: 对所有输入参数进行严格验证
3. **错误处理**: 提供详细的错误信息和适当的HTTP状态码
4. **性能监控**: 记录接口响应时间和处理情况
5. **扩展性**: 支持未来添加个性化推荐功能