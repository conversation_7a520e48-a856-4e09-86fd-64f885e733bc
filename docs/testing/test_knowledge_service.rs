//! 知识服务测试

use std::sync::Arc;
use recommendation_core::infrastructure::di::knowledge_factory;
use recommendation_core::application::knowledge::KnowledgeTreeQueryDto;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();
    
    println!("开始测试知识服务...");
    
    // 这里需要数据库连接，但我们先测试服务创建
    // 实际测试需要真实的数据库连接
    
    println!("测试完成");
    Ok(())
}
