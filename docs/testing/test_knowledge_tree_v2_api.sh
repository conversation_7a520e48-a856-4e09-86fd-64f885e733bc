#!/bin/bash

# 知识树V2接口测试脚本

echo "=== 知识树V2接口测试 ==="
echo ""

# 基础URL
BASE_URL="http://localhost:8000"

# 测试1: 基本知识树接口（所有学科，完整深度）
echo "1. 测试基本知识树接口（max_depth=4）"
curl -s -w "\n状态码: %{http_code}\n耗时: %{time_total}s\n" \
  "${BASE_URL}/api/v1/knowledge-tree-v2?max_depth=4" | jq '.'
echo ""

# 测试2: 指定学科
echo "2. 测试指定学科（subject_id=1, max_depth=3）"
curl -s -w "\n状态码: %{http_code}\n耗时: %{time_total}s\n" \
  "${BASE_URL}/api/v1/knowledge-tree-v2?subject_id=1&max_depth=3" | jq '.'
echo ""

# 测试3: 只到章节级别
echo "3. 测试只到章节级别（max_depth=2）"
curl -s -w "\n状态码: %{http_code}\n耗时: %{time_total}s\n" \
  "${BASE_URL}/api/v1/knowledge-tree-v2?max_depth=2" | jq '.'
echo ""

# 测试4: 只到小节级别
echo "4. 测试只到小节级别（max_depth=3）"
curl -s -w "\n状态码: %{http_code}\n耗时: %{time_total}s\n" \
  "${BASE_URL}/api/v1/knowledge-tree-v2?max_depth=3" | jq '.'
echo ""

# 测试5: 包含测试学科
echo "5. 测试包含测试学科（exclude_test_subjects=false）"
curl -s -w "\n状态码: %{http_code}\n耗时: %{time_total}s\n" \
  "${BASE_URL}/api/v1/knowledge-tree-v2?exclude_test_subjects=false" | jq '.'
echo ""

echo "=== 测试完成 ==="
