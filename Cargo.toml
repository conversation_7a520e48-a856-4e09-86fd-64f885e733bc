[workspace]
resolver = "3"  # 使用 Rust 2024 edition 的解析器版本
members = [
    "core",
    "api"
]
# 注意：tests 目录暂时不作为工作空间成员，需要修复编译错误后再添加

[workspace.package]
version = "1.1.0"
rust-version = "1.85"


[workspace.dependencies]
tokio = { version = "1.44.2", features = ["full"] }
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
serde_yaml = "0.9.34"
thiserror = "2.0.12"
anyhow = "1.0.98"
async-trait = "0.1.88"
tracing = "0.1.41"
tracing-subscriber = { version = "0.3.19", features = ["env-filter", "json", "chrono"] }
tracing-appender = "0.2.3"
tracing-core = "0.1.33"
reqwest = { version = "0.12.15", features = ["json"] }
sqlx = { version = "0.8.5", features = ["runtime-tokio-rustls", "postgres", "chrono", "json", "uuid"] }
sea-orm = { version = "1.1.10", features = ["sqlx-postgres", "runtime-tokio-rustls", "macros"] }
fastrand = "2.0"
num_cpus = "1.16.0"
sys-info = "0.9.1"
lazy_static = "1.4.0"
fsrs = "3.0.0" # FSRS 算法库

# 并发数据结构
dashmap = "6.1.0"
parking_lot = "0.12.3"

# 单例模式和懒加载
once_cell = "1.21.3"

# 环境变量
dotenv = "0.15.0"

# 正则表达式
regex = "1.11.1"

# 时间处理
chrono = { version = "0.4.40", features = ["serde"] }
chrono-tz = "0.8"

# 异步工具
futures = "0.3.31"

# UUID
uuid = { version = "1.16.0", features = ["v4", "serde"] }

# 数值转换
num-traits = "0.2.19"

# Redis
redis = { version = "0.30.0", features = ["tokio-comp", "aio", "tokio-native-tls-comp"] }

# API文档生成
utoipa = { version = "5.3.1", features = ["actix_extras"] }
utoipa-swagger-ui = { version = "9.0.1", features = ["actix-web"] }

# 加密和编码
ring = "0.17.14"
base64 = "0.22.1"
urlencoding = "2.1.3"

# API相关
actix-web = "4.10.2"
actix-cors = "0.7.1"
actix-service = "2.0.3"
config = "0.15.11"
toml = "0.8.20"
hostname = "0.4.1"
colored = "3.0.0"

# Nacos服务发现
nacos-sdk = { version = "0.5.0", features = ["default"] }
tokio-cron-scheduler = "0.13.0"

# AI提供商集成
genai = "0.3.5"

# 测试依赖
tempfile = "3.19.1"
mockall = "0.12.1"