# 项目文件结构说明

本文档说明了推荐系统项目的文件夹结构和组织方式。

## 根目录结构

```
recommendation-system2/
├── api/                    # API 服务层
├── core/                   # 核心业务逻辑
├── tests/                  # 集成测试和端到端测试
├── config/                 # 配置文件
├── docs/                   # 项目文档
├── scripts/                # 脚本工具
├── logs/                   # 日志文件
├── storage/                # 数据存储相关（包含 migrations）
├── tools/                  # 开发工具
├── docker/                 # Docker 相关文件
├── target/                 # Rust 编译输出
├── Cargo.toml             # Rust 工作空间配置
├── Cargo.lock             # Rust 依赖锁定
├── PROJECT_STRUCTURE.md   # 项目结构说明
└── readme.md              # 项目主要介绍
```

## 文件夹详细说明

### `/api` - API 服务层
- 包含 HTTP API 服务的实现
- 处理请求路由、中间件、错误处理
- 提供 RESTful API 接口

### `/core` - 核心业务逻辑
- 包含领域模型、业务服务、算法实现
- 遵循 DDD (领域驱动设计) 架构
- 独立于外部依赖的纯业务逻辑

### `/tests` - 集成测试和端到端测试
- 跨模块的集成测试
- 端到端测试
- 性能测试
- 作为独立的工作空间成员

### `/config` - 配置文件
- `base.yml` - 基础配置
- `development.yml` - 开发环境配置
- `production.yml` - 生产环境配置
- `test.yml` - 测试环境配置

### `/docs` - 项目文档
- 架构设计文档
- API 文档
- 算法说明
- 开发指南
- 故障排除指南

### `/scripts` - 脚本工具
- 数据库迁移脚本
- 数据导入脚本
- 测试脚本
- 部署脚本
- 性能测试脚本

### `/logs` - 日志文件
- 应用程序日志
- 错误日志
- 访问日志
- 调试日志

### `/storage` - 数据存储相关
- 数据库迁移文件（`migrations/`）
- 数据模型定义
- 存储配置
- 包含 SAT 和 FlashCard 两个子系统的迁移

### `/tools` - 开发工具
- 数据库优化工具
- 代码分析工具
- 报告生成工具

### `/docker` - Docker 相关文件
- `docker-compose.test.yml` - 测试环境 Docker 配置
- Dockerfile (如果有)
- Docker 相关脚本

## 文件命名规范

### 文档文件
- 使用大写字母和下划线：`README.md`, `API_GUIDE.md`
- 中文文档可以使用中文名称

### 配置文件
- 使用小写字母和点分隔：`base.yml`, `development.yml`

### 脚本文件
- 使用小写字母和下划线：`run_tests.sh`, `import_data.py`

### 日志文件
- 使用小写字母、连字符和日期：`api.log`, `dev-app.log.2025-06-16`

## 开发工作流

1. **开发阶段**：主要在 `/api` 和 `/core` 目录工作
2. **测试阶段**：使用 `/tests` 目录的集成测试
3. **部署阶段**：使用 `/scripts` 目录的部署脚本
4. **文档更新**：在 `/docs` 目录维护文档

## 注意事项

- `/target` 目录是 Rust 编译输出，不应提交到版本控制
- `/logs` 目录的日志文件不应提交到版本控制
- 配置文件中的敏感信息应使用环境变量
- 所有脚本文件应具有可执行权限

## 🔄 架构优化历史

### v2.0.0 (2025-07-18) - 根目录架构优化

#### ✅ 完成的优化
1. **清理根目录**：移除临时测试文件和脚本
2. **整理文档结构**：合并 `DOCUMENTATION_INDEX.md` 到 `docs/README.md`
3. **重组存储目录**：删除空的根目录 `migrations`，统一使用 `storage/migrations`
4. **优化工作空间**：将 `tests` 添加为独立的工作空间成员
5. **标准化入口**：更新项目结构说明和文档

#### 📊 优化效果
- **根目录更整洁**：只保留核心配置和说明文件
- **文档更统一**：集中的文档中心和清晰的导航
- **结构更清晰**：明确的模块边界和职责划分
- **维护更便捷**：标准化的工作空间配置

### v1.0.0 - 初始文档整理
- 移动散落文档到对应目录
- 创建文档分类和索引
- 建立文档维护规范

## 🎯 最佳实践

### 工作空间管理
- 使用 `cargo build` 构建所有成员
- 使用 `cargo test` 运行所有测试
- 使用 `cargo check` 检查所有代码

### 文档维护
- 新增功能时同步更新文档
- 保持文档与代码的一致性
- 遵循统一的 Markdown 格式

这样的组织方式使项目结构更清晰，便于维护和协作开发。
