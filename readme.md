# SAT 智能推荐系统

[![代码行数](https://img.shields.io/badge/代码行数-95896-blue)](scripts/count_code.sh)
[![版本](https://img.shields.io/badge/版本-0.2.0-brightgreen)](config/base.yml)
[![核心测试覆盖率](https://img.shields.io/badge/核心测试覆盖率-8.91%25-red)](core/)
[![总体覆盖率](https://img.shields.io/badge/总体覆盖率-约8%25-red)](api/)

## 🎯 项目概述

基于 **Rust + DDD架构** 构建的 SAT 考试智能推荐系统，采用先进的心理测量学模型，为学生提供个性化的学习体验。

### ✨ 核心特性

- 🧠 **智能推荐算法**：基于 ELO 评分和 IRT 模型的双重能力评估
- 📊 **精细化评估**：知识点级别的能力建模和推荐
- 🚀 **高性能架构**：异步 Rust + PostgreSQL + Redis 缓存
- 🔧 **模块化设计**：DDD 四层架构，易于扩展和维护
- 📱 **完整功能**：题目推荐、学习计划、记忆卡片、用户分层

## 📚 文档导航

完整的技术文档请查看：**[� 文档中心](docs/README.md)**

### 🚀 快速链接
- [系统架构](docs/architecture/推荐系统DDD架构分析.md) - DDD 四层架构设计
- [推荐算法](docs/recommendation_system_design.md) - 智能推荐系统设计
- [API 文档](docs/api/) - RESTful API 接口文档
- [用户分层](docs/feature/user_segmentation.md) - 个性化学习指导系统
- [性能优化](docs/performance_optimization_plan.md) - 系统性能优化方案

## 配置系统

系统使用多层次混合配置方案：

1. **基础配置文件 (`config/base.yml`)**: 定义所有共享的基础配置结构和默认值
2. **环境特定配置 (`config/{env}.yml`)**: 根据当前环境（development/test/production）提供特定配置
3. **环境变量**: 用于注入敏感信息（密码、密钥等）和动态配置

支持的环境变量占位符格式：`${ENV_NAME:default_value}`，系统会自动替换为相应环境变量的值，如果未设置则使用默认值。

### 配置示例

运行应用前，需要：

1. 复制 `.env.example` 为 `.env` 并填入适当的值
2. 确保 `config/` 目录存在并包含必要的配置文件

## 开发指南

### 环境准备

1. 安装Rust工具链: `curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh`
2. 安装PostgreSQL数据库
3. 安装其他可选依赖：Redis, ElasticSearch, MinIO(S3兼容存储)

### 构建和运行

```bash
# 编译项目
cargo build

# 运行项目
cargo run

# 运行测试
cargo test
```

### 数据库迁移

使用Flyway或类似工具运行 `storage/migrations` 目录中的SQL脚本。

```bash
flyway -url=******************************************** -user=postgres -password=postgres migrate
```

### 测试与覆盖率

项目包含全面的单元测试和集成测试，确保代码质量和功能稳定性。

#### 运行测试

```bash
# 运行所有测试
cargo test

# 运行核心模块测试
cargo test -p recommendation_core

# 运行API测试
cargo test -p recommendation_api

# 运行单个测试
cargo test test_name
```

#### 测试覆盖率

使用 [cargo-tarpaulin](https://github.com/xd009642/tarpaulin) 生成测试覆盖率报告：

```bash
# 安装tarpaulin
cargo install cargo-tarpaulin

# 生成覆盖率报告
cargo tarpaulin --out Html
```

覆盖率报告将保存在 `tarpaulin-report.html` 文件中。

当前测试覆盖率：
- 核心模块：8.91% (1282/14396 行)
- API模块：测试需要配置文件支持
- 总体覆盖率：约8%

重点测试覆盖的模块：
- ELO算法核心计算模块
- 数据层互操作
- 缓存服务
- 通知系统

## 🏗️ 系统架构

采用 **DDD (领域驱动设计)** 四层架构：

- **🎯 表示层** (`api/`) - HTTP API 接口和请求处理
- **📋 应用层** (`core/application/`) - 业务流程编排和服务协调
- **🏗️ 领域层** (`core/domain/`) - 核心业务逻辑和领域规则
- **🔧 基础设施层** (`core/infrastructure/`) - 数据持久化和外部集成

详细架构设计请参考：[DDD 架构分析](docs/architecture/推荐系统DDD架构分析.md)

## 📄 许可证

本项目遵循 [MIT 许可证](LICENSE)。


- **设计**: 高内聚、低耦合，独立于外部依赖

### 🔧 基础设施层 (Infrastructure Layer) - `core/infrastructure/`
- **职责**: 技术实现细节、外部系统集成、持久化
- **特点**: 实现领域层定义的接口，提供技术能力支撑
- **包含**: 数据库访问、缓存、消息队列、依赖注入等

## 核心特性

- ✅ **完全解耦**: 通过依赖倒置实现层次间解耦
- ✅ **模块化**: 按业务领域划分，职责清晰
- ✅ **异步优先**: 全面使用Tokio异步运行时
- ✅ **类型安全**: 强类型设计，编译时错误检查
- ✅ **可测试**: 高覆盖率的单元和集成测试

# 系统架构图

## DDD分层架构图

```mermaid
graph TB
    subgraph "表示层 (API)"
        A[HTTP Router] --> B[用户分层API]
        A --> C[推荐API] 
        A --> D[记忆卡片API]
        A --> E[学习计划API]
        A --> F[能力评估API]
    end
    
    subgraph "应用层 (Application)"
        G[用户分层应用服务] 
        H[推荐应用服务]
        I[记忆卡片应用服务]
        J[学习计划应用服务]
        K[能力评估应用服务]
    end
    
    subgraph "领域层 (Domain)"
        L[用户分层领域服务]
        M[推荐算法引擎]
        N[FSRS记忆算法]
        O[学习计划生成]
        P[ELO/IRT能力模型]
    end
    
    subgraph "基础设施层 (Infrastructure)"
        Q[PostgreSQL持久化]
        R[Redis缓存]
        S[依赖注入容器]
        T[日志监控系统]
    end
    
    B --> G
    C --> H  
    D --> I
    E --> J
    F --> K
    
    G --> L
    H --> M
    I --> N
    J --> O
    K --> P
    
    L --> Q
    M --> Q
    N --> Q
    O --> Q
    P --> Q
    
    G --> R
    H --> R
    I --> R
    
    classDef api fill:#e1f5fe
    classDef app fill:#f3e5f5  
    classDef domain fill:#e8f5e8
    classDef infra fill:#fff3e0
    
    class A,B,C,D,E,F api;
    class G,H,I,J,K app;
    class L,M,N,O,P domain;
    class Q,R,S,T infra;
```

## 数据流架构

```mermaid
graph LR
    subgraph "数据输入"
        A[用户答题记录]
        B[闪卡学习数据] 
        C[用户目标设置]
    end
    
    subgraph "算法处理"
        D[ELO评分算法]
        E[IRT能力模型]
        F[FSRS记忆算法]
        G[用户分层算法]
    end
    
    subgraph "推荐输出"
        H[题目推荐]
        I[学习计划]
        J[记忆卡片]
        K[个性化指导]
    end
    
    A --> D
    A --> E
    B --> F
    B --> G
    C --> G
    
    D --> H
    E --> H
    F --> J
    G --> K
    G --> I
    
    classDef input fill:#e3f2fd
    classDef algo fill:#f3e5f5
    classDef output fill:#e8f5e8
    
    class A,B,C input;
    class D,E,F,G algo;
    class H,I,J,K output;
```

# 功能特性

## 已实现功能

### 🎯 用户分层系统
- **智能用户分类**: 基于学习行为的7种用户类型分类
- **个性化指导**: 动态生成定制化学习建议
- **缓存优化**: 支持计算结果缓存和实时计算
- **详细文档**: [用户分层功能文档](docs/feature/user_segmentation.md)

### 📚 记忆卡片系统 (FSRS)
- **科学记忆算法**: 基于FSRS算法的间隔重复
- **多频次管理**: 支持高频、中频、低频词汇分层学习
- **学习统计**: 完整的学习进度和统计分析

### 📊 推荐算法引擎
- **多算法融合**: ELO、IRT、自适应精通等算法组合
- **实时推荐**: 基于用户能力的动态题目推荐
- **算法注册**: 插件化算法架构，易于扩展

### 📋 学习计划系统
- **智能规划**: 基于用户能力和目标的学习计划生成
- **动态调整**: 根据学习进度自动调整计划
- **多维度支持**: 数学、阅读、词汇等多领域计划

### 🔍 能力评估系统
- **ELO评分**: 实时更新的用户能力评分
- **IRT模型**: 基于项目反应理论的精确能力测量
- **知识点级别**: 细粒度的知识点能力评估

## 扩展计划

### 近期优化
- [ ] 推荐算法性能优化
- [ ] 缓存策略完善
- [ ] API响应时间优化
- [ ] 测试覆盖率提升

### 中期扩展  
- [ ] 学习路径智能规划
- [ ] 多设备学习同步
- [ ] 学习效果分析报告
- [ ] 协作学习功能

### 长期规划
- [ ] AI驱动的个性化学习
- [ ] 知识图谱集成
- [ ] 学习行为深度分析
- [ ] 多语言支持

## 性能指标

当前系统性能表现：
- **API响应时间**: < 100ms (平均)
- **推荐计算**: < 50ms (单次)
- **并发支持**: 1000+ QPS
- **数据库**: PostgreSQL + Redis缓存
- **测试覆盖率**: 核心模块 8.91%


# 开发任务

## 🔧 当前待办事项

### 高优先级
- [ ] 推荐算法性能优化
- [ ] 慢查询监控和飞书通知
- [ ] API路由结构整理
- [ ] 缓存抽象层完善

### 中优先级  
- [ ] 题目入库字段整理
- [ ] 知识树接口抽象
- [ ] 命名规范统一 (find → get)
- [ ] ORM更新操作完善

### 低优先级
- [ ] 默认知识点清理
- [ ] 日志系统合并
- [ ] DTO迁移完善
- [ ] 批量缓存清理


# 快速开始

## 运行应用

```bash
# 开发环境 (默认)
cargo run

# 测试环境  
APP_ENV=test cargo run

# 生产环境
APP_ENV=production cargo run

# 指定端口运行
PORT=8000 cargo run
```

## 开发命令

```bash
# 构建项目
cargo build

# 运行测试
cargo test

# 生成测试覆盖率报告
cargo tarpaulin --out Html

# 格式化代码
cargo fmt

# 代码检查
cargo clippy
```

## Docker运行

```bash
# 构建镜像
docker build -t recommendation-system .

# 运行容器
docker run -p 8000:8000 recommendation-system
```