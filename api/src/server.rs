//! 服务器模块
//!
//! 处理HTTP服务器的配置和启动

use actix_cors::Cors;
use actix_web::{App, HttpServer, middleware as actix_middleware, web};
use recommendation_core::logging::{ServiceLogger, ServiceType};
use recommendation_core::{Error, Result, log_error, log_info};

use crate::middleware::ErrorNotificationMiddleware;
use crate::middleware::performance_monitor::PerformanceMonitor;
use crate::middleware::rate_limit::RateLimiter;
use crate::middleware::request_logger::RequestLogger;
use crate::router;
use crate::services::types::ServiceData; // 引入路由模块

/// 启动HTTP服务器并返回服务器句柄（异步版本）
pub async fn start_server(
    host: String,
    port: u16,
    shutdown_timeout_secs: u64,
    services: ServiceData,
) -> Result<actix_web::dev::ServerHandle> {
    // 创建专用的服务器日志记录器
    let logger = ServiceLogger::new(ServiceType::Other, "HTTP服务器");

    start_server_with_logger(host, port, shutdown_timeout_secs, services, logger).await
}

/// 内部实现函数，接受日志器参数（异步版本）
async fn start_server_with_logger(
    host: String,
    port: u16,
    shutdown_timeout_secs: u64,
    services: ServiceData,
    logger: ServiceLogger,
) -> Result<actix_web::dev::ServerHandle> {
    // 启动服务器
    log_info!(logger, "启动服务器，监听地址: {}:{}", host, port);
    log_info!(logger, "服务关闭超时设置为: {}秒", shutdown_timeout_secs);

    // 异步检查端口可用性（可选的预检查）
    if let Err(e) = check_port_availability(&host, port).await {
        let error_msg = format!("端口 {}:{} 不可用: {}", host, port, e);
        log_error!(logger, "{}", error_msg);
        return Err(Error::system(error_msg));
    }

    // 将服务转换为Web数据
    let (
        ability_service_data,
        knowledge_service_data,
        storage_manager_data,
        nacos_service_data,
        user_service_data,
        mastery_v2_service_data,
        new_recommendation_service_data,
        question_service_data,
        flashcard_recommendation_service_data,
        flashcard_service_data,
        progress_repository_data,
        answer_history_repository_data,
        card_repository_data,
        plan_service_data,
        plan_facade_service_data,
        exam_service_data,
        user_data_aggregation_service_data,
        user_learning_guidance_repository_data,
        personalized_learning_guidance_service_data,
        task_scheduler_data,
        knowledge_tree_with_abilities_service_data,
        knowledge_application_service_data,
    ) = crate::services::init_refactored::to_web_data(services.clone());

    // 创建速率限制器
    let rate_limiter = RateLimiter::new(300, 60); // 每分钟300次请求，最大突发60次

    // 创建性能监控中间件
    let performance_monitor = PerformanceMonitor {
        slow_request_threshold_ms: 200, // 设置慢请求阈值为200毫秒
    };

    // 创建错误通知中间件
    let error_notification = ErrorNotificationMiddleware::default();

    // 异步获取系统资源状态
    let system_info = get_system_info().await;
    log_info!(
        logger,
        "系统资源状态：CPU核心数={}, 内存总量={}MB, 已用内存={}MB ({}%)",
        system_info.thread_count,
        system_info.mem_total_mb,
        system_info.mem_used_mb,
        system_info.mem_usage_pct
    );

    // 异步检查服务健康状态
    let health_status = check_services_health(&services).await;
    log_info!(logger, "服务健康状态：{}", health_status);

    let _logger_for_spawn = logger.clone();
    let server = HttpServer::new(move || {
        // 创建CORS中间件
        let cors = Cors::default()
            .allow_any_origin()
            .allow_any_method()
            .allow_any_header()
            .max_age(3600);

        // 创建应用
        App::new()
            // 应用CORS中间件
            .wrap(cors)
            // 应用请求日志中间件
            .wrap(RequestLogger::default())
            // 应用性能监控中间件
            .wrap(performance_monitor.clone())
            // 应用速率限制中间件
            .wrap(rate_limiter.clone())
            // 应用错误通知中间件
            .wrap(error_notification.clone())
            // 应用标准日志中间件 (默认开启，用于Access Log)
            .wrap(actix_middleware::Logger::new("%a %r %s %b %D"))
            // 注册服务
            .app_data(ability_service_data.clone())
            .app_data(knowledge_service_data.clone())
            .app_data(storage_manager_data.clone())
            .app_data(nacos_service_data.clone())
            .app_data(user_service_data.clone())
            .app_data(mastery_v2_service_data.clone())
            .app_data(new_recommendation_service_data.clone())
            .app_data(question_service_data.clone())
            .app_data(flashcard_recommendation_service_data.clone())
            .app_data(flashcard_service_data.clone())
            .app_data(progress_repository_data.clone())
            .app_data(answer_history_repository_data.clone())
            .app_data(card_repository_data.clone())
            .app_data(plan_service_data.clone())
            .app_data(plan_facade_service_data.clone())
            .app_data(exam_service_data.clone())
            .app_data(user_data_aggregation_service_data.clone())
            .app_data(user_learning_guidance_repository_data.clone())
            .app_data(personalized_learning_guidance_service_data.clone())
            .app_data(task_scheduler_data.clone())
            .app_data(knowledge_tree_with_abilities_service_data.clone())
            .app_data(knowledge_application_service_data.clone())
            .app_data(web::Data::new(services.clone()))
            // 配置路由（使用新的路由模块）
            .configure(router::configure)
    })
    .shutdown_timeout(shutdown_timeout_secs)
    .bind((host.clone(), port))
    .map_err(|e| {
        let error_msg = format!("无法绑定服务器端口 {}:{}: {}", host, port, e);
        log_error!(logger, "{}", error_msg);
        Error::system(error_msg)
    })?;

    let server = server.run();

    // 返回服务器句柄
    let server_handle = server.handle();

    // 启动服务器（在后台运行）
    tokio::spawn(async move {
        if let Err(e) = server.await {
            log_error!(_logger_for_spawn, "服务器运行错误: {}", e);

            // 发送服务器错误通知
            let error_payload = recommendation_core::NotificationPayload::error(
                "服务器错误",
                &format!("服务器运行出现错误: {}", e),
            );
            let _ = recommendation_core::send_notification(error_payload).await;
        }
    });

    // 服务器启动完成日志
    log_info!(logger, "服务器启动成功 {}:{}", host, port);

    Ok(server_handle)
}

/// 系统信息结构体
#[derive(Debug)]
struct SystemInfo {
    thread_count: usize,
    mem_total_mb: u64,
    mem_used_mb: u64,
    mem_usage_pct: u32,
}

/// 异步获取系统信息
async fn get_system_info() -> SystemInfo {
    // 在实际应用中，这里可能需要异步获取系统信息
    // 这里为了演示，我们保持原有的同步逻辑
    let thread_count = num_cpus::get();

    let (mem_total_mb, mem_used_mb, mem_usage_pct) = match sys_info::mem_info() {
        Ok(memory_info) => {
            let mem_total_mb = memory_info.total / 1024;
            let mem_used_mb = (memory_info.total - memory_info.free) / 1024;
            let mem_usage_pct = (mem_used_mb as f64 / mem_total_mb as f64 * 100.0) as u32;
            (mem_total_mb, mem_used_mb, mem_usage_pct)
        }
        Err(_) => (0, 0, 0),
    };

    SystemInfo {
        thread_count,
        mem_total_mb,
        mem_used_mb,
        mem_usage_pct,
    }
}

/// 异步检查端口可用性
async fn check_port_availability(host: &str, port: u16) -> Result<()> {
    // 这里可以实现真正的端口检查逻辑
    // 目前只是一个占位符
    tokio::time::sleep(std::time::Duration::from_millis(1)).await;

    // 简单的端口检查 - 实际实现中可以更复杂
    match tokio::net::TcpListener::bind((host, port)).await {
        Ok(_) => Ok(()),
        Err(e) => Err(Error::system(format!("端口检查失败: {}", e))),
    }
}

/// 异步检查服务健康状态
async fn check_services_health(services: &ServiceData) -> String {
    // 这里可以实现真正的服务健康检查
    tokio::time::sleep(std::time::Duration::from_millis(10)).await;

    let mut status = Vec::new();

    // 检查各个服务的健康状态
    status.push("能力服务=可用".to_string());
    status.push("推荐服务=可用".to_string());
    status.push("知识服务=可用".to_string());

    if services.nacos_service.is_some() {
        status.push("Nacos服务=可用".to_string());
    }

    status.push("存储服务=可用".to_string());

    status.join(", ")
}


