use std::sync::Arc;
use tokio_cron_scheduler::JobScheduler;

use crate::services::nacos_client::NacosClientManager;
use async_trait::async_trait;
use recommendation_core::Result;
use recommendation_core::application::question::service::QuestionApplicationService;
use recommendation_core::application::recommendation::MasteryRecommendationService;
use recommendation_core::domain::flashcard::FSRSAnswerHistoryRepository;
use recommendation_core::services::elo::models::{
    KnowledgeEloRecommendation, StageCalculationResult,
};
use recommendation_core::services::{
    AbilityService as CoreAbilityService,
    elo::{KnowledgeEloServiceTrait, KnowledgeEloUpdate},
    knowledge::KnowledgeServiceTrait,
    knowledge_tree_with_abilities::KnowledgeTreeWithAbilitiesService,
    user::UserService,
};
use recommendation_core::infrastructure::persistence::database::manager::StorageManager;

/// 空ELO服务实现 - 用于默认情况
#[derive(Debug, <PERSON><PERSON>)]
pub struct EmptyKnowledgeEloService;

#[async_trait]
impl KnowledgeEloServiceTrait for EmptyKnowledgeEloService {
    async fn get_knowledge_elo(&self, _student_id: i64, _knowledge_id: i32) -> Result<f64> {
        Ok(1700.0) // 返回默认ELO值
    }

    async fn update_knowledge_elo(
        &self,
        student_id: i64,
        knowledge_id: i32,
        question_id: i32,
        is_correct: bool,
        _answer_id: Option<i32>,
    ) -> Result<KnowledgeEloUpdate> {
        // 返回一个模拟的更新结果
        Ok(KnowledgeEloUpdate {
            student_id,
            knowledge_id,
            elo_before: 1700.0,
            elo_after: if is_correct { 1710.0 } else { 1690.0 },
            question_id,
            question_elo: 1700.0,
            is_correct,
            k_factor: 32.0,
        })
    }

    async fn predict_success_probability(
        &self,
        _student_id: i64,
        _knowledge_id: i32,
        _question_id: i32,
    ) -> Result<f64> {
        Ok(0.5) // 返回默认概率
    }

    async fn recommend_questions_by_knowledge_elo(
        &self,
        _student_id: i64,
        _knowledge_id: i32,
        _target_probability: f64,
        _limit: usize,
    ) -> Result<Vec<KnowledgeEloRecommendation>> {
        Ok(vec![]) // 返回空推荐
    }

    async fn calculate_dynamic_k_factor(
        &self,
        _student_id: i64,
        _knowledge_id: i32,
        _question_id: i32,
        _submission_count: u32,
    ) -> Result<f64> {
        Ok(32.0) // 返回默认K因子
    }

    async fn calculate_learning_stage(
        &self,
        student_id: i64,
        knowledge_id: i32,
    ) -> Result<StageCalculationResult> {
        // 返回默认学习阶段
        Ok(StageCalculationResult {
            student_id,
            knowledge_id,
            current_stage: "cold_start".to_string(),
            previous_stage: Some("none".to_string()),
            stage_progress: 0.0,
            stage_changed_at: Some(chrono::Utc::now()),
            ability_rating: 1700.0,
            total_answers: 0,
            correct_answers: 0,
        })
    }

    async fn update_knowledge_elo_enhanced(
        &self,
        student_id: i64,
        knowledge_id: i32,
        question_id: i32,
        is_correct: bool,
        _student_answer: String,
        _response_time_sec: Option<i32>,
        _answer_type: Option<String>,
        _session_id: Option<String>,
        _answer_context: Option<serde_json::Value>,
    ) -> Result<KnowledgeEloUpdate> {
        // 返回一个模拟的增强版更新结果，与普通方法相比有更大变化
        Ok(KnowledgeEloUpdate {
            student_id,
            knowledge_id,
            elo_before: 1700.0,
            elo_after: if is_correct { 1515.0 } else { 1485.0 }, // 更大的变化
            question_id,
            question_elo: 1700.0,
            is_correct,
            k_factor: 48.0, // 更大的K因子
        })
    }
}

/// 服务错误类型
#[derive(Debug, thiserror::Error)]
pub enum ServiceError {
    #[error("Configuration error: {0}")]
    ConfigError(String),

    #[error("Database/Storage error: {0}")]
    StorageError(String),

    #[error("Service initialization error: {0}")]
    InitializationError(String),
    // #[error("Generic service error: {0}")]
    // GenericError(String),
}

/// 服务数据集合 (Holding core services)
#[derive(Clone)]
pub struct ServiceData {
    /// 能力值服务
    pub ability_service: Arc<CoreAbilityService>,
    /// 知识服务
    pub knowledge_service: Arc<dyn KnowledgeServiceTrait>,
    /// 知识点ELO服务
    pub elo_service: Arc<dyn KnowledgeEloServiceTrait + Send + Sync>,
    /// 存储管理器
    pub storage_manager: Arc<StorageManager>,
    /// Nacos服务管理器
    pub nacos_service: Option<Arc<NacosClientManager>>,
    /// 心跳任务调度器
    #[allow(dead_code)]
    pub scheduler: Option<Arc<JobScheduler>>,

    /// 新版自适应掌握度服务（可选）
    pub mastery_v2_service: Option<Arc<dyn MasteryRecommendationService>>,
    /// 新的DDD架构推荐服务（可选）
    pub new_recommendation_service: Option<Arc<dyn MasteryRecommendationService>>,
    /// 用户服务
    pub user_service: Arc<UserService>,
    /// 问题应用服务
    pub question_service: Arc<dyn QuestionApplicationService>,
    /// 题库服务
    pub question_bank_service: Arc<recommendation_core::application::question::question_bank_service::QuestionBankService>,
    /// 闪卡推荐服务（可选）
    pub flashcard_recommendation_service: Option<Arc<recommendation_core::application::flashcard::FlashcardRecommendationService>>,
    /// 闪卡应用服务
    pub flashcard_service: Option<Arc<recommendation_core::application::flashcard::service::FlashCardService>>,
    /// 进度仓储
    pub progress_repository: Option<Arc<dyn recommendation_core::domain::flashcard::UserCardProgressRepository + Send + Sync>>,
    /// 答题历史仓储
    pub answer_history_repository: Option<Arc<dyn FSRSAnswerHistoryRepository + Send + Sync>>,
    /// 卡片仓储
    pub card_repository: Option<Arc<dyn recommendation_core::domain::flashcard::CardRepository + Send + Sync>>,
    /// 学习计划服务
    pub plan_service: Option<Arc<recommendation_core::application::plan::PlanApplicationService>>,
    /// 计划门面服务 (新架构)
    pub plan_facade_service: Option<Arc<recommendation_core::application::plan::PlanFacadeService>>,
    /// 计划任务服务 (新架构)
    pub plan_task_service: Option<Arc<recommendation_core::application::plan::PlanTaskService>>,
    /// 定时任务调度器
    pub task_scheduler: Option<Arc<recommendation_core::infrastructure::scheduler::TaskScheduler>>,
    /// 答题处理服务
    pub answer_processing_service: Option<Arc<recommendation_core::application::answer::AnswerProcessingService>>,
    /// 考试服务
    pub exam_service: Option<Arc<dyn recommendation_core::application::exam::ExamApplicationService>>,
    /// 用户数据聚合服务
    pub user_data_aggregation_service: Arc<dyn recommendation_core::domain::user_segmentation::repositories::UserDataAggregationRepository>,
    /// 用户学习指导仓储服务
    pub user_learning_guidance_repository: Arc<dyn recommendation_core::domain::user_segmentation::UserLearningGuidanceRepository>,
    /// 个性化学习指导应用服务
    pub personalized_learning_guidance_service: Arc<recommendation_core::application::user_segmentation::PersonalizedLearningGuidanceApplicationService>,
    /// 知识树与能力整合服务
    pub knowledge_tree_with_abilities_service: Option<Arc<KnowledgeTreeWithAbilitiesService>>,
    /// 知识应用服务（新DDD架构）
    pub knowledge_application_service: Option<Arc<recommendation_core::application::knowledge::KnowledgeApplicationService>>,
}

impl ServiceData {
    pub fn new(
        storage_manager: Arc<StorageManager>,
        ability_service: Arc<CoreAbilityService>,
        knowledge_service: Arc<dyn KnowledgeServiceTrait>,
        elo_service: Arc<dyn KnowledgeEloServiceTrait + Send + Sync>,
        question_bank_service: Arc<recommendation_core::application::question::question_bank_service::QuestionBankService>,
        user_data_aggregation_service: Arc<dyn recommendation_core::domain::user_segmentation::repositories::UserDataAggregationRepository>,
        user_learning_guidance_repository: Arc<
            dyn recommendation_core::domain::user_segmentation::UserLearningGuidanceRepository,
        >,
        personalized_learning_guidance_service: Arc<recommendation_core::application::user_segmentation::PersonalizedLearningGuidanceApplicationService>,
    ) -> Self {
        // 创建问题应用服务
        let question_service =
            recommendation_core::infrastructure::di::factory::create_question_service(
                storage_manager.clone(),
            ).expect("Failed to create question service");

        Self {
            ability_service,
            knowledge_service,
            elo_service,
            storage_manager,
            nacos_service: None,
            scheduler: None,

            mastery_v2_service: None,
            new_recommendation_service: None,
            user_service: Arc::new(UserService::new_empty()),
            question_service,
            question_bank_service,
            flashcard_recommendation_service: None,
            flashcard_service: None,
            progress_repository: None,
            answer_history_repository: None,
            card_repository: None,
            plan_service: None,
            plan_facade_service: None,
            plan_task_service: None,
            task_scheduler: None,
            answer_processing_service: None,
            exam_service: None,
            user_data_aggregation_service,
            user_learning_guidance_repository,
            personalized_learning_guidance_service,
            knowledge_tree_with_abilities_service: None,
            knowledge_application_service: None,
        }
    }

    /// 获取能力视图
    pub fn ability_view(
        &self,
    ) -> Arc<dyn recommendation_core::storage::traits::ability_view::AbilityView + Send + Sync>
    {
        // 克隆storage_manager以便能调用方法
        let storage_manager = Arc::clone(&self.storage_manager);
        // 由于使用了Arc包装，需要明确使用.操作符调用方法
        let _db = storage_manager.sea_orm_db();

        // 使用指定的导入路径创建一个新的AbilityView实现
        use recommendation_core::infrastructure::persistence::storage_impl::ability_view::SeaOrmAbilityView;
        Arc::new(SeaOrmAbilityView { db: _db })
    }

    /// 创建 Application 层的能力统计服务
    pub fn create_ability_stats_service(
        &self,
    ) -> std::sync::Arc<dyn recommendation_core::application::ability::service::AbilityStatsService>
    {
        use recommendation_core::algorithms::elo::EloConfig;
        use recommendation_core::infrastructure::di::ability_factory::create_ability_stats_service;

        create_ability_stats_service(
            self.storage_manager.sea_orm_db(),
            self.knowledge_service.clone(),
            Arc::new(EloConfig::default()),
        )
    }

    /// 创建知识树与能力整合服务
    pub fn create_knowledge_tree_with_abilities_service(&self) -> Arc<KnowledgeTreeWithAbilitiesService> {
        // 创建能力统计服务
        let ability_stats_service = Arc::new(
            recommendation_core::services::ability::stats::AbilityStatsService::new(
                self.ability_view(),
                self.knowledge_service.clone(),
                Arc::new(recommendation_core::algorithms::elo::EloConfig::default()),
            )
        );

        // 创建知识树与能力整合服务
        Arc::new(KnowledgeTreeWithAbilitiesService::new(
            self.knowledge_service.clone(),
            ability_stats_service,
            self.storage_manager.clone(),
        ))
    }

    /// 创建知识应用服务（新DDD架构）
    pub fn create_knowledge_application_service(&self) -> Arc<recommendation_core::application::knowledge::KnowledgeApplicationService> {
        tracing::debug!("ServiceData: 开始创建知识应用服务");
        let service = recommendation_core::infrastructure::di::knowledge_factory::create_knowledge_application_service(
            self.storage_manager.sea_orm_db()
        );
        tracing::debug!("ServiceData: 知识应用服务创建完成");
        service
    }

    /// 获取知识点仓库
    pub fn knowledge_repository(
        &self,
    ) -> Arc<dyn recommendation_core::storage::traits::knowledge::KnowledgeRepository> {
        // 克隆storage_manager以便能调用方法
        let storage_manager = Arc::clone(&self.storage_manager);
        // 由于使用了Arc包装，需要明确使用.操作符调用方法
        let _db = storage_manager.sea_orm_db();

        // 使用指定的导入路径创建一个新的KnowledgeRepository实现
        use recommendation_core::infrastructure::persistence::storage_impl::knowledge::SeaOrmKnowledgeRepository;
        let db = Arc::clone(&self.storage_manager).sea_orm_db();
        Arc::new(SeaOrmKnowledgeRepository::new(db))
    }

    /// 获取计划门面服务 (新架构)
    pub fn plan_facade_service(
        &self,
    ) -> Option<Arc<recommendation_core::application::plan::PlanFacadeService>> {
        self.plan_facade_service.clone()
    }

    /// 获取计划任务服务 (新架构)
    pub fn plan_task_service(
        &self,
    ) -> Option<Arc<recommendation_core::application::plan::PlanTaskService>> {
        self.plan_task_service.clone()
    }

    /// 获取学习计划服务 (向后兼容)
    pub fn plan_service(
        &self,
    ) -> Option<Arc<recommendation_core::application::plan::PlanApplicationService>> {
        self.plan_service.clone()
    }
}
