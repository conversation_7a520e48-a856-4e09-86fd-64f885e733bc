//! 重构后的服务初始化模块
//!
//! 使用服务容器管理依赖注入，减少耦合

use actix_web::web;
use anyhow::Result;
use sea_orm::Database;
use std::sync::Arc;
use tokio_cron_scheduler::JobScheduler;
use tracing::{error, warn};

use crate::services::nacos_client::{NacosClientManager, NacosConfig};
use crate::services::types::{ServiceData, ServiceError};
use recommendation_core::application::question::service::QuestionApplicationService;
use recommendation_core::config::{Config as CoreConfig, DatabaseConfig};
use recommendation_core::infrastructure::di::ServiceContainer;
use recommendation_core::services::AbilityService;
use recommendation_core::services::knowledge::KnowledgeServiceTrait;
use recommendation_core::services::user::UserService;
use recommendation_core::infrastructure::persistence::StorageManager;

/// 初始化所有应用服务 - 重构版本
pub async fn init_services_v2(
    config: Arc<CoreConfig>,
    yaml_config: serde_yaml::Value,
) -> Result<ServiceData, ServiceError> {
    tracing::trace!("开始初始化服务");

    // 1. 初始化数据库连接
    let (storage_manager, db_connection) = init_database_and_storage(&config).await?;

    // 2. 初始化缓存
    init_cache(&config).await?;

    // 3. 创建服务容器
    let mut container = ServiceContainer::new(
        config.clone(),
        storage_manager.clone(),
        db_connection.clone(),
    );

    // 4. 初始化核心服务
    container
        .initialize_core_services()
        .await
        .map_err(|e| ServiceError::InitializationError(format!("核心服务初始化失败: {}", e)))?;

    // 5. 初始化应用层服务
    container
        .initialize_application_services()
        .await
        .map_err(|e| ServiceError::InitializationError(format!("应用服务初始化失败: {}", e)))?;

    // 6. 初始化外部服务
    let (nacos_service, scheduler, task_scheduler) = init_external_services(
        &config,
        &yaml_config,
        container.plan_facade_service(),
        container.plan_task_service(),
    )
    .await;

    // 7. 初始化用户数据聚合服务
    let user_data_aggregation_service =
        init_user_data_services(&container, storage_manager.clone())?;

    // 8. 构建ServiceData
    let service_data = build_service_data(
        container,
        user_data_aggregation_service,
        nacos_service,
        scheduler,
        task_scheduler,
    )?;

    tracing::info!("服务初始化完成");
    Ok(service_data)
}

/// 初始化数据库连接和存储管理器
async fn init_database_and_storage(
    config: &CoreConfig,
) -> Result<
    (
        Arc<StorageManager>,
        sea_orm::DatabaseConnection,
    ),
    ServiceError,
> {
    let db_name = "main";
    let pg_config = config
        .databases
        .get(db_name)
        .and_then(|db_conf| match db_conf {
            DatabaseConfig::Postgres(pg) if pg.enabled => Some(pg),
            _ => None,
        })
        .ok_or_else(|| {
            ServiceError::ConfigError(format!(
                "PostgreSQL config '{}' not found or not enabled",
                db_name
            ))
        })?;

    // 建立数据库连接
    let db = Database::connect(&pg_config.url)
        .await
        .map_err(|e| ServiceError::StorageError(format!("Database connection failed: {}", e)))?;

    // 创建存储管理器并设置为全局实例
    let storage_manager = StorageManager::initialize_global_with_config(pg_config.clone())
        .await
        .map_err(|e| {
            ServiceError::StorageError(format!("Storage manager creation failed: {}", e))
        })?;

    Ok((storage_manager, db))
}

/// 初始化缓存系统
async fn init_cache(config: &CoreConfig) -> Result<(), ServiceError> {
    let redis_url = if config.cache.redis.enabled {
        Some(config.cache.redis.url.as_str())
    } else {
        None
    };

    // 初始化全局缓存管理器
    match recommendation_core::services::cache::hybrid::HybridCacheManager::initialize_global(
        redis_url,
    )
    .await
    {
        Ok(_) => {
            tracing::trace!("缓存管理器初始化成功");
        }
        Err(e) => {
            warn!("缓存管理器初始化失败: {}，使用内存缓存", e);
            // 尝试创建内存模式缓存
            match recommendation_core::services::cache::hybrid::HybridCacheManager::initialize_global(None).await {
                Ok(_) => tracing::trace!("内存缓存初始化成功"),
                Err(e2) => error!("内存缓存初始化失败: {}", e2),
            }
        }
    }

    Ok(())
}

/// 初始化外部服务（Nacos等）
async fn init_external_services(
    config: &CoreConfig,
    yaml_config: &serde_yaml::Value,
    _plan_facade_service: Option<Arc<recommendation_core::application::plan::PlanFacadeService>>,
    plan_task_service: Option<Arc<recommendation_core::application::plan::PlanTaskService>>,
) -> (
    Option<Arc<NacosClientManager>>,
    Option<Arc<JobScheduler>>,
    Option<Arc<recommendation_core::infrastructure::scheduler::TaskScheduler>>,
) {
    let (nacos_service, job_scheduler) = match init_nacos_service(config, yaml_config).await {
        Ok((nacos, scheduler)) => (nacos, scheduler),
        Err(e) => {
            warn!("Nacos服务初始化失败: {}", e);
            (None, None)
        }
    };

    // 初始化任务调度器
    let task_scheduler = if let Some(task_svc) = plan_task_service {
        match recommendation_core::infrastructure::scheduler::TaskScheduler::new(task_svc.clone())
            .await
        {
            Ok(scheduler) => {
                // 启动调度器
                if let Err(e) = scheduler.start().await {
                    error!("启动任务调度器失败: {}", e);
                    None
                } else {
                    tracing::info!("任务调度器启动成功");
                    Some(Arc::new(scheduler))
                }
            }
            Err(e) => {
                error!("创建任务调度器失败: {}", e);
                None
            }
        }
    } else {
        tracing::trace!("计划任务服务未初始化，跳过任务调度器");
        None
    };

    (nacos_service, job_scheduler, task_scheduler)
}

/// 初始化Nacos服务发现
async fn init_nacos_service(
    app_config: &CoreConfig,
    yaml_config: &serde_yaml::Value,
) -> Result<(Option<Arc<NacosClientManager>>, Option<Arc<JobScheduler>>)> {
    // 检查是否启用了Nacos服务发现
    let enabled = yaml_config
        .get("service_discovery")
        .and_then(|sd| sd.get("enabled"))
        .and_then(|e| e.as_bool())
        .unwrap_or(false);

    if !enabled {
        tracing::trace!("Nacos服务发现未启用");
        return Ok((None, None));
    }

    // 从YAML配置加载Nacos配置
    let nacos_config = NacosClientManager::from_yaml_config(yaml_config).unwrap_or_else(|e| {
        warn!("加载Nacos配置失败: {}，使用默认配置", e);
        NacosConfig::default()
    });

    // 创建Nacos客户端管理器
    let nacos_service = match NacosClientManager::new(app_config, nacos_config).await {
        Ok(service) => Arc::new(service),
        Err(e) => {
            error!("创建Nacos客户端失败: {}", e);
            return Ok((None, None));
        }
    };

    // 注册服务
    match nacos_service.register().await {
        Ok(_) => {
            tracing::info!("Nacos服务注册成功");
            Ok((Some(nacos_service), None))
        }
        Err(e) => {
            warn!("Nacos服务注册失败: {}", e);
            Ok((None, None))
        }
    }
}

/// 初始化用户数据相关服务
fn init_user_data_services(
    container: &ServiceContainer,
    _storage_manager: Arc<StorageManager>,
) -> Result<Option<recommendation_core::infrastructure::di::user_segmentation_factory::UserSegmentationServices>, ServiceError>{
    // 直接从容器获取用户分群服务
    if let Some(user_segmentation_services) = container.user_segmentation_services() {
        Ok(Some(user_segmentation_services))
    } else {
        // 如果没有初始化，记录警告但不报错
        warn!("用户分群服务未初始化，相关功能将不可用");
        Ok(None)
    }
}

/// 构建最终的ServiceData
fn build_service_data(
    container: ServiceContainer,
    user_segmentation_services: Option<recommendation_core::infrastructure::di::user_segmentation_factory::UserSegmentationServices>,
    nacos_service: Option<Arc<NacosClientManager>>,
    scheduler: Option<Arc<JobScheduler>>,
    task_scheduler: Option<Arc<recommendation_core::infrastructure::scheduler::TaskScheduler>>,
) -> Result<ServiceData, ServiceError> {
    // 如果用户分群服务可用，提取相关服务
    let (
        user_data_aggregation_service,
        user_learning_guidance_repository,
        personalized_learning_guidance_service,
    ) = if let Some(services) = user_segmentation_services {
        (
            services.data_aggregation_repository(),
            services.guidance_repository(),
            services.application_service(),
        )
    } else {
        return Err(ServiceError::InitializationError(
            "用户分群服务未初始化".to_string(),
        ));
    };

    // 构建ServiceData
    let mut service_data = ServiceData::new(
        container.storage_manager(),
        container
            .ability_service()
            .ok_or_else(|| ServiceError::InitializationError("能力服务未初始化".to_string()))?,
        // 已删除：recommendation_service - 未被任何API处理器使用
        container
            .knowledge_service()
            .ok_or_else(|| ServiceError::InitializationError("知识服务未初始化".to_string()))?,
        container
            .elo_service()
            .ok_or_else(|| ServiceError::InitializationError("ELO服务未初始化".to_string()))?,
        container
            .question_bank_service()
            .ok_or_else(|| ServiceError::InitializationError("题库服务未初始化".to_string()))?,
        user_data_aggregation_service,
        user_learning_guidance_repository.ok_or_else(|| {
            ServiceError::InitializationError("用户学习指导仓储未初始化".to_string())
        })?,
        personalized_learning_guidance_service,
    );

    // 设置可选服务
    service_data.nacos_service = nacos_service;
    service_data.scheduler = scheduler;

    service_data.mastery_v2_service = container.mastery_v2_service();
    service_data.new_recommendation_service = container.new_recommendation_service();
    service_data.user_service = container
        .user_service()
        .unwrap_or_else(|| Arc::new(recommendation_core::services::user::UserService::new_empty()));
    service_data.flashcard_recommendation_service = container.flashcard_recommendation_service();
    service_data.flashcard_service = container.flashcard_service();
    service_data.plan_service = container.plan_service();
    service_data.plan_facade_service = container.plan_facade_service();
    service_data.plan_task_service = container.plan_task_service();
    service_data.answer_processing_service = container.answer_processing_service();
    service_data.question_service = container
        .question_service()
        .ok_or_else(|| ServiceError::InitializationError("问题服务未初始化".to_string()))?;
    service_data.task_scheduler = task_scheduler;

    // 创建知识树与能力整合服务
    service_data.knowledge_tree_with_abilities_service = Some(
        service_data.create_knowledge_tree_with_abilities_service()
    );

    // 创建知识应用服务（新DDD架构）
    service_data.knowledge_application_service = Some(
        service_data.create_knowledge_application_service()
    );

    // 设置考试服务
    service_data.exam_service = container.exam_service();

    Ok(service_data)
}

/// Web服务数据类型定义
pub type WebServiceData = (
    web::Data<Arc<AbilityService>>,
    web::Data<Arc<dyn KnowledgeServiceTrait>>,
    web::Data<Arc<StorageManager>>,
    web::Data<Option<Arc<NacosClientManager>>>,
    web::Data<Arc<UserService>>,
    web::Data<Option<Arc<dyn recommendation_core::application::recommendation::MasteryRecommendationService>>>,
    web::Data<Option<Arc<dyn recommendation_core::application::recommendation::MasteryRecommendationService>>>,
    web::Data<Arc<dyn QuestionApplicationService>>,
    web::Data<Option<Arc<recommendation_core::application::flashcard::FlashcardRecommendationService>>>,
    web::Data<Option<Arc<recommendation_core::application::flashcard::service::FlashCardService>>>,
    web::Data<Option<Arc<dyn recommendation_core::domain::flashcard::UserCardProgressRepository + Send + Sync>>>,
    web::Data<Option<Arc<dyn recommendation_core::domain::flashcard::FSRSAnswerHistoryRepository + Send + Sync>>>,
    web::Data<Option<Arc<dyn recommendation_core::domain::flashcard::CardRepository + Send + Sync>>>,
    web::Data<Option<Arc<recommendation_core::application::plan::PlanApplicationService>>>,
    web::Data<Option<Arc<recommendation_core::application::plan::PlanFacadeService>>>,
    web::Data<Option<Arc<dyn recommendation_core::application::exam::ExamApplicationService>>>,
    web::Data<Arc<dyn recommendation_core::domain::user_segmentation::repositories::UserDataAggregationRepository>>,
    web::Data<Arc<dyn recommendation_core::domain::user_segmentation::UserLearningGuidanceRepository>>,
    web::Data<Arc<recommendation_core::application::user_segmentation::PersonalizedLearningGuidanceApplicationService>>,
    web::Data<Option<Arc<recommendation_core::infrastructure::scheduler::TaskScheduler>>>,
    web::Data<Option<Arc<recommendation_core::services::knowledge_tree_with_abilities::KnowledgeTreeWithAbilitiesService>>>,
    web::Data<Option<Arc<recommendation_core::application::knowledge::KnowledgeApplicationService>>>,
);

/// 将服务结构体转换为Web数据格式
pub fn to_web_data(services: ServiceData) -> WebServiceData {
    (
        web::Data::new(services.ability_service.clone()),
        web::Data::new(services.knowledge_service.clone()),
        web::Data::new(services.storage_manager.clone()),
        web::Data::new(services.nacos_service.clone()),
        web::Data::new(services.user_service.clone()),
        web::Data::new(services.mastery_v2_service.clone()),
        web::Data::new(services.new_recommendation_service.clone()),
        web::Data::new(services.question_service.clone()),
        web::Data::new(services.flashcard_recommendation_service.clone()),
        web::Data::new(services.flashcard_service.clone()),
        web::Data::new(services.progress_repository.clone()),
        web::Data::new(services.answer_history_repository.clone()),
        web::Data::new(services.card_repository.clone()),
        web::Data::new(services.plan_service.clone()),
        web::Data::new(services.plan_facade_service.clone()),
        web::Data::new(services.exam_service.clone()),
        web::Data::new(services.user_data_aggregation_service.clone()),
        web::Data::new(services.user_learning_guidance_repository.clone()),
        web::Data::new(services.personalized_learning_guidance_service.clone()),
        web::Data::new(services.task_scheduler.clone()),
        web::Data::new(services.knowledge_tree_with_abilities_service.clone()),
        web::Data::new(services.knowledge_application_service.clone()),
    )
}
