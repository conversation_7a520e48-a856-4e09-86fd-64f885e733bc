//! 题库接口处理器

use actix_web::{web, HttpResponse, Result as ActixResult};
use serde::{Deserialize, Serialize, Deserializer};
use std::sync::Arc;
use std::time::Instant;
use tracing::{debug, info, error};
use utoipa::ToSchema;

use crate::services::types::ServiceData;

use recommendation_core::application::knowledge::{
    KnowledgeApplicationService,
    KnowledgeTreeQueryDto,
    KnowledgeTreeResponseDto
};
use recommendation_core::application::question::question_bank_service::{
    GenerateType,
    Subject,
};


use crate::http::response::{success, from_error};
use crate::error::ApiError;
use crate::handlers::common::{FlexibleUserId, FlexibleQuestionId};

/// 自定义反序列化函数，支持 section_ids 接受数组或逗号分隔的字符串
fn deserialize_section_ids<'de, D>(deserializer: D) -> Result<Vec<i32>, D::Error>
where
    D: Deserializer<'de>,
{
    use serde::de::{self, Visitor};
    use std::fmt;

    struct SectionIdsVisitor;

    impl<'de> Visitor<'de> for SectionIdsVisitor {
        type Value = Vec<i32>;

        fn expecting(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
            formatter.write_str("an array of integers or a comma-separated string")
        }

        fn visit_seq<A>(self, mut seq: A) -> Result<Self::Value, A::Error>
        where
            A: de::SeqAccess<'de>,
        {
            let mut vec = Vec::new();
            while let Some(value) = seq.next_element::<i32>()? {
                vec.push(value);
            }
            Ok(vec)
        }

        fn visit_str<E>(self, value: &str) -> Result<Self::Value, E>
        where
            E: de::Error,
        {
            value
                .split(',')
                .map(|s| s.trim().parse::<i32>().map_err(de::Error::custom))
                .collect()
        }
    }

    deserializer.deserialize_any(SectionIdsVisitor)
}

/// 题库知识树查询请求参数
#[derive(Debug, Deserialize, Serialize, ToSchema)]
pub struct QuestionBankKnowledgeTreeRequest {
    /// 用户ID（必需）
    pub user_id: i64,
    /// 学科（可选）：math 或 reading，不传时查询两个科目的最新session
    pub subject: Option<String>,
    /// 学科ID（可选）
    pub subject_id: Option<i32>,
    /// 最大深度（可选，默认4）
    pub max_depth: Option<i32>,
    /// 是否排除测试学科（可选，默认true）
    pub exclude_test_subjects: Option<bool>,
}

/// SAT 题目选择请求参数
#[derive(Debug, Deserialize, Serialize, ToSchema)]
pub struct SatQuestionSelectRequest {
    /// 用户ID（必需）- 支持字符串和整数格式
    #[schema(value_type = i64)]
    pub user_id: FlexibleUserId,
    /// 小节ID列表 - 支持数组格式 [1,2,3] 或字符串格式 "1,2,3"
    #[serde(deserialize_with = "deserialize_section_ids")]
    #[schema(value_type = Vec<i32>)]
    pub section_ids: Vec<i32>,
    /// 选择类型
    pub generate_type: GenerateType,
    /// 题目数量（10/22/27）
    pub question_count: i32,
    /// 学科
    pub subject: Subject,
}

/// SAT 后续题目请求参数
#[derive(Debug, Deserialize, Serialize, ToSchema)]
pub struct SatNextQuestionsRequest {
    /// 会话ID
    pub session_id: String,
    /// 当前题目ID - 支持字符串和整数格式
    #[schema(value_type = i32)]
    pub current_question_id: FlexibleQuestionId,
    /// 需要获取的题目数量，默认10题
    pub count: Option<i32>,
}

impl From<QuestionBankKnowledgeTreeRequest> for KnowledgeTreeQueryDto {
    fn from(request: QuestionBankKnowledgeTreeRequest) -> Self {
        Self {
            user_id: Some(request.user_id),
            subject: request.subject,
            subject_id: request.subject_id,
            max_depth: request.max_depth,
            exclude_test_subjects: request.exclude_test_subjects,
        }
    }
}



/// 获取题库知识树
///
/// 获取用于题库出题的知识树结构，支持根据用户最新session设置选中状态
///
/// ## 功能特性
/// - 支持按学科过滤（math/reading）
/// - 支持设置最大深度（1-4层）
/// - 根据用户最新session设置is_selected状态
/// - 不传subject时查询所有科目的最新session
///
/// ## 参数说明
/// - `user_id`: 用户ID，用于查询最新session
/// - `subject`: 学科过滤，可选值：math, reading
/// - `max_depth`: 知识树最大深度，默认4
/// - `exclude_test_subjects`: 是否排除测试学科，默认true
#[utoipa::path(
    get,
    path = "/api/v1/question-bank/knowledge-tree",
    tag = "question-bank",
    params(
        ("user_id" = i64, Query, description = "用户ID，用于查询最新session状态"),
        ("subject" = Option<String>, Query, description = "学科过滤，可选值：math, reading。不传时查询所有科目"),
        ("max_depth" = Option<i32>, Query, description = "知识树最大深度，范围1-4，默认4"),
        ("exclude_test_subjects" = Option<bool>, Query, description = "是否排除测试学科，默认true")
    ),
    responses(
        (status = 200, description = "成功获取题库知识树", body = KnowledgeTreeResponseDto,
            example = json!({
                "code": 0,
                "msg": "操作成功",
                "data": {
                    "subjects": [
                        {
                            "id": 1,
                            "name": "Math",
                            "code": "1001",
                            "description": "数学",
                            "chapters": [
                                {
                                    "id": 1,
                                    "subject_id": 1,
                                    "name": "Algebra",
                                    "code": "ALG",
                                    "description": "代数",
                                    "sections": [
                                        {
                                            "id": 1,
                                            "chapter_id": 1,
                                            "name": "Linear Equations",
                                            "code": "LE",
                                            "description": "线性方程",
                                            "is_selected": true,
                                            "knowledge_points": []
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            })
        ),
        (status = 400, description = "请求参数错误"),
        (status = 500, description = "服务器内部错误")
    )
)]
pub async fn get_question_bank_knowledge_tree(
    query: web::Query<QuestionBankKnowledgeTreeRequest>,
    service: web::Data<Option<Arc<KnowledgeApplicationService>>>,
) -> ActixResult<HttpResponse, ApiError> {
    let start_time = Instant::now();

    debug!("收到题库知识树查询请求: {:?}", query);

    // 验证max_depth参数范围
    if let Some(max_depth) = query.max_depth {
        if max_depth < 1 || max_depth > 4 {
            error!("max_depth参数超出范围: {}, 有效范围: 1-4", max_depth);
            return Err(ApiError::Validation(format!("max_depth参数必须在1-4范围内，当前值: {}", max_depth)));
        }
    }

    // 检查服务是否可用
    let knowledge_service = match service.as_ref() {
        Some(service) => service,
        None => {
            error!("知识应用服务未配置，请检查服务初始化配置");
            return Err(ApiError::ServiceUnavailable("知识应用服务未配置，请联系管理员检查服务配置".to_string()));
        }
    };

    // 转换请求参数
    let query_dto: KnowledgeTreeQueryDto = query.into_inner().into();

    // 调用应用服务
    match knowledge_service.get_knowledge_tree(query_dto).await {
        Ok(response) => {
            let elapsed = start_time.elapsed();
            info!(
                "题库知识树查询成功，返回{}个学科，耗时: {:?}",
                response.subjects.len(),
                elapsed
            );
            Ok(success(response))
        }
        Err(e) => {
            error!("题库知识树查询失败: {}", e);
            Ok(from_error::<KnowledgeTreeResponseDto>(&e))
        }
    }
}



/// 生成 SAT 题库题目
///
/// 根据指定的小节和生成类型创建题目集合，支持随机和自适应两种模式
///
/// ## 生成类型
/// - `random`: 随机选择题目
/// - `adaptive`: 基于用户能力自适应选择题目
///
/// ## 学科支持
/// - `math`: 数学题目
/// - `reading`: 阅读题目
#[utoipa::path(
    post,
    path = "/api/v1/question-bank/generate",
    tag = "question-bank",
    request_body = SatQuestionSelectRequest,
    responses(
        (status = 200, description = "成功生成题目集合",
            example = json!({
                "code": 0,
                "msg": "操作成功",
                "data": {
                    "session_id": "qb_random_20250716_001",
                    "question_ids": [123, 124, 125, 126, 127],
                    "total_count": 5,
                    "generate_type": "random",
                    "subject": "math",
                    "section_ids": [1, 2, 3],
                    "created_at": "2025-07-16T12:00:00Z"
                }
            })
        ),
        (status = 400, description = "请求参数错误"),
        (status = 404, description = "指定小节下没有足够的题目"),
        (status = 500, description = "服务器内部错误")
    )
)]
pub async fn get_question_bank_recommendations(
    request: web::Json<SatQuestionSelectRequest>,
    service_data: web::Data<ServiceData>,
) -> ActixResult<HttpResponse, ApiError> {
    debug!("收到 SAT 题目选择请求: {:?}", request);

    // 直接从服务数据中获取题库服务
    let question_bank_service = &service_data.question_bank_service;

    let req = request.into_inner();

    // 调用应用服务选择 SAT 题目
    match question_bank_service.get_question_bank_recommendations(
        Some(req.user_id.as_i64()),
        req.section_ids,
        req.generate_type,
        req.question_count,
        req.subject,
    ).await {
        Ok(response) => {
            info!("SAT 题目选择完成，返回{}道题目", response.total_count);

            // 转换题目格式为 ApiQuestionContent，与推荐接口的题目格式保持一致
            let api_questions: Vec<crate::models::question::ApiQuestionContent> = response.questions
                .into_iter()
                .map(crate::models::question::ApiQuestionContent::from_question_dto)
                .collect();

            // 创建一个新的响应，将 questions 替换为转换后的格式
            let response_data = serde_json::json!({
                "session_id": response.session_id,
                "questions": api_questions,
                "current_count": response.current_count,
                "total_count": response.total_count,
                "generate_type": response.generate_type,
                "subject": response.subject
            });

            Ok(success(response_data))
        }
        Err(e) => {
            error!("SAT 题目选择失败: {}", e);

            // 根据错误类型返回合适的HTTP响应
            let api_error = match e {
                recommendation_core::error::Error::InvalidInput(msg) => {
                    ApiError::Validation(msg)
                }
                recommendation_core::error::Error::NotFound(msg) => {
                    ApiError::NotFound(msg)
                }
                _ => {
                    ApiError::Internal(format!("SAT 题目选择失败: {}", e))
                }
            };

            Err(api_error)
        }
    }
}

/// 获取 SAT 后续题目
///
/// 基于已生成的题目会话，获取后续的题目内容
///
/// ## 功能说明
/// - 根据会话ID和当前题目位置获取后续题目
/// - 支持指定获取数量，默认10题
/// - 保持会话状态的连续性
#[utoipa::path(
    post,
    path = "/api/v1/question-bank/next",
    tag = "question-bank",
    request_body = SatNextQuestionsRequest,
    responses(
        (status = 200, description = "成功获取后续题目",
            example = json!({
                "code": 0,
                "msg": "操作成功",
                "data": {
                    "session_id": "qb_random_20250716_001",
                    "questions": [
                        {
                            "id": "126",
                            "subject_id": 1,
                            "knowledge_id": 42,
                            "type_id": 1,
                            "difficulty": 3.0,
                            "question_content": {
                                "content": "题目内容",
                                "metadata": {
                                    "content_type": "stem",
                                    "style": ["p"]
                                },
                                "type": "string"
                            },
                            "options": [
                                {
                                    "identifier": "A",
                                    "content": [{"content": "选项A", "type": "string"}],
                                    "is_correct": false
                                }
                            ],
                            "answer": {"identifier": "A"},
                            "explanation": [{"content": "解析内容", "type": "string"}],
                            "elo_rating": 1500.0,
                            "usage_count": 0,
                            "correct_count": 0,
                            "is_active": true
                        }
                    ],
                    "current_count": 1,
                    "remaining_count": 11,
                    "has_more": true
                }
            })
        ),
        (status = 400, description = "请求参数错误"),
        (status = 404, description = "会话不存在或已过期"),
        (status = 500, description = "服务器内部错误")
    )
)]
pub async fn get_next_sat_questions(
    request: web::Json<SatNextQuestionsRequest>,
    service_data: web::Data<ServiceData>,
) -> ActixResult<HttpResponse, ApiError> {
    debug!("收到 SAT 后续题目请求: {:?}", request);

    // 直接从服务数据中获取题库服务
    let question_bank_service = &service_data.question_bank_service;

    // 调用应用服务获取后续题目
    match question_bank_service.get_next_questions(
        request.session_id.clone(),
        request.current_question_id.as_i32(),
        request.count,
    ).await {
        Ok(response) => {
            info!(
                "SAT 后续题目获取成功: session_id={}, 返回{}题",
                request.session_id, response.current_count
            );

            // 转换题目格式为 ApiQuestionContent，与 generate 接口的题目格式保持一致
            let api_questions: Vec<crate::models::question::ApiQuestionContent> = response.questions
                .into_iter()
                .map(crate::models::question::ApiQuestionContent::from_question_dto)
                .collect();

            // 创建一个新的响应，将 questions 替换为转换后的格式
            let response_data = serde_json::json!({
                "session_id": response.session_id,
                "questions": api_questions,
                "current_count": response.current_count,
                "remaining_count": response.remaining_count,
                "has_more": response.has_more
            });

            Ok(success(response_data))
        }
        Err(e) => {
            error!("SAT 后续题目获取失败: {}", e);

            // 根据错误类型返回合适的HTTP响应
            let api_error = match e {
                recommendation_core::error::Error::InvalidInput(msg) => {
                    ApiError::Validation(msg)
                }
                recommendation_core::error::Error::NotFound(msg) => {
                    ApiError::NotFound(msg)
                }
                _ => {
                    ApiError::Internal(format!("SAT 后续题目获取失败: {}", e))
                }
            };

            Err(api_error)
        }
    }
}
