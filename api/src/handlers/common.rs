//! 通用的 API 类型定义
//!
//! 包含在多个处理器中使用的通用类型

use serde::{Deserialize, Deserializer, Serialize};
use std::fmt;

/// 灵活的用户ID类型，支持字符串和整数
///
/// 这个类型可以从 JSON 中接受字符串或整数形式的用户ID，
/// 然后统一转换为内部的 i64 类型进行处理。
///
/// # 使用示例
///
/// ```rust
/// use serde::{Deserialize};
/// use recommendation_api::handlers::common::FlexibleUserId;
///
/// #[derive(Deserialize)]
/// struct MyRequest {
///     user_id: FlexibleUserId,
/// }
///
/// // 可以接受整数格式
/// let json1 = r#"{"user_id": 12345}"#;
/// let req1: MyRequest = serde_json::from_str(json1).unwrap();
/// assert_eq!(req1.user_id.as_i64(), 12345);
///
/// // 也可以接受字符串格式
/// let json2 = r#"{"user_id": "12345"}"#;
/// let req2: MyRequest = serde_json::from_str(json2).unwrap();
/// assert_eq!(req2.user_id.as_i64(), 12345);
/// ```
#[derive(Debug, Clone, PartialEq)]
pub struct FlexibleUserId {
    pub value: i64,
}

impl FlexibleUserId {
    /// 获取内部的 i64 值
    pub fn as_i64(&self) -> i64 {
        self.value
    }
}

impl fmt::Display for FlexibleUserId {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.value)
    }
}

impl<'de> Deserialize<'de> for FlexibleUserId {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: Deserializer<'de>,
    {
        use serde::de::{self, Visitor};

        struct FlexibleUserIdVisitor;

        impl<'de> Visitor<'de> for FlexibleUserIdVisitor {
            type Value = FlexibleUserId;

            fn expecting(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
                formatter.write_str("a string or integer representing user_id")
            }

            fn visit_i64<E>(self, value: i64) -> Result<Self::Value, E>
            where
                E: de::Error,
            {
                Ok(FlexibleUserId { value })
            }

            fn visit_u64<E>(self, value: u64) -> Result<Self::Value, E>
            where
                E: de::Error,
            {
                if value <= i64::MAX as u64 {
                    Ok(FlexibleUserId {
                        value: value as i64,
                    })
                } else {
                    Err(de::Error::custom(format!(
                        "user_id {} is too large for i64",
                        value
                    )))
                }
            }

            fn visit_str<E>(self, value: &str) -> Result<Self::Value, E>
            where
                E: de::Error,
            {
                value
                    .parse::<i64>()
                    .map(|v| FlexibleUserId { value: v })
                    .map_err(de::Error::custom)
            }
        }

        deserializer.deserialize_any(FlexibleUserIdVisitor)
    }
}

impl Serialize for FlexibleUserId {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        serializer.serialize_i64(self.value)
    }
}

/// 灵活的题目ID类型，支持字符串和整数
///
/// 这个类型可以从 JSON 中接受字符串或整数形式的题目ID，
/// 然后统一转换为内部的 i32 类型进行处理。
///
/// # 使用示例
///
/// ```rust
/// use serde::{Deserialize};
/// use recommendation_api::handlers::common::FlexibleQuestionId;
///
/// #[derive(Deserialize)]
/// struct MyRequest {
///     question_id: FlexibleQuestionId,
/// }
///
/// // 可以接受整数格式
/// let json1 = r#"{"question_id": 12345}"#;
/// let req1: MyRequest = serde_json::from_str(json1).unwrap();
/// assert_eq!(req1.question_id.as_i32(), 12345);
///
/// // 也可以接受字符串格式
/// let json2 = r#"{"question_id": "12345"}"#;
/// let req2: MyRequest = serde_json::from_str(json2).unwrap();
/// assert_eq!(req2.question_id.as_i32(), 12345);
/// ```
#[derive(Debug, Clone, PartialEq)]
pub struct FlexibleQuestionId {
    pub value: i32,
}

impl FlexibleQuestionId {
    /// 获取内部的 i32 值
    pub fn as_i32(&self) -> i32 {
        self.value
    }
}

impl fmt::Display for FlexibleQuestionId {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.value)
    }
}

impl<'de> Deserialize<'de> for FlexibleQuestionId {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: Deserializer<'de>,
    {
        use serde::de::{self, Visitor};

        struct FlexibleQuestionIdVisitor;

        impl<'de> Visitor<'de> for FlexibleQuestionIdVisitor {
            type Value = FlexibleQuestionId;

            fn expecting(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
                formatter.write_str("a string or integer representing question_id")
            }

            fn visit_i32<E>(self, value: i32) -> Result<Self::Value, E>
            where
                E: de::Error,
            {
                Ok(FlexibleQuestionId { value })
            }

            fn visit_i64<E>(self, value: i64) -> Result<Self::Value, E>
            where
                E: de::Error,
            {
                if value >= i32::MIN as i64 && value <= i32::MAX as i64 {
                    Ok(FlexibleQuestionId {
                        value: value as i32,
                    })
                } else {
                    Err(de::Error::custom(format!(
                        "question_id {} is out of range for i32",
                        value
                    )))
                }
            }

            fn visit_u64<E>(self, value: u64) -> Result<Self::Value, E>
            where
                E: de::Error,
            {
                if value <= i32::MAX as u64 {
                    Ok(FlexibleQuestionId {
                        value: value as i32,
                    })
                } else {
                    Err(de::Error::custom(format!(
                        "question_id {} is too large for i32",
                        value
                    )))
                }
            }

            fn visit_str<E>(self, value: &str) -> Result<Self::Value, E>
            where
                E: de::Error,
            {
                value
                    .parse::<i32>()
                    .map(|v| FlexibleQuestionId { value: v })
                    .map_err(de::Error::custom)
            }
        }

        deserializer.deserialize_any(FlexibleQuestionIdVisitor)
    }
}

impl Serialize for FlexibleQuestionId {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        serializer.serialize_i32(self.value)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json;

    #[test]
    fn test_flexible_user_id_from_integer() {
        let json = r#"{"user_id": 12345}"#;

        #[derive(Deserialize)]
        struct TestStruct {
            user_id: FlexibleUserId,
        }

        let result: TestStruct = serde_json::from_str(json).unwrap();
        assert_eq!(result.user_id.as_i64(), 12345);
    }

    #[test]
    fn test_flexible_user_id_from_string() {
        let json = r#"{"user_id": "12345"}"#;

        #[derive(Deserialize)]
        struct TestStruct {
            user_id: FlexibleUserId,
        }

        let result: TestStruct = serde_json::from_str(json).unwrap();
        assert_eq!(result.user_id.as_i64(), 12345);
    }

    #[test]
    fn test_flexible_user_id_from_negative_string() {
        let json = r#"{"user_id": "-12345"}"#;

        #[derive(Deserialize)]
        struct TestStruct {
            user_id: FlexibleUserId,
        }

        let result: TestStruct = serde_json::from_str(json).unwrap();
        assert_eq!(result.user_id.as_i64(), -12345);
    }

    #[test]
    fn test_flexible_user_id_invalid_string() {
        let json = r#"{"user_id": "not_a_number"}"#;

        #[derive(Deserialize)]
        struct TestStruct {
            #[allow(dead_code)]
            user_id: FlexibleUserId,
        }

        let result: Result<TestStruct, _> = serde_json::from_str(json);
        assert!(result.is_err());
    }

    #[test]
    fn test_flexible_user_id_serialization() {
        #[derive(Serialize)]
        struct TestStruct {
            user_id: FlexibleUserId,
        }

        let test_struct = TestStruct {
            user_id: FlexibleUserId { value: 12345 },
        };

        let json = serde_json::to_string(&test_struct).unwrap();
        assert_eq!(json, r#"{"user_id":12345}"#);
    }

    #[test]
    fn test_flexible_user_id_display() {
        let user_id = FlexibleUserId { value: 12345 };
        assert_eq!(format!("{}", user_id), "12345");
    }

    #[test]
    fn test_flexible_question_id_from_integer() {
        let json = r#"{"question_id": 12345}"#;

        #[derive(Deserialize)]
        struct TestStruct {
            question_id: FlexibleQuestionId,
        }

        let result: TestStruct = serde_json::from_str(json).unwrap();
        assert_eq!(result.question_id.as_i32(), 12345);
    }

    #[test]
    fn test_flexible_question_id_from_string() {
        let json = r#"{"question_id": "12345"}"#;

        #[derive(Deserialize)]
        struct TestStruct {
            question_id: FlexibleQuestionId,
        }

        let result: TestStruct = serde_json::from_str(json).unwrap();
        assert_eq!(result.question_id.as_i32(), 12345);
    }

    #[test]
    fn test_flexible_question_id_from_negative_string() {
        let json = r#"{"question_id": "-12345"}"#;

        #[derive(Deserialize)]
        struct TestStruct {
            question_id: FlexibleQuestionId,
        }

        let result: TestStruct = serde_json::from_str(json).unwrap();
        assert_eq!(result.question_id.as_i32(), -12345);
    }

    #[test]
    fn test_flexible_question_id_invalid_string() {
        let json = r#"{"question_id": "not_a_number"}"#;

        #[derive(Deserialize)]
        struct TestStruct {
            #[allow(dead_code)]
            question_id: FlexibleQuestionId,
        }

        let result: Result<TestStruct, _> = serde_json::from_str(json);
        assert!(result.is_err());
    }

    #[test]
    fn test_flexible_question_id_serialization() {
        #[derive(Serialize)]
        struct TestStruct {
            question_id: FlexibleQuestionId,
        }

        let test_struct = TestStruct {
            question_id: FlexibleQuestionId { value: 12345 },
        };

        let json = serde_json::to_string(&test_struct).unwrap();
        assert_eq!(json, r#"{"question_id":12345}"#);
    }

    #[test]
    fn test_flexible_question_id_display() {
        let question_id = FlexibleQuestionId { value: 12345 };
        assert_eq!(format!("{}", question_id), "12345");
    }

    #[test]
    fn test_flexible_question_id_large_number() {
        let json = r#"{"question_id": "1388700"}"#;

        #[derive(Deserialize)]
        struct TestStruct {
            question_id: FlexibleQuestionId,
        }

        let result: TestStruct = serde_json::from_str(json).unwrap();
        assert_eq!(result.question_id.as_i32(), 1388700);
    }
}
