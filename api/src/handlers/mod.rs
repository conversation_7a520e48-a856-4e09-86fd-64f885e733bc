//! API处理器模块
//!
//! 包含所有API路由处理函数

// 子模块
pub mod ability;
pub mod common; // 通用类型定义
pub mod conversation; // 对话模块
pub mod elo;
pub mod flash_card;
pub mod health; // 健康检查模块
pub mod knowledge; // 这里导入知识模块而不是knowledge.rs文件
pub mod question_bank; // 题库模块
pub mod knowledge_tree_with_abilities; // 知识树与能力整合模块
// 已删除：mastery 和 mastery_v2 模块 - 功能已迁移到 recommendations 模块
pub mod plan; // 新版学习计划模块
pub mod question; // 题目查询模块，使用目录结构
pub mod recommendations; // 新版推荐模块，替代v2/recommendations
pub mod user; // 添加用户模块
pub mod user_segmentation;
pub mod exam; // 用户分群模块

// 不再需要configure函数，路由由router模块统一管理
