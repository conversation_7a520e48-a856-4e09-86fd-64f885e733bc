//! 考试路由器
//!
//! 提供考试相关的HTTP路由配置

use actix_web::web;
use crate::router::traits::Router;
use crate::handlers::exam;

/// 考试路由器
pub struct ExamRouter {
    prefix: String,
}

impl ExamRouter {
    /// 创建新的考试路由器
    pub fn new(prefix: &str) -> Self {
        Self {
            prefix: prefix.to_string(),
        }
    }
}

impl Router for ExamRouter {
    fn configure_routes(&self, cfg: &mut web::ServiceConfig) {
        // 配置考试相关路由
        cfg.service(
            web::scope(&format!("{}/exam", self.prefix))
                // ==================== 优化接口 ====================
                // 考试准备（合并接口）
                .route("/prepare", web::post().to(exam::prepare_exam))

                // 模块切换（合并接口）
                .route("/module/transition", web::post().to(exam::transition_module))

                // 增强答题提交
                .route("/answer/submit-enhanced", web::post().to(exam::submit_answer_enhanced))

                // 综合状态查询
                .route("/state/comprehensive", web::get().to(exam::get_comprehensive_state))

                // ==================== 原有接口（保持兼容） ====================
                // 用户状态路由
                .route("/user/status", web::get().to(exam::get_user_status))

                // 考试指引路由
                .route("/guide", web::get().to(exam::get_exam_guide))

                // 会话管理路由
                .route("/session/create", web::post().to(exam::create_session))
                .route("/session/resume", web::get().to(exam::resume_session))
                .route("/session/cancel", web::post().to(exam::cancel_session))
                .route("/session/state", web::get().to(exam::get_session_state))
                .route("/session/validate", web::get().to(exam::validate_session))

                // 模块管理路由
                .route("/module/start", web::post().to(exam::start_module))
                .route("/module/submit", web::post().to(exam::submit_module))

                // 答题管理路由
                .route("/answer/submit", web::post().to(exam::submit_answer))
                .route("/answer/history", web::get().to(exam::get_answer_history))

                // 考试提交路由
                .route("/submit", web::post().to(exam::submit_exam))

                // ==================== 考试记录功能 ====================
                // 获取考试记录
                .route("/records", web::get().to(exam::exam_record_handlers::get_exam_records))

                // 获取指定试卷考试记录（不分页）
                .route("/records/paper", web::post().to(exam::exam_record_handlers::get_paper_exam_records))

                // 重新计算统计信息
                .route("/records/recalculate", web::post().to(exam::exam_record_handlers::recalculate_user_statistics))
        );
    }
}
