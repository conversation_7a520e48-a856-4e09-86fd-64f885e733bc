//! 路由模块
//!
//! 集中管理所有API路由配置，提供统一的接口注册方式

// 移除旧的模块引用
// mod api_v1;
// mod modules;
// mod elo;

// 引入新的 Router 模块
mod ability_router;
mod conversation_router; // 对话路由模块
mod elo_router;
mod exam_router; // 考试路由模块
mod flashcard_router;
mod health_router; // 新增：健康检查路由模块
mod knowledge_router;
mod question_bank_router; // 题库路由模块
mod knowledge_tree_with_abilities_router; // 知识树与能力整合路由模块
// 已删除：mastery_router 和 mastery_v2_router - 功能已迁移到 recommendation_router
mod plan_router; // 新版学习计划路由模块
mod question_router;
mod recommendation_router;
mod traits;
mod user_router;
mod user_segmentation_router; // 用户分群路由模块

use crate::docs::ApiDoc;
use crate::docs::welcome_page;
use crate::handlers; // 引入handlers模块
use actix_web::web;
use once_cell::sync::Lazy;
use recommendation_core::logging::ApiLogger;
use recommendation_core::log_debug;
use utoipa::OpenApi;
use utoipa_swagger_ui::SwaggerUi;

// 创建模块级日志记录器
static _ROUTER_LOGGER: Lazy<ApiLogger> = Lazy::new(|| ApiLogger::new("router"));

// 引入所有 Router 实现
use self::ability_router::AbilityRouter;
use self::conversation_router::ConversationRouter; // 引入对话路由器
use self::elo_router::EloRouter;
use self::exam_router::ExamRouter; // 引入考试路由器
use self::flashcard_router::FlashcardRouter;
use self::health_router::HealthRouter; // 引入 HealthRouter
use self::knowledge_router::KnowledgeRouter;
use self::question_bank_router::QuestionBankRouter; // 引入题库路由器
use self::knowledge_tree_with_abilities_router::KnowledgeTreeWithAbilitiesRouter; // 引入知识树与能力整合路由器
// 已删除：MasteryRouter 和 MasteryV2Router - 功能已迁移到 RecommendationRouter
use self::plan_router::PlanRouter;
use self::question_router::QuestionRouter;
use self::recommendation_router::RecommendationRouter;
use self::traits::Router;
use self::user_router::UserRouter;
use self::user_segmentation_router::UserSegmentationRouter;

// 定义 API V1 前缀常量
const API_V1_PREFIX: &str = "/api/v1";

/// 注册所有API路由
///
/// 统一配置API的路由注册，包括API文档、健康检查等基础路由
pub fn configure(cfg: &mut web::ServiceConfig) {
    // 注册API文档
    let openapi = ApiDoc::openapi();
    cfg.service(welcome_page)
        .service(SwaggerUi::new("/swagger-ui/{_:.*}").url("/api-docs/openapi.json", openapi));

    // 注册根路径下的健康检查，与 API 版本无关
    // 使用处理器模块中的函数，保持所有健康检查一致
    cfg.route("/health", web::get().to(handlers::health::health_check));

    // --- 注册所有领域 Router ---
    let routers: Vec<Box<dyn Router>> = vec![
        Box::new(HealthRouter::new(API_V1_PREFIX)), // 添加 HealthRouter
        Box::new(EloRouter::new(API_V1_PREFIX)),
        Box::new(AbilityRouter::new(API_V1_PREFIX)),
        Box::new(ConversationRouter::new(API_V1_PREFIX)), // 添加对话路由器
        Box::new(ExamRouter::new(API_V1_PREFIX)), // 添加考试路由器
        Box::new(RecommendationRouter::new(API_V1_PREFIX)),
        // 已删除：MasteryRouter 和 MasteryV2Router - 功能已迁移到 RecommendationRouter
        Box::new(UserRouter::new(API_V1_PREFIX)),
        Box::new(QuestionRouter::new(API_V1_PREFIX)),
        Box::new(FlashcardRouter::new(API_V1_PREFIX)), // 添加闪卡路由
        Box::new(UserSegmentationRouter::new(API_V1_PREFIX)), // 添加用户分群路由
        Box::new(PlanRouter::new(API_V1_PREFIX)), // 添加新版学习计划路由（必须在KnowledgeRouter之前）
        Box::new(QuestionBankRouter::new(API_V1_PREFIX)), // 添加题库路由
        Box::new(KnowledgeTreeWithAbilitiesRouter::new(API_V1_PREFIX)), // 添加知识树与能力整合路由
        Box::new(KnowledgeRouter::new(API_V1_PREFIX)), // 这个router的地址是api/v1，会覆盖之后所有api/v1/...的路由，所以必须放在最后

                                                       // 注意：根路径的健康检查已在上面单独注册
    ];

    // 遍历并配置每个 Router
    log_debug!(_ROUTER_LOGGER, "配置{}个领域路由器", routers.len());
    for router in routers.iter() {
        router.configure_routes(cfg);
    }

    log_debug!(_ROUTER_LOGGER, "路由配置完成");
    // 旧的模块化系统已完全移除
}

pub fn register_routers(config: &mut web::ServiceConfig, api_prefix: &str) {
    // 读取环境变量和配置参数
    let v1_prefix = format!("{}/v1", api_prefix.trim_end_matches('/'));

    // 创建路由器实例
    let health_router = health_router::HealthRouter::new(&v1_prefix);
    let ability_router = ability_router::AbilityRouter::new(&v1_prefix);
    let conversation_router = conversation_router::ConversationRouter::new(&v1_prefix); // 添加对话路由器
    let elo_router = elo_router::EloRouter::new(&v1_prefix);
    let knowledge_router = knowledge_router::KnowledgeRouter::new(&v1_prefix);
    // 已删除：mastery_v2_router - 功能已迁移到 recommendation_router
    let question_router = question_router::QuestionRouter::new(&v1_prefix);
    let recommendation_router = recommendation_router::RecommendationRouter::new(&v1_prefix);
    let user_router = user_router::UserRouter::new(&v1_prefix);
    // 创建闪卡路由实例
    let flashcard_router = flashcard_router::FlashcardRouter::new(&v1_prefix);

    // 注册路由
    health_router.configure_routes(config);
    ability_router.configure_routes(config);
    conversation_router.configure_routes(config); // 配置对话路由
    elo_router.configure_routes(config);
    knowledge_router.configure_routes(config);
    // 已删除：mastery_v2_router.configure_routes - 功能已迁移到 recommendation_router
    question_router.configure_routes(config);
    recommendation_router.configure_routes(config);
    user_router.configure_routes(config);
    // 配置闪卡路由
    flashcard_router.configure_routes(config);
    // 配置学习计划路由 - 已移除，使用新的PlanRouter
}
