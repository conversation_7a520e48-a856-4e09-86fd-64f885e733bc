mod common;

use common::{
    TestData, create_test_client, get_base_url, validate_standard_response,
};
use serde_json::json;

#[tokio::test]
async fn test_question_bank_generate() {
    let client = create_test_client();
    let url = format!("{}/question-bank/generate", get_base_url());

    let response = client
        .post(&url)
        .header("Content-Type", "application/json")
        .json(&json!({
            "section_ids": [1, 2],
            "generate_type": "random",
            "question_count": 22,
            "subject": "math",
            "user_id": TestData::user_id()
        }))
        .send()
        .await
        .expect("请求失败: POST /question-bank/generate");

    if response.status().is_success() {
        let json = validate_standard_response(response).await;

        // 打印完整响应JSON以便调试
        println!(
            "题库生成响应JSON: {}",
            serde_json::to_string_pretty(&json).unwrap()
        );
    } else {
        println!("题库生成接口返回非成功状态码: {}", response.status());
    }
}

#[tokio::test]
async fn test_question_bank_generate_with_string_section_ids() {
    let client = create_test_client();
    let url = format!("{}/question-bank/generate", get_base_url());

    // 测试字符串格式的 section_ids
    let response = client
        .post(&url)
        .header("Content-Type", "application/json")
        .json(&json!({
            "section_ids": "1,2",
            "generate_type": "random",
            "question_count": 22,
            "subject": "math",
            "user_id": TestData::user_id()
        }))
        .send()
        .await
        .expect("请求失败: POST /question-bank/generate with string section_ids");

    if response.status().is_success() {
        let json = validate_standard_response(response).await;

        // 打印完整响应JSON以便调试
        println!(
            "题库生成响应JSON (字符串section_ids): {}",
            serde_json::to_string_pretty(&json).unwrap()
        );

        // 验证数据结构
        let data = &json["data"];
        assert!(data.is_object(), "题库生成数据应为对象");
        
        // 验证必要字段存在
        assert!(data.get("session_id").is_some(), "题库生成响应缺少session_id字段");
        assert!(data.get("questions").is_some(), "题库生成响应缺少questions字段");
        assert!(data.get("current_count").is_some(), "题库生成响应缺少current_count字段");
        assert!(data.get("total_count").is_some(), "题库生成响应缺少total_count字段");
        assert!(data.get("generate_type").is_some(), "题库生成响应缺少generate_type字段");
        assert!(data.get("subject").is_some(), "题库生成响应缺少subject字段");

        // 验证题目数组
        if let Some(questions) = data.get("questions").and_then(|q| q.as_array()) {
            println!("返回的题目数量: {}", questions.len());
            
            // 验证current_count与实际返回的题目数量一致
            if let Some(current_count) = data.get("current_count").and_then(|c| c.as_i64()) {
                assert_eq!(current_count as usize, questions.len(), "current_count应与实际返回的题目数量一致");
            }
        }

        // 验证total_count字段 - 这是我们修改的核心部分
        if let Some(total_count) = data.get("total_count").and_then(|c| c.as_i64()) {
            println!("total_count: {}", total_count);
            
            // total_count应该反映实际选择的题目数量，而不是请求的数量
            // 由于我们请求了22题，但可能实际选择的题目数量不同
            assert!(total_count > 0, "total_count应该大于0");
            
            // 如果有题目返回，total_count应该大于等于current_count
            if let Some(current_count) = data.get("current_count").and_then(|c| c.as_i64()) {
                assert!(total_count >= current_count, "total_count应该大于等于current_count");
            }
        }

        // 验证generate_type和subject字段值
        if let Some(generate_type) = data.get("generate_type").and_then(|t| t.as_str()) {
            assert_eq!(generate_type, "random", "generate_type应与请求一致");
        }
        
        if let Some(subject) = data.get("subject").and_then(|s| s.as_str()) {
            assert_eq!(subject, "math", "subject应与请求一致");
        }

    } else {
        println!("题库生成接口返回非成功状态码: {}", response.status());
        // 对于测试环境，可能没有足够的数据，这是正常的
        if response.status().as_u16() == 404 {
            println!("测试环境可能没有足够的题目数据，跳过验证");
        }
    }
}

#[tokio::test]
async fn test_question_bank_generate_with_10_questions() {
    let client = create_test_client();
    let url = format!("{}/question-bank/generate", get_base_url());

    let response = client
        .post(&url)
        .header("Content-Type", "application/json")
        .json(&json!({
            "section_ids": [1, 2],
            "generate_type": "random",
            "question_count": 10,
            "subject": "math",
            "user_id": TestData::user_id()
        }))
        .send()
        .await
        .expect("请求失败: POST /question-bank/generate");

    if response.status().is_success() {
        let json = validate_standard_response(response).await;

        println!(
            "10题生成响应JSON: {}",
            serde_json::to_string_pretty(&json).unwrap()
        );

        let data = &json["data"];
        
        // 对于10题的请求，total_count应该等于实际选择的题目数量
        if let Some(total_count) = data.get("total_count").and_then(|c| c.as_i64()) {
            if let Some(current_count) = data.get("current_count").and_then(|c| c.as_i64()) {
                println!("10题请求 - total_count: {}, current_count: {}", total_count, current_count);
                
                // 对于10题的请求，如果题目充足，total_count应该等于current_count
                // 因为所有题目都会在第一次返回
                if total_count <= 10 {
                    assert_eq!(total_count, current_count, "对于10题或更少的情况，total_count应该等于current_count");
                }
            }
        }
    } else {
        println!("10题生成接口返回非成功状态码: {}", response.status());
    }
}

#[tokio::test]
async fn test_question_bank_generate_single_string_section_id() {
    let client = create_test_client();
    let url = format!("{}/question-bank/generate", get_base_url());

    // 测试单个数字字符串（不含逗号）
    let response = client
        .post(&url)
        .header("Content-Type", "application/json")
        .json(&json!({
            "section_ids": "160",  // 单个数字，不含逗号
            "generate_type": "random",
            "question_count": 10,
            "subject": "math",
            "user_id": TestData::user_id()
        }))
        .send()
        .await
        .expect("请求失败: POST /question-bank/generate with single string section_id");

    if response.status().is_success() {
        let json = validate_standard_response(response).await;
        println!(
            "单个字符串section_id响应JSON: {}",
            serde_json::to_string_pretty(&json).unwrap()
        );
    } else {
        println!("单个字符串section_id接口返回非成功状态码: {}", response.status());
    }
}

#[tokio::test]
async fn test_question_bank_generate_invalid_string_section_id() {
    let client = create_test_client();
    let url = format!("{}/question-bank/generate", get_base_url());

    // 测试无效的字符串格式
    let response = client
        .post(&url)
        .header("Content-Type", "application/json")
        .json(&json!({
            "section_ids": "160abc",  // 包含非数字字符
            "generate_type": "random",
            "question_count": 10,
            "subject": "math",
            "user_id": TestData::user_id()
        }))
        .send()
        .await
        .expect("请求失败: POST /question-bank/generate with invalid string section_id");

    // 这应该返回400错误
    if response.status().is_client_error() {
        println!("✅ 无效字符串section_id正确返回客户端错误: {}", response.status());
    } else {
        println!("⚠️ 无效字符串section_id返回状态码: {}", response.status());
    }
}
