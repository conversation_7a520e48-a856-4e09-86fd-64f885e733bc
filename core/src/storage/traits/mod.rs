//! 存储traits模块
//!
//! 定义各种存储特征接口
//! 这个是旧的框架
//! 新的内容不要使用这里的任何东西！！！！！

// 导出所有子模块
pub mod ability;
pub mod base;
pub mod knowledge_elo;
// pub mod search; // 暂时移除，模块不存在

pub mod knowledge_service;
// pub mod question; // 已删除，请使用 domain::question::repository::QuestionRepository
pub mod student;

// 导出ELO相关存储接口
pub mod answer;
pub mod combined;
pub mod elo_factors;
pub mod elo_history;
pub mod knowledge_encounter;
pub mod streak;

// 添加学生知识点学习进度存储接口
pub mod student_knowledge_progress;

// 添加闪卡仓库接口
pub mod flash_card_repository;

// 重导出基础traits
pub use ability::*;
pub use answer::*;
pub use base::*;
pub use knowledge_elo::*;
// pub use question::*; // 已删除，请使用 domain::question::repository::QuestionRepository
// pub use search::*; // 暂时移除，模块不存在
pub use student::*;

// 重导出所有接口特征
pub use combined::{CombinedEloStorage, EloStorage};
pub use elo_factors::EloFactorsStorage;
pub use elo_history::EloHistoryStorage;
pub use knowledge_encounter::KnowledgeEncounterStorage;
pub use streak::StreakStorage;

// 重导出所有接口，便于使用
pub use student_knowledge_progress::StudentKnowledgeProgressStorage;

pub mod knowledge;
pub use knowledge::KnowledgeRepository;

// 重导出闪卡仓库接口
pub use flash_card_repository::FlashCardRepository;

pub mod ability_view;
pub use ability_view::AbilityView;
