//! 组合存储接口
//!
//! 定义组合多种存储接口的复合Trait

use super::AbilityStorage;
use super::KnowledgeEloStorage;
use super::StudentAnswerStorage;
use async_trait::async_trait;

/// 组合知识点Elo和能力值存储的特征
///
/// 该特征同时继承KnowledgeEloStorage和AbilityStorage，
/// 用于解决Rust中trait对象不能有多个非自动trait的限制
#[async_trait]
pub trait CombinedEloStorage: KnowledgeEloStorage + AbilityStorage {
    // 不需要添加新方法，仅用作组合
}

// 为任何同时实现KnowledgeEloStorage和AbilityStorage的类型自动实现CombinedEloStorage
#[async_trait]
impl<T> CombinedEloStorage for T
where
    T: KnowledgeEloStorage + AbilityStorage + Send + Sync,
{
    // 空实现，因为所有方法都来自父特征
}

/// Elo服务所需的存储特征
///
/// 组合能力评估和答题记录存储功能
#[async_trait]
pub trait EloStorage: AbilityStorage + StudentAnswerStorage {
    // 不需要添加新方法，仅用作组合
}

// 为任何同时实现AbilityStorage和StudentAnswerStorage的类型自动实现EloStorage
#[async_trait]
impl<T> EloStorage for T
where
    T: AbilityStorage + StudentAnswerStorage + Send + Sync,
{
    // 空实现，因为所有方法都来自父特征
}

// 删除了未使用的 EnhancedEloStorage trait
// 该trait定义了但从未被实际使用
