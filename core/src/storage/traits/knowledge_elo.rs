//! 知识点Elo存储接口
//!
//! 提供对知识点特定Elo评分的存储访问接口

use super::base::Storage;
use crate::Error;
use crate::Result;
use crate::infrastructure::dto::question::QuestionEloInfo;
use async_trait::async_trait;

/// 知识点Elo存储特征
#[async_trait]
pub trait KnowledgeEloStorage: Storage {
    /// 获取知识点Elo评分
    async fn get_knowledge_elo(&self, _student_id: i64, _knowledge_id: i32) -> Result<Option<f64>> {
        Err(Error::service("未实现 get_knowledge_elo 方法".to_string()))
    }

    /// 设置知识点Elo评分
    async fn set_knowledge_elo(
        &self,
        _student_id: i64,
        _knowledge_id: i32,
        _rating: f64,
        _k_value: f64,
    ) -> Result<()> {
        Err(Error::service("未实现 set_knowledge_elo 方法".to_string()))
    }

    /// 获取题目在特定知识点的Elo评分
    async fn get_question_knowledge_elo(
        &self,
        _question_id: i32,
        _knowledge_id: i32,
    ) -> Result<Option<f64>> {
        Err(Error::service(
            "未实现 get_question_knowledge_elo 方法".to_string(),
        ))
    }

    /// 设置题目在特定知识点的Elo评分
    async fn set_question_knowledge_elo(
        &self,
        _question_id: i32,
        _knowledge_id: i32,
        _rating: f64,
    ) -> Result<()> {
        Err(Error::service(
            "未实现 set_question_knowledge_elo 方法".to_string(),
        ))
    }

    /// 获取学生在指定知识点的答题数量
    async fn get_knowledge_question_count(
        &self,
        _student_id: i64,
        _knowledge_id: i32,
    ) -> Result<u32> {
        Err(Error::service(
            "未实现 get_knowledge_question_count 方法".to_string(),
        ))
    }

    /// 增加学生在指定知识点的答题数量
    async fn increment_knowledge_question_count(
        &self,
        _student_id: i64,
        _knowledge_id: i32,
    ) -> Result<()> {
        Err(Error::service(
            "未实现 increment_knowledge_question_count 方法".to_string(),
        ))
    }

    /// 增加学生在指定知识点的正确答题数量
    async fn increment_knowledge_correct_count(
        &self,
        _student_id: i64,
        _knowledge_id: i32,
    ) -> Result<()> {
        Err(Error::service(
            "未实现 increment_knowledge_correct_count 方法".to_string(),
        ))
    }

    /// 获取知识点的所有题目ELO信息
    async fn get_knowledge_questions(&self, _knowledge_id: i32) -> Result<Vec<QuestionEloInfo>> {
        Err(Error::service(
            "未实现 get_knowledge_questions 方法".to_string(),
        ))
    }

    /// 检查学生是否已做过某题
    async fn has_student_answered(&self, _student_id: i64, _question_id: i32) -> Result<bool> {
        Err(Error::service(
            "未实现 has_student_answered 方法".to_string(),
        ))
    }
}
