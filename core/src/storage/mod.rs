//! 存储接口模块（已废弃，请使用domain层的repository接口）
//!
//! ⚠️  **重要提示：此模块已废弃！**
//!
//! 根据DDD架构原则，存储接口应该定义在domain层：
//! - 请使用 `crate::domain::*/repository.rs` 中的接口
//! - 此模块仅为向后兼容而保留
//! - 新代码请勿使用此模块中的接口
//!
//! 该模块原本定义了存储接口，允许系统与不同的存储后端集成，如：
//! - PostgreSQL 用于结构化数据存储和事务处理
//! - Elasticsearch 用于搜索和分析
//! - Redis 用于缓存
//! - 内存存储用于测试

use std::any::Any;
use std::fmt::Debug;

// 导出子模块
pub mod traits; // 存储接口实现
// 注意：implementations模块已迁移到infrastructure/persistence/storage_impl
// 注意：entities模块已迁移到infrastructure/database/models
// 注意：dto模块已迁移到infrastructure/dto

// 重导出entities以保持向后兼容
pub use crate::infrastructure::database::models as entities;

/// 存储类型
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum DatabaseType {
    /// PostgreSQL数据库
    Postgres,
    /// Elasticsearch搜索引擎
    Elasticsearch,
    /// 内存存储（用于测试）
    Memory,
    /// 文件存储
    File,
    /// 其他存储类型
    Other,
}

/// 包装任意StorageConfig的Box类型
///
/// 用于在运行时包装不同的配置类型，提供统一的接口。
#[derive(Debug)]
pub struct BoxedStorageConfig {
    name: String,
    db_type: DatabaseType,
    inner: Box<dyn traits::StorageConfig>,
}

impl BoxedStorageConfig {
    /// 从具体的StorageConfig类型创建包装器
    pub fn new<T: traits::StorageConfig>(config: T) -> Self {
        Self {
            name: config.name().to_string(),
            db_type: config.db_type(),
            inner: Box::new(config),
        }
    }

    /// 获取内部配置的引用
    pub fn inner(&self) -> &dyn traits::StorageConfig {
        self.inner.as_ref()
    }

    /// 获取数据库类型
    pub fn db_type(&self) -> DatabaseType {
        self.db_type
    }
}

impl traits::StorageConfig for BoxedStorageConfig {
    fn name(&self) -> &str {
        &self.name
    }

    fn db_type(&self) -> DatabaseType {
        self.db_type
    }

    fn as_any(&self) -> &dyn Any {
        self
    }
}

/// 题目搜索条件
#[derive(Debug, Clone)]
pub struct QuestionSearchCriteria {
    /// 关键词
    pub keywords: Option<String>,

    /// 难度范围
    pub difficulty_range: Option<(f64, f64)>,

    /// 主题
    pub topic: Option<String>,

    /// 标签
    pub tags: Vec<String>,
}

// 为兼容性保留的旧名称
pub type ProblemSearchCriteria = QuestionSearchCriteria;

// 重导出重要的类型（从新位置）
// 注意：SeaOrmQuestionStorage 已被删除，请使用新的 DDD 架构

// 导出ELO相关存储实现，使用模块而非所有类型
pub mod elo {
    pub use crate::infrastructure::persistence::storage_impl::elo::*;
}

// 重导出特定的trait和dto，避免名称冲突
pub use traits::ability::AbilityStorage;
pub use traits::answer::StudentAnswerStorage;
pub use traits::base::Storage;
pub use traits::base::StorageConfig;
pub use traits::combined::CombinedEloStorage;
pub use traits::elo_factors::EloFactorsStorage;
// 注意：QuestionStorage trait 已被删除，请使用 domain::question::repository::QuestionRepository

// 重导出DTO（从新位置）
pub use crate::infrastructure::dto as dto;
pub use crate::infrastructure::dto::ability;
pub use crate::infrastructure::dto::answer;
pub use crate::infrastructure::dto::question;
pub use crate::infrastructure::dto::question::QuestionEloInfo;
pub use crate::infrastructure::dto::student;
