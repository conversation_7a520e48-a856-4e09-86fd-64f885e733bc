//! 知识点Elo推荐功能
//!
//! 基于知识点Elo评分的题目推荐实现

use super::base::KnowledgeEloService;
use crate::Result;
use crate::services::elo::models::KnowledgeEloRecommendation;

impl KnowledgeEloService {
    /// 根据知识点Elo推荐题目
    pub async fn recommend_questions_by_knowledge_elo(
        &self,
        student_id: i64,
        knowledge_id: i32,
        target_probability: f64,
        limit: usize,
    ) -> Result<Vec<KnowledgeEloRecommendation>> {
        // 获取学生在该知识点的Elo评分
        let student_elo = self.get_knowledge_elo(student_id, knowledge_id).await?;

        // 获取该知识点的所有题目ELO信息
        let question_elo_infos = self.storage.get_knowledge_questions(knowledge_id).await?;

        // 计算每道题的成功概率，并找出最接近目标概率的题目
        let mut question_probabilities = Vec::new();

        for question_info in question_elo_infos {
            // 检查学生是否已经回答过此题目
            let question_id_int = question_info.question_id.parse::<i32>().unwrap_or_default();
            let answered = self
                .storage
                .has_student_answered(student_id, question_id_int)
                .await?;

            // 如果回答过，跳过此题目
            if answered {
                continue;
            }

            // 使用题目的ELO评分作为难度
            let question_difficulty = question_info.elo_rating;

            // 使用纯计算器计算成功概率
            let probability = self
                .calculator
                .predict_success_probability(student_elo, question_difficulty);

            // 计算与目标概率的差距
            let distance = (probability - target_probability).abs();

            // 添加到结果列表
            question_probabilities.push(KnowledgeEloRecommendation {
                question_id: question_id_int,
                success_probability: probability,
                student_elo,
                question_elo: question_difficulty,
                target_distance: distance,
            });
        }

        // 按照与目标概率的距离排序（距离越小越好）
        question_probabilities.sort_by(|a, b| {
            a.target_distance
                .partial_cmp(&b.target_distance)
                .unwrap_or(std::cmp::Ordering::Equal)
        });

        // 取出前limit个结果
        Ok(question_probabilities.into_iter().take(limit).collect())
    }

    /// 预测学生在指定知识点上解答特定题目的成功概率
    pub async fn predict_success_probability(
        &self,
        student_id: i64,
        knowledge_id: i32,
        question_id: i32,
    ) -> Result<f64> {
        // 获取学生知识点Elo评分
        let student_elo = self.get_knowledge_elo(student_id, knowledge_id).await?;

        // 获取题目在该知识点的Elo评分
        let question_elo = match self
            .storage
            .get_question_knowledge_elo(question_id, knowledge_id)
            .await?
        {
            Some(elo) => elo,
            None => {
                // 回退到全局题目难度
                match self
                    .storage
                    .get_question_elo(&question_id.to_string())
                    .await?
                {
                    Some(elo) => elo,
                    None => self.calculator.config.initial_rating,
                }
            }
        };

        // 使用纯计算器预测成功概率
        Ok(self
            .calculator
            .predict_success_probability(student_elo, question_elo))
    }
}
