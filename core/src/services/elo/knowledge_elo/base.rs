//! 知识点Elo服务基础实现
//!
//! 提供基础服务功能和依赖管理

use chrono::Utc;
use sea_orm::DatabaseConnection;
use std::sync::Arc;
use tracing::info;

use super::config::KnowledgeEloConfig;
use crate::Result;
use crate::algorithms::elo::calculator::StageCalculator;
use crate::algorithms::elo::types::EloFactors;
use crate::algorithms::elo::types::LearningStage;
use crate::algorithms::elo::{EloCalculator, EloConfig};
use crate::services::elo::models::StageCalculationResult;
use crate::storage::traits::{CombinedEloStorage, EloFactorsStorage};

/// 知识点Elo能力服务
#[derive(Debug)]
pub struct KnowledgeEloService {
    /// 数据库连接
    pub(crate) db: DatabaseConnection,

    /// 存储服务（使用组合trait）
    pub(crate) storage: Arc<dyn CombinedEloStorage + Send + Sync>,

    /// Elo配置
    #[allow(dead_code)]
    pub(crate) config: KnowledgeEloConfig,

    /// 纯计算器
    pub(crate) calculator: EloCalculator,

    /// 可选的因子存储，用于记录ELO因子
    pub(crate) factors_storage: Option<Arc<dyn EloFactorsStorage + Send + Sync>>,
}

impl KnowledgeEloService {
    /// 创建新的知识点Elo能力服务
    pub fn new(
        db: DatabaseConnection,
        storage: Arc<dyn CombinedEloStorage + Send + Sync>,
        config: KnowledgeEloConfig,
        factors_storage: Option<Arc<dyn EloFactorsStorage + Send + Sync>>,
    ) -> Self {
        // 创建纯计算器
        let calculator = EloCalculator::new(config.elo_config.clone());

        Self {
            db,
            storage,
            config,
            calculator,
            factors_storage,
        }
    }

    /// 创建新的知识点Elo能力服务（不带因子存储）
    pub fn new_without_factors(
        db: DatabaseConnection,
        storage: Arc<dyn CombinedEloStorage + Send + Sync>,
        config: KnowledgeEloConfig,
    ) -> Self {
        Self::new(db, storage, config, None)
    }

    /// 从EloConfig创建服务
    pub fn from_elo_config(
        db: DatabaseConnection,
        storage: Arc<dyn CombinedEloStorage + Send + Sync>,
        elo_config: &EloConfig,
        factors_storage: Option<Arc<dyn EloFactorsStorage + Send + Sync>>,
    ) -> Self {
        let config = KnowledgeEloConfig::from_global_config(elo_config);

        info!(
            "初始化知识点Elo服务 - 配置: initial={}, student_k={}, question_k={}, scale={}, min_k_ratio={}, dynamic_k={}, cache_ttl={}s",
            config.elo_config.initial_rating,
            config.elo_config.k_factor,
            config.elo_config.question_k_factor,
            config.elo_config.scale_factor,
            config.min_k_ratio,
            config.use_dynamic_k,
            config.cache_ttl_seconds
        );

        Self::new(db, storage, config, factors_storage)
    }

    /// 从EloConfig创建服务（不带因子存储）
    pub fn from_elo_config_without_factors(
        db: DatabaseConnection,
        storage: Arc<dyn CombinedEloStorage + Send + Sync>,
        elo_config: &EloConfig,
    ) -> Self {
        Self::from_elo_config(db, storage, elo_config, None)
    }

    /// 获取学生在指定知识点的Elo评分
    pub async fn get_knowledge_elo(&self, student_id: i64, knowledge_id: i32) -> Result<f64> {
        // 直接从数据库查询，不使用缓存
        let elo = match self
            .storage
            .get_knowledge_elo(student_id, knowledge_id)
            .await?
        {
            Some(rating) => rating,
            None => self.calculator.config().initial_rating,
        };

        Ok(elo)
    }

    /// 获取学生在指定知识点的全部能力数据
    ///
    /// 一次性查询获取知识点能力相关数据，包括:
    /// - Elo评分 (elo_rating): 学生在该知识点的能力值
    /// - 答题数量 (question_count): 学生在该知识点上的总答题次数
    /// - 正确答题数 (correct_count): 学生在该知识点上答对的题目数量
    /// - 最后更新时间 (last_updated): 该知识点能力值的最后更新时间
    ///
    /// 如果学生在该知识点没有记录，则返回None
    pub async fn get_knowledge_ability(
        &self,
        student_id: i64,
        knowledge_id: i32,
    ) -> Result<Option<crate::storage::entities::knowledge_ability::Model>> {
        use crate::storage::entities::knowledge_ability::Column;
        use crate::storage::entities::knowledge_ability::Entity as KnowledgeAbilityEntity;
        use sea_orm::{ColumnTrait, EntityTrait, QueryFilter};

        // 构建查询并执行
        let ability = KnowledgeAbilityEntity::find()
            .filter(Column::StudentId.eq(student_id))
            .filter(Column::KnowledgeId.eq(knowledge_id))
            .one(&self.db)
            .await
            .map_err(|e| crate::Error::storage(format!("查询知识点能力数据失败: {}", e)))?;

        Ok(ability)
    }

    /// 记录ELO计算因子（如果存储支持）
    pub(crate) async fn record_elo_factors(
        &self,
        student_id: i64,
        knowledge_id: i32,
        _question_id: i32,
        answer_id: Option<i32>,
        _is_correct: bool,
        _expected_score: f64,
        _actual_score: f64,
        k_factor: f64,
        submission_count: u32,
    ) -> Result<()> {
        // 检查是否有因子存储可用
        if let Some(factors_storage) = self.factors_storage.as_ref() {
            // 构建ELO因子
            let factors = EloFactors {
                base_k: self.calculator.config().k_factor,
                time_factor: 1.0,      // 默认时间因子
                activity_factor: 1.0,  // 默认活跃度因子
                knowledge_factor: 1.0, // 默认知识点因子
                streak_factor: 1.0,    // 默认连胜/连败因子
                final_k: k_factor,     // 最终使用的K因子
            };

            // 确定学习阶段
            let stage = match submission_count {
                0..=5 => "cold_start",
                6..=20 => "transition",
                21..=100 => "learning",
                101..=1000 => "mastery",
                _ => "expert",
            };

            // 记录因子
            factors_storage
                .record_elo_factors(student_id, knowledge_id, answer_id, &factors, stage)
                .await?;
        }

        Ok(())
    }
}

/// 计算学习阶段结果
pub fn calculate_learning_stage_result(
    student_id: i64,
    knowledge_id: i32,
    stage_calculator: &StageCalculator,
    ability_rating: f64,
    total_answers: u32,
    correct_answers: u32,
) -> StageCalculationResult {
    // 使用算法层的StageCalculator确定学习阶段
    let learning_stage = stage_calculator.determine_stage(total_answers as i32);

    // 将学习阶段枚举转换为字符串
    let current_stage = learning_stage.name().to_string();

    // 计算阶段进度
    let stage_progress = match total_answers {
        0..=5 => total_answers as f64 / 5.0,
        6..=10 => (total_answers as f64 - 5.0) / 5.0,
        11..=20 => (total_answers as f64 - 10.0) / 10.0,
        21..=50 => (total_answers as f64 - 20.0) / 30.0,
        51..=200 => (total_answers as f64 - 50.0) / 150.0,
        n => (n as f64 - 200.0) / 300.0, // 超过500题就认为达到100%
    };

    // 找出前一个阶段
    let previous_stage = match learning_stage {
        LearningStage::ColdStart => None,
        LearningStage::TransitionI => Some(LearningStage::ColdStart.name().to_string()),
        LearningStage::TransitionII => Some(LearningStage::TransitionI.name().to_string()),
        LearningStage::Regular => Some(LearningStage::TransitionII.name().to_string()),
        LearningStage::Stable => Some(LearningStage::Regular.name().to_string()),
        LearningStage::Mastery => Some(LearningStage::Stable.name().to_string()),
    };

    // 返回计算结果
    StageCalculationResult {
        student_id,
        knowledge_id,
        current_stage,
        previous_stage,
        stage_progress: stage_progress.min(1.0), // 确保不超过100%
        stage_changed_at: Some(Utc::now()),      // 当前时间
        ability_rating,
        total_answers,
        correct_answers,
    }
}
