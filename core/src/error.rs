use crate::notifications;
use std::fmt;
use thiserror::Error;
use tracing::error;

/// 错误严重程度
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum ErrorSeverity {
    /// 低严重性 - 不影响系统功能，但可能导致次优体验
    Low,
    /// 中等严重性 - 影响部分功能，但系统整体仍可用
    Medium,
    /// 高严重性 - 影响关键功能，但系统仍可部分工作
    High,
    /// 严重 - 系统无法正常工作
    Critical,
}

impl fmt::Display for ErrorSeverity {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Self::Low => write!(f, "低"),
            Self::Medium => write!(f, "中"),
            Self::High => write!(f, "高"),
            Self::Critical => write!(f, "严重"),
        }
    }
}

impl Default for ErrorSeverity {
    fn default() -> Self {
        Self::Medium
    }
}

/// 核心库错误类型
#[derive(Debug, Error)]
pub enum Error {
    #[error("算法错误: {0}")]
    Algorithm(String),

    #[error("服务错误: {0}")]
    Service(String),

    #[error("存储错误: {0}")]
    Storage(String),

    #[error("数据库错误: {0}")]
    Database(String),

    #[error("配置错误: {0}")]
    Config(String),

    #[error("告警错误: {0}")]
    Notification(String),

    #[error("缓存错误: {0}")]
    Cache(String),

    #[error("系统错误: {0}")]
    System(String),

    #[error("无效输入: {0}")]
    InvalidInput(String),

    #[error("未找到: {0}")]
    NotFound(String),

    #[error("领域错误: {0}")]
    Domain(String),

    #[error("其他错误: {0}")]
    Other(String),
}

impl Error {
    /// 获取错误的默认严重程度
    pub fn severity(&self) -> ErrorSeverity {
        match self {
            Error::Algorithm(_) => ErrorSeverity::Medium,
            Error::Service(_) => ErrorSeverity::High,
            Error::Storage(_) => ErrorSeverity::High,
            Error::Database(_) => ErrorSeverity::High,
            Error::Config(_) => ErrorSeverity::Medium,
            Error::Notification(_) => ErrorSeverity::Low,
            Error::Cache(_) => ErrorSeverity::Medium,
            Error::System(_) => ErrorSeverity::Critical,
            Error::InvalidInput(_) => ErrorSeverity::Low,
            Error::NotFound(_) => ErrorSeverity::Low,
            Error::Domain(_) => ErrorSeverity::Medium,
            Error::Other(_) => ErrorSeverity::Medium,
        }
    }

    /// 创建算法错误
    pub fn algorithm<S: AsRef<str>>(msg: S) -> Self {
        Error::Algorithm(msg.as_ref().to_string())
    }

    /// 创建服务错误
    pub fn service<S: AsRef<str>>(msg: S) -> Self {
        Error::Service(msg.as_ref().to_string())
    }

    /// 创建存储错误
    pub fn storage<S: AsRef<str>>(msg: S) -> Self {
        Error::Storage(msg.as_ref().to_string())
    }

    /// 创建数据库错误
    pub fn database<S: AsRef<str>>(msg: S) -> Self {
        Error::Database(msg.as_ref().to_string())
    }

    /// 创建配置错误
    pub fn config<S: AsRef<str>>(msg: S) -> Self {
        Error::Config(msg.as_ref().to_string())
    }

    /// 创建通知错误
    pub fn notification<S: AsRef<str>>(msg: S) -> Self {
        Error::Notification(msg.as_ref().to_string())
    }

    /// 创建缓存错误
    pub fn cache<S: AsRef<str>>(msg: S) -> Self {
        Error::Cache(msg.as_ref().to_string())
    }

    /// 创建系统错误
    pub fn system<S: AsRef<str>>(msg: S) -> Self {
        Error::System(msg.as_ref().to_string())
    }

    /// 创建无效输入错误
    pub fn invalid_input<S: AsRef<str>>(msg: S) -> Self {
        Error::InvalidInput(msg.as_ref().to_string())
    }

    /// 创建未找到错误
    pub fn not_found<S: AsRef<str>>(msg: S) -> Self {
        Error::NotFound(msg.as_ref().to_string())
    }

    /// 创建领域错误
    pub fn domain<S: AsRef<str>>(msg: S) -> Self {
        Error::Domain(msg.as_ref().to_string())
    }

    /// 创建对话错误（使用领域错误）
    pub fn conversation<S: AsRef<str>>(msg: S) -> Self {
        Error::Domain(format!("对话错误: {}", msg.as_ref()))
    }

    /// 创建其他错误
    pub fn other<E: std::error::Error>(err: E) -> Self {
        Error::Other(err.to_string())
    }

    /// 添加上下文信息
    pub fn with_context<S: AsRef<str>>(self, context: S) -> Self {
        let context_str = context.as_ref();
        match self {
            Error::Algorithm(msg) => Error::Algorithm(format!("{}: {}", context_str, msg)),
            Error::Service(msg) => Error::Service(format!("{}: {}", context_str, msg)),
            Error::Storage(msg) => Error::Storage(format!("{}: {}", context_str, msg)),
            Error::Database(msg) => Error::Database(format!("{}: {}", context_str, msg)),
            Error::Config(msg) => Error::Config(format!("{}: {}", context_str, msg)),
            Error::Notification(msg) => Error::Notification(format!("{}: {}", context_str, msg)),
            Error::Cache(msg) => Error::Cache(format!("{}: {}", context_str, msg)),
            Error::System(msg) => Error::System(format!("{}: {}", context_str, msg)),
            Error::InvalidInput(msg) => Error::InvalidInput(format!("{}: {}", context_str, msg)),
            Error::NotFound(msg) => Error::NotFound(format!("{}: {}", context_str, msg)),
            Error::Domain(msg) => Error::Domain(format!("{}: {}", context_str, msg)),
            Error::Other(msg) => Error::Other(format!("{}: {}", context_str, msg)),
        }
    }

    /// 发送此错误的通知
    pub async fn notify(&self, title: &str) {
        // 记录错误日志
        error!("{}: {}", title, self);

        // 根据严重程度选择通知级别
        let payload = match self.severity() {
            ErrorSeverity::Low => {
                notifications::NotificationPayload::info(title.to_string(), self.to_string())
            }
            ErrorSeverity::Medium => {
                notifications::NotificationPayload::warning(title.to_string(), self.to_string())
            }
            ErrorSeverity::High | ErrorSeverity::Critical => {
                notifications::NotificationPayload::error(title.to_string(), self.to_string())
            }
        };

        let notification_result = notifications::send_notification(payload).await;

        // 如果发送通知失败，记录日志但不抛出异常
        if let Err(e) = notification_result {
            error!("发送错误通知失败: {}", e);
        }
    }

    /// 发送此错误的通知，带有额外上下文
    pub async fn notify_with_context(&self, title: &str, context: impl std::fmt::Display) {
        // 记录错误日志
        error!("{}: {} - 上下文: {}", title, self, context);

        // 创建带上下文的消息
        let message = format!("{}\n上下文: {}", self, context);

        // 根据严重程度选择通知级别
        let notification_result = match self.severity() {
            ErrorSeverity::Low => {
                notifications::NotificationPayload::info(title.to_string(), message)
            }
            ErrorSeverity::Medium => {
                notifications::NotificationPayload::warning(title.to_string(), message)
            }
            ErrorSeverity::High | ErrorSeverity::Critical => {
                notifications::NotificationPayload::error(title.to_string(), message)
            }
        };

        // 发送通知
        if let Err(e) = notifications::send_notification(notification_result).await {
            error!("发送错误通知失败: {}", e);
        }
    }
}

/// 核心库结果类型
pub type Result<T> = std::result::Result<T, Error>;

/// 结果扩展特性，添加通知功能
pub trait ResultExt<T: Send> {
    /// 出错时发送通知
    fn notify_err(self, title: &str) -> impl std::future::Future<Output = Result<T>> + Send;
}

impl<T: Send> ResultExt<T> for Result<T> {
    fn notify_err(self, title: &str) -> impl std::future::Future<Output = Result<T>> + Send {
        let title = title.to_string(); // 克隆标题字符串，避免生命周期问题

        async move {
            // 在await前处理错误，而不是传递引用跨越await边界
            if let Err(e) = &self {
                // 在异步调用前记录错误
                error!("{}: {}", title, e);

                // 创建通知内容
                let error_message = e.to_string();
                let payload =
                    crate::notifications::NotificationPayload::error(title.clone(), error_message);

                // 发送通知（不使用错误引用）
                let _ = crate::notifications::send_notification(payload).await;
            }

            // 返回原始结果
            self
        }
    }
}

/// 从anyhow错误转换为自定义错误
impl From<anyhow::Error> for Error {
    fn from(err: anyhow::Error) -> Self {
        Error::Other(err.to_string())
    }
}

/// 从通知错误转换为自定义错误
impl From<notifications::NotificationError> for Error {
    fn from(err: notifications::NotificationError) -> Self {
        Error::Notification(err.to_string())
    }
}

/// 从SeaORM数据库错误转换为自定义错误
impl From<sea_orm::DbErr> for Error {
    fn from(err: sea_orm::DbErr) -> Self {
        Error::Database(err.to_string())
    }
}
