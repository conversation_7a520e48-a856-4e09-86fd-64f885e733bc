//! 题目提供者实现
//!
//! 实现QuestionProvider接口，为推荐引擎领域层提供题目数据
//! 这是推荐系统的核心数据提供组件，负责高效的题目查询和筛选

use async_trait::async_trait;
use std::sync::Arc;
use std::time::Instant;

use crate::{
    Result,
    domain::recommendation::services::recommendation_engine::QuestionProvider,
    storage::dto::question::QuestionMetadata,
    log_info,
};

/// 题目提供者实现
///
/// 实现推荐引擎的QuestionProvider接口，为领域层提供题目数据
///
/// ## 架构说明
/// - 这是推荐系统的**主要题目数据提供者**
/// - 使用高效的数据库查询，在数据库层进行筛选
/// - 与V1推荐系统保持一致的查询逻辑
/// - 支持难度区间筛选、已答题目排除等功能
pub struct QuestionProviderImpl {
    // 使用QuestionApplicationService进行高效的查询方法
    question_service: Arc<dyn crate::application::question::service::QuestionApplicationService>,
}

impl QuestionProviderImpl {
    pub fn new(
        question_service: Arc<
            dyn crate::application::question::service::QuestionApplicationService,
        >,
    ) -> Self {
        Self {
            question_service,
        }
    }
}

#[async_trait]
impl QuestionProvider for QuestionProviderImpl {
    async fn get_questions_for_knowledge_point(
        &self,
        knowledge_id: i32,
        difficulty_range: (f64, f64),
        limit: usize,
        exclude_ids: &[i32],
        status: Option<i32>,
    ) -> Result<Vec<QuestionMetadata>> {
        let start = Instant::now();

        // 与V1保持一致：使用高效的数据库查询，在数据库层进行筛选
        // 这样可以避免传输大量无用数据，提高性能
        let questions_metadata = self
            .question_service
            .get_activated_knowledge_questions_by_difficulty_exclude(
                knowledge_id,
                difficulty_range.0,
                difficulty_range.1,
                limit,
                exclude_ids,
                status,
            )
            .await?;

        // 只在DEBUG级别且查询结果为空时打印详细信息
        if tracing::enabled!(tracing::Level::DEBUG) && questions_metadata.is_empty() {
            tracing::debug!(
                "🔍 知识点 {} 查询参数 - 难度区间: [{:.1}, {:.1}], 排除题目数: {}, 查询限制: {}",
                knowledge_id,
                difficulty_range.0,
                difficulty_range.1,
                exclude_ids.len(),
                limit
            );
            
            tracing::debug!(
                "🔍 知识点 {} 过滤后获得题目数: {}",
                knowledge_id,
                questions_metadata.len()
            );
        }

        let elapsed = start.elapsed();
        log_info!(
            "question_provider",
            "[Performance] Knowledge {} question query: {:?}, returned {} questions, difficulty range: {:.1}-{:.1}",
            knowledge_id,
            elapsed,
            questions_metadata.len(),
            difficulty_range.0,
            difficulty_range.1
        );

        // questions_metadata已经是基础设施层类型，直接返回
        Ok(questions_metadata)
    }
}
