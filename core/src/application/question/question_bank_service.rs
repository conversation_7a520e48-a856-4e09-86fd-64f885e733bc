//! 题库生成应用服务

use std::sync::Arc;
use std::collections::HashSet;
use tracing::{debug, info, warn};

use crate::error::{Error, Result};
use crate::application::question::service::QuestionApplicationService;
use crate::application::question::selection_service::QuestionSelectionService;
use crate::application::answer::StudentAnswerService;
use crate::infrastructure::dto::question::QuestionContent;
use crate::domain::question::{
    QuestionBankSession, QuestionBankSessionRepository, QuestionBankConfig
};
use serde::{Deserialize, Serialize};

// 重新导出领域类型，保持 API 兼容性
pub use crate::domain::question::value_objects::{GenerateType, Subject};



/// SAT 组卷响应
#[derive(Debug, Serialize)]
pub struct SatPaperGenerateResponse {
    /// 会话ID
    pub session_id: String,
    /// 题目列表（第一批10题）
    pub questions: Vec<QuestionDto>,
    /// 当前返回的题目数量
    pub current_count: i32,
    /// 总题目数量
    pub total_count: i32,
    /// 生成类型
    pub generate_type: GenerateType,
    /// 学科
    pub subject: Subject,
}

/// SAT 后续题目响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SatNextQuestionsResponse {
    /// 会话ID
    pub session_id: String,
    /// 题目列表
    pub questions: Vec<QuestionDto>,
    /// 当前返回的题目数量
    pub current_count: i32,
    /// 剩余题目数量
    pub remaining_count: i32,
    /// 是否还有更多题目
    pub has_more: bool,
}

/// SAT 题目获取响应
#[derive(Debug, Serialize)]
pub struct SatQuestionsResponse {
    /// 会话ID
    pub session_id: String,
    /// 题目列表
    pub questions: Vec<QuestionDto>,
    /// 当前返回的题目数量
    pub current_count: i32,
    /// 是否还有更多题目
    pub has_more: bool,
}

/// API层的题目DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QuestionDto {
    /// 题目ID
    pub id: String,
    /// 题目内容
    pub content: serde_json::Value,
    /// 选项
    pub options: Option<serde_json::Value>,
    /// 答案
    pub answer: serde_json::Value,
    /// 解释
    pub explanation: Option<serde_json::Value>,
    /// 知识点ID
    pub knowledge_id: i32,
    /// 学科ID
    pub subject_id: i32,
    /// 题型ID
    pub type_id: i32,
    /// 难度
    pub difficulty: i32,
    /// ELO评分
    pub elo_rating: f64,
}

// 从存储层 DTO 转换为应用层 DTO
impl From<QuestionContent> for QuestionDto {
    fn from(question: QuestionContent) -> Self {
        Self {
            id: question.id,
            content: question.question_content,
            options: Some(question.options),
            answer: question.answer,
            explanation: Some(question.explanation),
            knowledge_id: question.knowledge_id,
            subject_id: question.subject_id,
            type_id: question.type_id,
            difficulty: question.difficulty as i32,
            elo_rating: question.elo_rating,
        }
    }
}

/// 题库生成应用服务
///
/// 负责题库会话管理和题目组织，支持随机和自适应两种模式
pub struct QuestionBankService {
    /// 问题应用服务
    question_service: Arc<dyn QuestionApplicationService>,
    /// 题目选择服务
    #[allow(dead_code)] // TODO: 将在后续版本中使用统一的选择服务替代当前的选择逻辑
    selection_service: Arc<dyn QuestionSelectionService>,
    /// 题库配置
    config: QuestionBankConfig,
    /// 会话仓储
    session_repository: Arc<dyn QuestionBankSessionRepository>,
    /// 推荐应用服务（可选，用于自适应选择）
    recommendation_service: Option<Arc<crate::application::recommendation::NewRecommendationApplicationService>>,
    /// 学生答题服务（可选，用于获取已答题信息）
    answer_service: Option<Arc<StudentAnswerService>>,
}

impl QuestionBankService {
    /// 创建题库生成服务
    pub fn new(
        question_service: Arc<dyn QuestionApplicationService>,
        selection_service: Arc<dyn QuestionSelectionService>,
        session_repository: Arc<dyn QuestionBankSessionRepository>,
    ) -> Self {
        // 根据环境获取配置
        let config = Self::get_config_from_environment();

        Self {
            question_service,
            selection_service,
            config,
            session_repository,
            recommendation_service: None,
            answer_service: None,
        }
    }

    /// 创建带推荐服务的题库生成服务（支持自适应选择）
    pub fn with_recommendation_service(
        question_service: Arc<dyn QuestionApplicationService>,
        selection_service: Arc<dyn QuestionSelectionService>,
        session_repository: Arc<dyn QuestionBankSessionRepository>,
        recommendation_service: Arc<crate::application::recommendation::NewRecommendationApplicationService>,
    ) -> Self {
        // 根据环境获取配置
        let config = Self::get_config_from_environment();

        Self {
            question_service,
            selection_service,
            config,
            session_repository,
            recommendation_service: Some(recommendation_service),
            answer_service: None,
        }
    }

    /// 创建带答题服务的题库生成服务（支持已答题判断）
    pub fn with_answer_service(
        question_service: Arc<dyn QuestionApplicationService>,
        selection_service: Arc<dyn QuestionSelectionService>,
        session_repository: Arc<dyn QuestionBankSessionRepository>,
        answer_service: Arc<StudentAnswerService>,
    ) -> Self {
        // 根据环境获取配置
        let config = Self::get_config_from_environment();

        Self {
            question_service,
            selection_service,
            config,
            session_repository,
            recommendation_service: None,
            answer_service: Some(answer_service),
        }
    }

    /// 创建完整功能的题库生成服务（支持自适应推荐和已答题判断）
    pub fn with_full_services(
        question_service: Arc<dyn QuestionApplicationService>,
        selection_service: Arc<dyn QuestionSelectionService>,
        session_repository: Arc<dyn QuestionBankSessionRepository>,
        recommendation_service: Arc<crate::application::recommendation::NewRecommendationApplicationService>,
        answer_service: Arc<StudentAnswerService>,
    ) -> Self {
        // 根据环境获取配置
        let config = Self::get_config_from_environment();

        Self {
            question_service,
            selection_service,
            config,
            session_repository,
            recommendation_service: Some(recommendation_service),
            answer_service: Some(answer_service),
        }
    }

    /// 根据环境获取题库配置
    fn get_config_from_environment() -> QuestionBankConfig {
        // 尝试从配置管理器获取环境信息
        if let Some(config_manager) = crate::config::manager::CONFIG_MANAGER.get() {
            let env = config_manager.environment();
            match env {
                "production" => QuestionBankConfig::production(),
                "test" => QuestionBankConfig::test(),
                _ => QuestionBankConfig::development(), // development 和其他环境
            }
        } else {
            // 如果配置管理器未初始化，使用默认开发环境配置
            QuestionBankConfig::development()
        }
    }



    /// 选择 SAT 题目
    ///
    /// 从 t_sat_question 表中根据小节ID选择22题，创建session，返回前10题
    ///
    /// # 参数
    /// - `section_ids`: 小节ID列表
    /// - `generate_type`: 选择类型（random/adaptive）
    /// - `question_count`: 题目数量（固定为22）
    /// - `subject`: 学科（math/reading）
    ///
    /// # 返回
    /// - SAT 组卷响应（包含session_id和前10题）
    pub async fn get_question_bank_recommendations(
        &self,
        user_id: Option<i64>,
        section_ids: Vec<i32>,
        generate_type: GenerateType,
        question_count: i32,
        subject: Subject,
    ) -> Result<SatPaperGenerateResponse> {
        debug!(
            "开始题库推荐: 用户={:?}, 小节={:?}, 类型={:?}, 数量={}, 学科={:?}",
            user_id, section_ids, generate_type, question_count, subject
        );

        // 验证参数
        self.validate_sat_request(&section_ids, question_count)?;

        // 根据推荐类型选择推荐策略
        let selected_questions = match &generate_type {
            GenerateType::Random => {
                info!("执行随机推荐策略: sections={:?}, count={}", section_ids, question_count);
                self.get_random_recommendations(user_id, &section_ids, question_count as usize).await?
            }
            GenerateType::Adaptive => {
                info!("执行自适应推荐策略: user_id={:?}, sections={:?}, count={}", user_id, section_ids, question_count);
                if let Some(user_id_value) = user_id {
                    // 检查是否有推荐服务，如果没有则尝试创建
                    if self.recommendation_service.is_some() {
                        info!("推荐服务可用，执行自适应推荐");
                        self.get_adaptive_recommendations(
                            user_id_value,
                            &section_ids,
                            question_count as usize,
                        ).await?
                    } else {
                        warn!("推荐服务未配置，自适应推荐回退到随机推荐");
                        info!("提示：要启用自适应推荐，请在应用启动时使用 provide_question_bank_service_async");
                        self.get_random_recommendations(user_id, &section_ids, question_count as usize).await?
                    }
                } else {
                    warn!("自适应推荐需要用户ID，回退到随机推荐");
                    self.get_random_recommendations(user_id, &section_ids, question_count as usize).await?
                }
            }
        };

        if selected_questions.is_empty() {
            return Err(Error::NotFound("未找到任何题目".to_string()));
        }

 /*        if selected_questions.len() < question_count as usize {
            return Err(Error::NotFound(format!(
                "题目数量不足，需要{}题，实际只有{}题",
                question_count,
                selected_questions.len()
            )));
        } */



        // 存储会话到数据库
        let question_ids: Vec<i32> = selected_questions.iter().map(|q| q.id.parse::<i32>().unwrap_or(0)).collect();
        let actual_total_count = question_ids.len() as i32;

        // 使用领域类型（已经是领域类型，无需转换）
        let domain_generate_type = generate_type.clone();
        let domain_subject = subject.clone();

        let session = QuestionBankSession::new(
            user_id,
            question_ids.clone(),
            domain_generate_type,
            domain_subject,
            section_ids.clone(),
        )?;

        self.session_repository.save(&session).await?;
        let session_id = session.session_id().to_string();

        // 统一只返回前10题（如果不足10题就返回实际数量）
        let return_questions: Vec<QuestionDto> = selected_questions
            .into_iter()
            .take(10)
            .map(QuestionDto::from)
            .collect();
        let current_count = return_questions.len() as i32;

        info!(
            "SAT 题目选择完成: 用户={:?}, session_id={}, 小节数={}, 实际总题数={}, 返回前{}题",
            user_id, session_id, section_ids.len(), actual_total_count, current_count
        );

        Ok(SatPaperGenerateResponse {
            session_id,
            questions: return_questions,
            current_count,
            total_count: actual_total_count,
            generate_type,
            subject,
        })
    }

    /// 根据会话ID和当前题目ID获取后续题目
    ///
    /// # 参数
    /// - `session_id`: 会话ID
    /// - `current_question_id`: 当前题目ID
    /// - `count`: 需要获取的题目数量，默认10题
    ///
    /// # 返回
    /// - 后续题目列表响应
    pub async fn get_next_questions(
        &self,
        session_id: String,
        current_question_id: i32,
        count: Option<i32>,
    ) -> Result<SatNextQuestionsResponse> {
        let count = count.unwrap_or(10) as usize;

        debug!(
            "获取后续题目: session_id={}, current_question_id={}, count={}",
            session_id, current_question_id, count
        );

        // 从数据库获取会话信息
        let session = self.session_repository
            .find_by_id(&session_id)
            .await?
            .ok_or_else(|| Error::NotFound(format!("会话 {} 不存在或已过期", session_id)))?;

        // 检查会话是否过期
        if session.is_expired() {
            return Err(Error::NotFound(format!("会话 {} 已过期", session_id)));
        }

        // 获取后续题目ID列表
        let next_question_ids = session.get_questions_after(current_question_id, count)?;

        if next_question_ids.is_empty() {
            return Ok(SatNextQuestionsResponse {
                session_id,
                questions: vec![],
                current_count: 0,
                remaining_count: 0,
                has_more: false,
            });
        }

        // 获取题目内容
        let next_question_ids_str: Vec<String> = next_question_ids.iter().map(|id| id.to_string()).collect();
        let question_contents = self.question_service
            .get_questions_content(&next_question_ids_str)
            .await?;

        // 转换为DTO
        let questions: Vec<QuestionDto> = question_contents
            .into_iter()
            .map(QuestionDto::from)
            .collect();

        // 计算剩余题目数量
        let current_count = questions.len();
        let remaining_count = session.remaining_questions_count(current_question_id)
            .unwrap_or(0) - current_count;

        let has_more = remaining_count > 0;

        info!(
            "后续题目获取完成: session_id={}, 返回{}题, 剩余{}题",
            session_id, current_count, remaining_count
        );

        Ok(SatNextQuestionsResponse {
            session_id,
            questions,
            current_count: current_count as i32,
            remaining_count: remaining_count as i32,
            has_more,
        })
    }



    /// 验证 SAT 请求参数
    fn validate_sat_request(&self, section_ids: &[i32], question_count: i32) -> Result<()> {
        if section_ids.is_empty() {
            return Err(Error::InvalidInput("小节ID列表不能为空".to_string()));
        }

        // 验证题目数量是否为允许的值
        if ![10, 22, 27].contains(&question_count) {
            return Err(Error::InvalidInput(
                format!("题目数量必须是 10、22 或 27，当前值: {}", question_count)
            ));
        }

        if section_ids.len() > 50 {
            return Err(Error::InvalidInput("小节数量不能超过50个".to_string()));
        }

        Ok(())
    }

    /// 获取状态配置
    /// 从题库配置中读取状态值，与推荐系统保持一致
    fn get_status_from_config(&self) -> Option<i32> {
        self.config.status
    }

    /// 获取用户已答题目ID列表
    async fn get_user_answered_question_ids(&self, user_id: i64) -> Result<Vec<i32>> {
        match &self.answer_service {
            Some(service) => {
                debug!("获取用户 {} 的已答题目ID", user_id);
                service.get_answered_question_ids(user_id).await
            }
            None => {
                debug!("答题服务未配置，返回空的已答题目列表");
                Ok(Vec::new())
            }
        }
    }

    /// 获取随机推荐题目
    ///
    /// 应用服务方法：协调数据获取和随机选择逻辑
    /// 实现均匀覆盖所有小节、优先未答题、完全随机抽取的逻辑
    async fn get_random_recommendations(
        &self,
        user_id: Option<i64>,
        section_ids: &[i32],
        count: usize,
    ) -> Result<Vec<QuestionContent>> {
        debug!(
            "应用服务：开始随机推荐 user_id={:?}, sections={:?}, count={}",
            user_id, section_ids, count
        );

        if section_ids.is_empty() || count == 0 {
            return Ok(Vec::new());
        }

        // 1. 获取用户已答题目ID列表
        let answered_question_ids = if let Some(uid) = user_id {
            self.get_user_answered_question_ids(uid).await?
        } else {
            Vec::new()
        };
        let answered_set: HashSet<i32> = answered_question_ids.iter()
            .filter_map(|id| id.to_string().parse::<i32>().ok())
            .collect();

        debug!("用户已答题目数量: {}", answered_set.len());

        // 2. 按小节获取题目并分组
        let mut section_groups = self.build_section_groups(section_ids, &answered_set).await?;

        if section_groups.is_empty() {
            return Ok(Vec::new());
        }

        // 3. 计算小节题量分配
        self.calculate_section_allocations(&mut section_groups, count);

        // 4. 处理题库不足的小节，重新分配题量
        self.handle_insufficient_sections(&mut section_groups, count);

        // 5. 从各小节选择题目
        let mut all_selected_questions = Vec::new();
        for group in section_groups {
            let selected = group.select_questions(group.allocated_count);
            all_selected_questions.extend(selected);
        }

        // 6. 最终随机打乱所有题目
        fastrand::shuffle(&mut all_selected_questions);

        info!(
            "随机推荐完成: user_id={:?}, sections={:?}, 返回{}题",
            user_id, section_ids, all_selected_questions.len()
        );

        Ok(all_selected_questions)
    }

    /// 按小节获取题目并分组
    async fn build_section_groups(
        &self,
        section_ids: &[i32],
        answered_set: &HashSet<i32>,
    ) -> Result<Vec<SectionQuestionGroup>> {
        let mut section_groups = Vec::new();

        for &section_id in section_ids {
            let mut group = SectionQuestionGroup::new(section_id);

            // 获取小节的题目基础信息（使用与自适应推荐相同的方法）
            let questions_basic_info = self.question_service
                .get_questions_basic_info_by_sections(&[section_id], self.get_status_from_config())
                .await?;

            // 过滤出当前小节的题目并批量获取完整内容
            let section_question_ids: Vec<i32> = questions_basic_info
                .iter()
                .filter(|(_, section_id_in_result, _)| *section_id_in_result == section_id)
                .map(|(question_id, _, _)| *question_id)
                .collect();

            // 批量获取题目完整内容
            let question_ids_str: Vec<String> = section_question_ids
                .iter()
                .map(|id| id.to_string())
                .collect();

            let section_questions = if !question_ids_str.is_empty() {
                self.question_service.get_questions_content(&question_ids_str).await?
            } else {
                Vec::new()
            };

            // 按已答/未答分组
            for question in section_questions {
                let question_id = question.id.parse::<i32>().unwrap_or(-1);
                let is_answered = answered_set.contains(&question_id);
                group.add_question(question, is_answered);
            }

            debug!(
                "小节 {} 题目分组: 未答题={}, 已答题={}, 总计={}",
                section_id,
                group.unanswered_questions.len(),
                group.answered_questions.len(),
                group.total_available()
            );

            if group.total_available() > 0 {
                section_groups.push(group);
            }
        }

        Ok(section_groups)
    }

    /// 计算小节题量分配
    /// 实现均匀覆盖题量的规则：分到小节的题量向下取整，多余题目按照小节顺序依次补齐
    fn calculate_section_allocations(&self, section_groups: &mut Vec<SectionQuestionGroup>, total_count: usize) {
        let section_count = section_groups.len();
        if section_count == 0 {
            return;
        }

        // 基础分配：向下取整
        let base_count_per_section = total_count / section_count;
        let remainder = total_count % section_count;

        debug!(
            "小节题量分配开始: 总题量={}, 小节数={}, 基础分配={}, 余量={}",
            total_count, section_count, base_count_per_section, remainder
        );

        // 为每个小节分配基础题量 + 余量补齐（按小节顺序）
        for (index, group) in section_groups.iter_mut().enumerate() {
            let allocated_count = base_count_per_section + if index < remainder { 1 } else { 0 };
            group.set_allocated_count(allocated_count);

            debug!(
                "小节 {} 初始分配: {}题",
                group.section_id, allocated_count
            );
        }
    }

    /// 处理题库不足的小节，重新分配题量
    /// 若某小节的题库题量不足以满足分配目标，则按其最大可分配题量取值，缺少题量按照小节顺序依次补齐
    fn handle_insufficient_sections(&self, section_groups: &mut Vec<SectionQuestionGroup>, _total_count: usize) {
        let mut total_deficit = 0;
        let mut available_sections = Vec::new();

        // 1. 检查每个小节的题库是否充足，记录不足的题量
        for group in section_groups.iter_mut() {
            let available_count = group.total_available();
            if available_count < group.allocated_count {
                let deficit = group.allocated_count - available_count;
                total_deficit += deficit;
                group.set_allocated_count(available_count); // 调整为最大可用数量

                debug!(
                    "小节 {} 题库不足: 需要={}, 可用={}, 缺少={}",
                    group.section_id, group.allocated_count + deficit, available_count, deficit
                );
            } else {
                available_sections.push(group.section_id);
            }
        }

        // 2. 如果有缺少的题量，按小节顺序重新分配给有余量的小节
        if total_deficit > 0 && !available_sections.is_empty() {
            debug!("开始重新分配缺少的题量: {}", total_deficit);

            let mut remaining_deficit = total_deficit;
            let mut section_index = 0;

            while remaining_deficit > 0 && section_index < section_groups.len() {
                let group = &mut section_groups[section_index];

                if available_sections.contains(&group.section_id) {
                    let available_count = group.total_available();
                    let current_allocated = group.allocated_count;
                    let can_add = available_count - current_allocated;

                    if can_add > 0 {
                        let add_count = remaining_deficit.min(can_add);
                        group.set_allocated_count(current_allocated + add_count);
                        remaining_deficit -= add_count;

                        debug!(
                            "小节 {} 补充分配: 原分配={}, 新分配={}, 补充={}",
                            group.section_id, current_allocated, group.allocated_count, add_count
                        );
                    }
                }

                section_index += 1;
            }

            if remaining_deficit > 0 {
                warn!("仍有 {} 题无法分配，所有小节题库都已用尽", remaining_deficit);
            }
        }
    }



    /// 获取自适应推荐题目
    ///
    /// 应用服务方法：协调推荐服务进行基于用户能力的智能推荐
    async fn get_adaptive_recommendations(
        &self,
        user_id: i64,
        section_ids: &[i32],
        count: usize,
    ) -> Result<Vec<QuestionContent>> {
        debug!(
            "应用服务：开始自适应推荐 user_id={}, sections={:?}, count={}",
            user_id, section_ids, count
        );

        // 检查是否有推荐服务
        let recommendation_service = match &self.recommendation_service {
            Some(service) => service,
            None => {
                debug!("推荐服务未配置，回退到随机推荐");
                return self.get_random_recommendations(Some(user_id), section_ids, count).await;
            }
        };

        // 委托给推荐服务处理
        let recommended_questions = recommendation_service
            .get_question_bank_recommendations_by_section_ability(
                user_id,
                section_ids.to_vec(),
                count,
                self.config.status,
            )
            .await?;

        info!(
            "自适应推荐完成: user_id={}, sections={:?}, 返回{}题",
            user_id, section_ids, recommended_questions.len()
        );

        Ok(recommended_questions)
    }

}

/// 小节题目分组
/// 用于按小节组织题目，区分已答题和未答题
#[derive(Debug, Clone)]
pub struct SectionQuestionGroup {
    /// 小节ID
    section_id: i32,
    /// 未答题目列表
    unanswered_questions: Vec<QuestionContent>,
    /// 已答题目列表
    answered_questions: Vec<QuestionContent>,
    /// 分配的题目数量
    allocated_count: usize,
}

impl SectionQuestionGroup {
    /// 创建新的小节题目分组
    pub fn new(section_id: i32) -> Self {
        Self {
            section_id,
            unanswered_questions: Vec::new(),
            answered_questions: Vec::new(),
            allocated_count: 0,
        }
    }

    /// 添加题目到相应的分组
    pub fn add_question(&mut self, question: QuestionContent, is_answered: bool) {
        if is_answered {
            self.answered_questions.push(question);
        } else {
            self.unanswered_questions.push(question);
        }
    }

    /// 获取可用题目总数
    pub fn total_available(&self) -> usize {
        self.unanswered_questions.len() + self.answered_questions.len()
    }

    /// 获取未答题数量
    pub fn unanswered_count(&self) -> usize {
        self.unanswered_questions.len()
    }

    /// 获取已答题数量
    pub fn answered_count(&self) -> usize {
        self.answered_questions.len()
    }

    /// 设置分配数量
    fn set_allocated_count(&mut self, count: usize) {
        self.allocated_count = count;
    }

    /// 随机选择题目（优先未答题）
    ///
    /// 修复：使用非破坏性选择，避免题目被永久移除
    pub fn select_questions(&self, count: usize) -> Vec<QuestionContent> {
        let mut selected = Vec::new();
        let mut remaining_count = count;

        // 1. 优先选择未答题
        if remaining_count > 0 && !self.unanswered_questions.is_empty() {
            let unanswered_count = remaining_count.min(self.unanswered_questions.len());

            // 创建索引数组并随机打乱
            let mut indices: Vec<usize> = (0..self.unanswered_questions.len()).collect();
            fastrand::shuffle(&mut indices);

            // 选择前 unanswered_count 个题目（通过索引，不移除原题目）
            for i in 0..unanswered_count {
                if let Some(&index) = indices.get(i) {
                    if let Some(question) = self.unanswered_questions.get(index) {
                        selected.push(question.clone());
                        remaining_count -= 1;
                    }
                }
            }
        }

        // 2. 如果还需要更多题目，选择已答题
        if remaining_count > 0 && !self.answered_questions.is_empty() {
            let answered_count = remaining_count.min(self.answered_questions.len());

            // 创建索引数组并随机打乱
            let mut indices: Vec<usize> = (0..self.answered_questions.len()).collect();
            fastrand::shuffle(&mut indices);

            // 选择前 answered_count 个题目（通过索引，不移除原题目）
            for i in 0..answered_count {
                if let Some(&index) = indices.get(i) {
                    if let Some(question) = self.answered_questions.get(index) {
                        selected.push(question.clone());
                        remaining_count -= 1;
                    }
                }
            }
        }

        debug!(
            "小节 {} 选择题目: 需要={}, 实际选择={}, 未答题总数={}, 已答题总数={}",
            self.section_id, count, selected.len(),
            self.unanswered_questions.len(), self.answered_questions.len()
        );

        selected
    }
}