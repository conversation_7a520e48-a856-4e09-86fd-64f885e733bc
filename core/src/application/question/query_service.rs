//! 题目查询服务
//!
//! 统一处理题目查询逻辑，避免在多个地方重复相同的数据库查询代码
//! 提供高性能的批量查询和缓存机制

use async_trait::async_trait;
use std::collections::HashSet;
use std::sync::Arc;
use tracing::{debug, info, warn};

use crate::domain::question::{Question, QuestionRepository, QuestionConverter};
use crate::infrastructure::dto::question::QuestionContent;
use crate::error::Result;

/// 查询条件构建器
#[derive(Debug, Clone, Default)]
pub struct QuestionQueryBuilder {
    /// 题目ID列表
    pub ids: Option<Vec<String>>,
    /// 知识点ID列表
    pub knowledge_ids: Option<Vec<i32>>,
    /// 小节ID列表
    pub section_ids: Option<Vec<i32>>,
    /// 学科ID
    pub subject_id: Option<i32>,
    /// 难度范围 (min, max)
    pub difficulty_range: Option<(f64, f64)>,
    /// ELO评分范围 (min, max)
    pub elo_range: Option<(f64, f64)>,
    /// 状态过滤
    pub status: Option<i32>,
    /// 是否只查询激活的题目
    pub active_only: bool,
    /// 排除的题目ID列表
    pub exclude_ids: Option<Vec<String>>,
    /// 限制返回数量
    pub limit: Option<usize>,
}

impl QuestionQueryBuilder {
    /// 创建新的查询构建器
    pub fn new() -> Self {
        Self {
            active_only: true, // 默认只查询激活的题目
            ..Default::default()
        }
    }

    /// 设置题目ID列表
    pub fn with_ids(mut self, ids: Vec<String>) -> Self {
        self.ids = Some(ids);
        self
    }

    /// 设置知识点ID列表
    pub fn with_knowledge_ids(mut self, knowledge_ids: Vec<i32>) -> Self {
        self.knowledge_ids = Some(knowledge_ids);
        self
    }

    /// 设置小节ID列表
    pub fn with_section_ids(mut self, section_ids: Vec<i32>) -> Self {
        self.section_ids = Some(section_ids);
        self
    }

    /// 设置学科ID
    pub fn with_subject_id(mut self, subject_id: i32) -> Self {
        self.subject_id = Some(subject_id);
        self
    }

    /// 设置难度范围
    pub fn with_difficulty_range(mut self, min: f64, max: f64) -> Self {
        self.difficulty_range = Some((min, max));
        self
    }

    /// 设置ELO评分范围
    pub fn with_elo_range(mut self, min: f64, max: f64) -> Self {
        self.elo_range = Some((min, max));
        self
    }

    /// 设置状态过滤
    pub fn with_status(mut self, status: i32) -> Self {
        self.status = Some(status);
        self
    }

    /// 设置是否只查询激活的题目
    pub fn with_active_only(mut self, active_only: bool) -> Self {
        self.active_only = active_only;
        self
    }

    /// 设置排除的题目ID列表
    pub fn with_exclude_ids(mut self, exclude_ids: Vec<String>) -> Self {
        self.exclude_ids = Some(exclude_ids);
        self
    }

    /// 设置限制返回数量
    pub fn with_limit(mut self, limit: usize) -> Self {
        self.limit = Some(limit);
        self
    }
}

/// 题目查询服务接口
#[async_trait]
pub trait QuestionQueryService: Send + Sync {
    /// 根据查询条件查找题目
    async fn find_questions(&self, query: QuestionQueryBuilder) -> Result<Vec<Question>>;

    /// 根据查询条件查找题目内容
    async fn find_question_contents(&self, query: QuestionQueryBuilder) -> Result<Vec<QuestionContent>>;

    /// 获取题目基本信息 (ID, 小节ID, ELO评分)
    async fn get_questions_basic_info(
        &self,
        section_ids: &[i32],
        status: Option<i32>,
    ) -> Result<Vec<(i32, i32, f64)>>;

    /// 批量获取题目内容
    async fn batch_get_question_contents(&self, ids: &[String]) -> Result<Vec<QuestionContent>>;

    /// 统计查询结果数量（不返回具体数据）
    async fn count_questions(&self, query: QuestionQueryBuilder) -> Result<usize>;
}

/// 题目查询服务实现
pub struct QuestionQueryServiceImpl {
    question_repository: Arc<dyn QuestionRepository>,
}

impl QuestionQueryServiceImpl {
    /// 创建新的查询服务实例
    pub fn new(question_repository: Arc<dyn QuestionRepository>) -> Self {
        Self {
            question_repository,
        }
    }

    /// 应用查询条件过滤题目
    fn apply_filters(&self, questions: Vec<Question>, query: &QuestionQueryBuilder) -> Vec<Question> {
        let mut filtered = questions;

        // 应用学科过滤
        if let Some(subject_id) = query.subject_id {
            filtered.retain(|q| q.subject_id == subject_id);
        }

        // 难度范围过滤已在数据库层面通过ELO评分完成，无需在应用层重复过滤

        // 应用ELO评分范围过滤
        if let Some((min, max)) = query.elo_range {
            filtered.retain(|q| q.is_in_elo_range(min, max));
        }

        // 应用状态过滤
        if let Some(status) = query.status {
            filtered.retain(|q| q.has_status(status));
        }

        // 应用激活状态过滤
        if query.active_only {
            filtered.retain(|q| q.is_active);
        }

        // 应用排除ID过滤
        if let Some(exclude_ids) = &query.exclude_ids {
            let exclude_set: HashSet<&String> = exclude_ids.iter().collect();
            filtered.retain(|q| !exclude_set.contains(&q.id));
        }

        // 应用数量限制
        if let Some(limit) = query.limit {
            filtered.truncate(limit);
        }

        filtered
    }
}

#[async_trait]
impl QuestionQueryService for QuestionQueryServiceImpl {
    async fn find_questions(&self, query: QuestionQueryBuilder) -> Result<Vec<Question>> {
        debug!("执行题目查询: {:?}", query);

        // 根据查询条件选择最优的查询策略
        let questions = if let Some(ids) = &query.ids {
            // 按ID查询
            self.question_repository.find_by_ids(ids).await?
        } else if let Some(knowledge_ids) = &query.knowledge_ids {
            if knowledge_ids.len() == 1 {
                // 单个知识点查询
                if let Some((min, max)) = query.difficulty_range {
                    self.question_repository
                        .find_by_knowledge_and_difficulty(knowledge_ids[0], min, max, query.status)
                        .await?
                } else {
                    self.question_repository
                        .find_by_knowledge(knowledge_ids[0])
                        .await?
                }
            } else {
                // 多个知识点查询
                self.question_repository
                    .find_by_knowledge_ids(knowledge_ids)
                    .await?
            }
        } else if let Some(subject_id) = query.subject_id {
            // 按学科查询
            self.question_repository.find_by_subject(subject_id).await?
        } else if let Some(ref section_ids) = query.section_ids {
            if !section_ids.is_empty() {
                // 按小节查询 - 恢复对section_ids的支持
                self.question_repository.find_by_sections(section_ids).await?
            } else {
                Vec::new()
            }
        } else {
            // 如果没有主要查询条件，返回空结果
            warn!("查询条件不足，无法执行查询");
            return Ok(Vec::new());
        };

        // 应用额外的过滤条件
        let filtered_questions = self.apply_filters(questions, &query);

        info!("查询完成，返回 {} 个题目", filtered_questions.len());
        Ok(filtered_questions)
    }

    async fn find_question_contents(&self, query: QuestionQueryBuilder) -> Result<Vec<QuestionContent>> {
        let questions = self.find_questions(query).await?;
        let contents = QuestionConverter::batch_to_question_content(&questions);
        Ok(contents)
    }

    async fn get_questions_basic_info(
        &self,
        section_ids: &[i32],
        status: Option<i32>,
    ) -> Result<Vec<(i32, i32, f64)>> {
        if section_ids.is_empty() {
            return Ok(Vec::new());
        }

        info!("批量获取小节题目基本信息，小节数量: {}, 状态过滤: {:?}", section_ids.len(), status);

        // 恢复原始的直接数据库查询逻辑，避免破坏现有功能
        use crate::infrastructure::persistence::StorageManager;
        use crate::storage::entities::question;
        use sea_orm::{ColumnTrait, EntityTrait, QueryFilter, QuerySelect};

        let storage_manager = StorageManager::global()
            .ok_or_else(|| crate::error::Error::service("存储管理器不可用".to_string()))?;

        let db = storage_manager.sea_orm_db();

        // 构建批量查询，只选择需要的字段
        let mut query = question::Entity::find()
            .select_only()
            .column(question::Column::QuestionId)
            .column(question::Column::SectionId)
            .column(question::Column::EloRating)
            .filter(question::Column::SectionId.is_in(section_ids.iter().cloned()));

        // 添加状态过滤
        if let Some(status_value) = status {
            query = query.filter(question::Column::Status.eq(status_value));
        }

        // 执行查询
        let raw_results = query
            .into_tuple::<(i32, Option<i32>, f64)>()
            .all(&*db)
            .await
            .map_err(|e| crate::error::Error::storage(format!("批量查询题目基本信息失败: {}", e)))?;

        // 过滤掉section_id为None的记录，并转换为所需格式
        let results: Vec<(i32, i32, f64)> = raw_results
            .into_iter()
            .filter_map(|(question_id, section_id_opt, elo_rating)| {
                section_id_opt.map(|section_id| (question_id, section_id, elo_rating))
            })
            .collect();

        info!("批量查询完成，获取到 {} 条题目基本信息", results.len());
        Ok(results)
    }

    async fn batch_get_question_contents(&self, ids: &[String]) -> Result<Vec<QuestionContent>> {
        if ids.is_empty() {
            return Ok(Vec::new());
        }

        let query = QuestionQueryBuilder::new()
            .with_ids(ids.to_vec())
            .with_active_only(false); // 批量查询时不过滤激活状态

        self.find_question_contents(query).await
    }

    async fn count_questions(&self, query: QuestionQueryBuilder) -> Result<usize> {
        let questions = self.find_questions(query).await?;
        Ok(questions.len())
    }
}
