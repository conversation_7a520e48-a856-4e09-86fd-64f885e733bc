//! 问题应用服务实现
//!
//! 实现问题应用服务接口，提供问题查询和管理功能
//! 遵循DDD架构原则，使用领域层repository接口

use async_trait::async_trait;
use std::collections::HashMap;
use std::sync::Arc;
use tracing::info;

use super::service::QuestionApplicationService;
use super::query_service::{QuestionQueryService, QuestionQueryBuilder};
use crate::domain::question::{Question, QuestionRepository, QuestionConverter};
use crate::domain::question::QuestionMetadata as DomainQuestionMetadata;
use crate::infrastructure::dto::question::{QuestionContent, QuestionMetadata};
use crate::infrastructure::converters::QuestionMetadataConverter;

use crate::error::Result;

/// 问题应用服务实现
pub struct QuestionApplicationServiceImpl {
    question_repository: Arc<dyn QuestionRepository>,
    query_service: Arc<dyn QuestionQueryService>,
    metadata_converter: QuestionMetadataConverter,
}

impl QuestionApplicationServiceImpl {
    /// 创建新的问题应用服务实例
    pub fn new(
        question_repository: Arc<dyn QuestionRepository>,
        query_service: Arc<dyn QuestionQueryService>,
    ) -> Self {
        Self {
            question_repository,
            query_service,
            metadata_converter: QuestionMetadataConverter::new(),
        }
    }
}

#[async_trait]
impl QuestionApplicationService for QuestionApplicationServiceImpl {
    async fn get_question(&self, question_id: &str) -> Result<Option<Question>> {
        info!("获取问题，ID: {}", question_id);
        self.question_repository.find_by_id(question_id).await
    }

    async fn get_questions(&self, question_ids: &[String]) -> Result<Vec<Question>> {
        if question_ids.is_empty() {
            return Ok(Vec::new());
        }

        info!("批量获取问题，问题数量: {}", question_ids.len());

        let query = QuestionQueryBuilder::new()
            .with_ids(question_ids.to_vec())
            .with_active_only(false); // 批量查询时不过滤激活状态

        self.query_service.find_questions(query).await
    }

    async fn get_questions_metadata(
        &self,
        question_ids: &[String],
    ) -> Result<HashMap<String, DomainQuestionMetadata>> {
        info!("批量获取问题元数据，问题数量: {}", question_ids.len());
        self.question_repository.get_metadata(question_ids).await
    }

    async fn get_questions_by_knowledge(&self, knowledge_id: i32) -> Result<Vec<Question>> {
        info!("获取知识点 {} 下的问题", knowledge_id);

        let query = QuestionQueryBuilder::new()
            .with_knowledge_ids(vec![knowledge_id]);

        self.query_service.find_questions(query).await
    }

    async fn get_questions_by_knowledge_and_difficulty(
        &self,
        knowledge_id: i32,
        min_difficulty: f64,
        max_difficulty: f64,
    ) -> Result<Vec<Question>> {
        info!(
            "获取知识点 {} 难度范围 [{:.1}, {:.1}] 的问题",
            knowledge_id, min_difficulty, max_difficulty
        );

        let query = QuestionQueryBuilder::new()
            .with_knowledge_ids(vec![knowledge_id])
            .with_difficulty_range(min_difficulty, max_difficulty);

        self.query_service.find_questions(query).await
    }

    async fn get_questions_by_subject(&self, subject_id: i32) -> Result<Vec<Question>> {
        info!("获取学科 {} 下的问题", subject_id);

        let query = QuestionQueryBuilder::new()
            .with_subject_id(subject_id);

        self.query_service.find_questions(query).await
    }

    // 兼容性方法实现 - 这些方法暂时返回未实现错误
    // 在完全迁移到新架构后可以移除

    async fn get_question_content(&self, question_id: &str) -> Result<QuestionContent> {
        // 使用统一的转换器
        if let Some(question) = self.question_repository.find_by_id(question_id).await? {
            Ok(QuestionConverter::to_question_content(&question))
        } else {
            Err(crate::error::Error::service(format!("问题不存在: {}", question_id)))
        }
    }

    async fn get_questions_content(&self, question_ids: &[String]) -> Result<Vec<QuestionContent>> {
        let questions = self.question_repository.find_by_ids(question_ids).await?;
        // 使用统一的转换器进行批量转换
        Ok(QuestionConverter::batch_to_question_content(&questions))
    }

    async fn get_questions_by_section(
        &self,
        section_id: i32,
        page: Option<u64>,
        page_size: Option<u64>,
    ) -> Result<(Vec<QuestionContent>, u64)> {
        info!("获取小节题目，小节ID: {}, 页码: {:?}, 每页数量: {:?}", section_id, page, page_size);

        // 使用查询服务获取小节下的问题
        let query = QuestionQueryBuilder::new()
            .with_section_ids(vec![section_id]);

        let questions = self.query_service.find_questions(query).await?;
        let total_count = questions.len() as u64;

        // 应用分页
        let (page, page_size) = (page.unwrap_or(1), page_size.unwrap_or(20));
        let start = ((page - 1) * page_size) as usize;
        let end = (start + page_size as usize).min(questions.len());

        let paginated_questions = if start < questions.len() {
            questions[start..end].to_vec()
        } else {
            Vec::new()
        };

        // 转换为 QuestionContent
        let content = QuestionConverter::batch_to_question_content(&paginated_questions);
        Ok((content, total_count))
    }

    async fn get_questions_by_section_and_status(
        &self,
        section_id: i32,
        status: i32,
        page: Option<u64>,
        page_size: Option<u64>,
    ) -> Result<(Vec<QuestionContent>, u64)> {
        info!("获取小节特定状态题目，小节ID: {}, 状态: {}, 页码: {:?}, 每页数量: {:?}",
              section_id, status, page, page_size);

        // 使用查询服务获取小节下特定状态的问题
        let query = QuestionQueryBuilder::new()
            .with_section_ids(vec![section_id])
            .with_status(status);

        let questions = self.query_service.find_questions(query).await?;
        let total_count = questions.len() as u64;

        // 应用分页
        let (page, page_size) = (page.unwrap_or(1), page_size.unwrap_or(20));
        let start = ((page - 1) * page_size) as usize;
        let end = (start + page_size as usize).min(questions.len());

        let paginated_questions = if start < questions.len() {
            questions[start..end].to_vec()
        } else {
            Vec::new()
        };

        // 转换为 QuestionContent
        let content = QuestionConverter::batch_to_question_content(&paginated_questions);
        Ok((content, total_count))
    }

    async fn get_questions_basic_info_by_sections(
        &self,
        section_ids: &[i32],
        status: Option<i32>,
    ) -> Result<Vec<(i32, i32, f64)>> {
        info!("批量获取小节题目基本信息，小节数量: {}, 状态过滤: {:?}", section_ids.len(), status);

        if section_ids.is_empty() {
            return Ok(Vec::new());
        }

        // 使用统一的查询服务
        let results = self.query_service.get_questions_basic_info(section_ids, status).await?;

        info!("批量查询完成，获取到 {} 条题目基本信息", results.len());
        Ok(results)
    }

    async fn get_knowledge_questions_metadata_exclude(
        &self,
        knowledge_id: i32,
        exclude_ids: &[i32],
        _status: Option<i32>,
    ) -> Result<Vec<QuestionMetadata>> {
        // 转换为字符串ID进行排除
        let exclude_str_ids: Vec<String> = exclude_ids.iter().map(|id| id.to_string()).collect();

        // 使用统一查询服务获取知识点下的问题
        let query = QuestionQueryBuilder::new()
            .with_knowledge_ids(vec![knowledge_id])
            .with_exclude_ids(exclude_str_ids);

        let questions = self.query_service.find_questions(query).await?;

        // 转换为基础设施层类型
        let domain_metadata: Vec<DomainQuestionMetadata> = questions
            .into_iter()
            .map(|q| {
                let correct_ratio = q.correct_ratio();
                DomainQuestionMetadata {
                    id: q.id,
                    subject_id: q.subject_id,
                    knowledge_id: q.knowledge_id,
                    knowledge_name: None, // 暂时不提供知识点名称
                    type_id: q.type_id,
                    difficulty: q.difficulty,
                    elo_rating: Some(q.elo_rating),
                    correct_ratio: Some(correct_ratio),
                }
            })
            .collect();

        // 转换为基础设施层类型
        let filtered_questions = self.metadata_converter.domain_to_infra_batch(domain_metadata);

        Ok(filtered_questions)
    }

    async fn get_activated_knowledge_questions_by_difficulty_exclude(
        &self,
        knowledge_id: i32,
        min_difficulty: f64,
        max_difficulty: f64,
        limit: usize,
        exclude_ids: &[i32],
        status: Option<i32>,
    ) -> Result<Vec<QuestionMetadata>> {
        // 转换为字符串ID进行排除
        let exclude_str_ids: Vec<String> = exclude_ids.iter().map(|id| id.to_string()).collect();

        // 使用查询服务获取知识点下指定难度范围的问题
        let query = QuestionQueryBuilder::new()
            .with_knowledge_ids(vec![knowledge_id])
            .with_difficulty_range(min_difficulty, max_difficulty)
            .with_exclude_ids(exclude_str_ids)
            .with_status(status.unwrap_or(5))
            .with_active_only(true)
            .with_limit(limit);

        let questions = self.query_service.find_questions(query).await?;

        // 转换为基础设施层类型
        let domain_metadata: Vec<DomainQuestionMetadata> = questions
            .into_iter()
            .map(|q| {
                let correct_ratio = q.correct_ratio();
                DomainQuestionMetadata {
                    id: q.id,
                    subject_id: q.subject_id,
                    knowledge_id: q.knowledge_id,
                    knowledge_name: None, // 暂时不提供知识点名称
                    type_id: q.type_id,
                    difficulty: q.difficulty,
                    elo_rating: Some(q.elo_rating),
                    correct_ratio: Some(correct_ratio),
                }
            })
            .collect();

        // 转换为基础设施层类型
        let filtered_questions = self.metadata_converter.domain_to_infra_batch(domain_metadata);

        Ok(filtered_questions)
    }
}
