//! 题目选择服务
//!
//! 统一处理题目筛选、排序、分组等逻辑，支持随机和自适应选择策略
//! 提供可复用的题目选择算法

use async_trait::async_trait;
use std::collections::HashSet;
use tracing::{debug, info, warn};

use crate::infrastructure::dto::question::QuestionContent;
use crate::error::Result;

/// 选择策略枚举
#[derive(Debug, Clone)]
pub enum SelectionStrategy {
    /// 随机选择
    Random,
    /// 优先未答题的随机选择
    RandomWithPriority,
    /// 基于难度的选择
    DifficultyBased { min: f64, max: f64 },
    /// 基于ELO评分的选择
    EloBased { min: f64, max: f64 },
    /// 自适应选择（需要用户能力信息）
    Adaptive { user_ability: f64 },
}

/// 分组策略枚举
#[derive(Debug, Clone)]
pub enum GroupingStrategy {
    /// 不分组
    None,
    /// 按小节分组
    BySection,
    /// 按知识点分组
    ByKnowledge,
    /// 按难度分组
    ByDifficulty,
}

/// 题目选择配置
#[derive(Debug, Clone)]
pub struct SelectionConfig {
    /// 选择策略
    pub strategy: SelectionStrategy,
    /// 分组策略
    pub grouping: GroupingStrategy,
    /// 总题目数量
    pub total_count: usize,
    /// 是否均匀分配到各组
    pub even_distribution: bool,
    /// 排除的题目ID列表
    pub exclude_ids: Option<HashSet<String>>,
    /// 是否允许重复选择
    pub allow_duplicates: bool,
}

impl Default for SelectionConfig {
    fn default() -> Self {
        Self {
            strategy: SelectionStrategy::Random,
            grouping: GroupingStrategy::None,
            total_count: 10,
            even_distribution: true,
            exclude_ids: None,
            allow_duplicates: false,
        }
    }
}

/// 题目分组
#[derive(Debug, Clone)]
pub struct QuestionGroup {
    /// 分组标识
    pub group_id: String,
    /// 分组名称
    pub group_name: Option<String>,
    /// 未答题目列表
    pub unanswered_questions: Vec<QuestionContent>,
    /// 已答题目列表
    pub answered_questions: Vec<QuestionContent>,
    /// 分配的题目数量
    pub allocated_count: usize,
}

impl QuestionGroup {
    /// 创建新的题目分组
    pub fn new(group_id: String) -> Self {
        Self {
            group_id,
            group_name: None,
            unanswered_questions: Vec::new(),
            answered_questions: Vec::new(),
            allocated_count: 0,
        }
    }

    /// 设置分组名称
    pub fn with_name(mut self, name: String) -> Self {
        self.group_name = Some(name);
        self
    }

    /// 添加题目到分组
    pub fn add_question(&mut self, question: QuestionContent, is_answered: bool) {
        if is_answered {
            self.answered_questions.push(question);
        } else {
            self.unanswered_questions.push(question);
        }
    }

    /// 获取总可用题目数量
    pub fn total_available(&self) -> usize {
        self.unanswered_questions.len() + self.answered_questions.len()
    }

    /// 设置分配数量
    pub fn set_allocated_count(&mut self, count: usize) {
        self.allocated_count = count;
    }

    /// 根据策略选择题目
    pub fn select_questions(&mut self, strategy: &SelectionStrategy, count: usize) -> Vec<QuestionContent> {
        match strategy {
            SelectionStrategy::Random => self.select_random(count),
            SelectionStrategy::RandomWithPriority => self.select_random_with_priority(count),
            SelectionStrategy::DifficultyBased { min, max } => {
                self.select_by_difficulty(*min, *max, count)
            }
            SelectionStrategy::EloBased { min, max } => {
                self.select_by_elo(*min, *max, count)
            }
            SelectionStrategy::Adaptive { user_ability: _ } => {
                // 自适应选择暂时回退到优先未答题的随机选择
                self.select_random_with_priority(count)
            }
        }
    }

    /// 随机选择题目
    fn select_random(&mut self, count: usize) -> Vec<QuestionContent> {
        let mut all_questions = Vec::new();
        all_questions.extend(self.unanswered_questions.drain(..));
        all_questions.extend(self.answered_questions.drain(..));

        if all_questions.len() <= count {
            return all_questions;
        }

        fastrand::shuffle(&mut all_questions);
        all_questions.into_iter().take(count).collect()
    }

    /// 优先未答题的随机选择
    fn select_random_with_priority(&mut self, count: usize) -> Vec<QuestionContent> {
        let mut selected = Vec::new();
        let mut remaining_count = count;

        // 1. 优先选择未答题
        if remaining_count > 0 && !self.unanswered_questions.is_empty() {
            let unanswered_count = remaining_count.min(self.unanswered_questions.len());
            fastrand::shuffle(&mut self.unanswered_questions);

            for _ in 0..unanswered_count {
                if let Some(question) = self.unanswered_questions.pop() {
                    selected.push(question);
                    remaining_count -= 1;
                }
            }
        }

        // 2. 如果还需要更多题目，选择已答题
        if remaining_count > 0 && !self.answered_questions.is_empty() {
            let answered_count = remaining_count.min(self.answered_questions.len());
            fastrand::shuffle(&mut self.answered_questions);

            for _ in 0..answered_count {
                if let Some(question) = self.answered_questions.pop() {
                    selected.push(question);
                    remaining_count -= 1;
                }
            }
        }

        debug!(
            "分组 {} 选择题目: 需要={}, 实际选择={}, 未答题剩余={}, 已答题剩余={}",
            self.group_id, count, selected.len(),
            self.unanswered_questions.len(), self.answered_questions.len()
        );

        selected
    }

    /// 基于难度选择题目
    fn select_by_difficulty(&mut self, min: f64, max: f64, count: usize) -> Vec<QuestionContent> {
        let mut filtered_questions = Vec::new();

        // 过滤未答题
        self.unanswered_questions.retain(|q| {
            if q.difficulty >= min && q.difficulty <= max {
                filtered_questions.push(q.clone());
                false
            } else {
                true
            }
        });

        // 如果未答题不够，过滤已答题
        if filtered_questions.len() < count {
            self.answered_questions.retain(|q| {
                if q.difficulty >= min && q.difficulty <= max && filtered_questions.len() < count {
                    filtered_questions.push(q.clone());
                    false
                } else {
                    true
                }
            });
        }

        if filtered_questions.len() <= count {
            return filtered_questions;
        }

        fastrand::shuffle(&mut filtered_questions);
        filtered_questions.into_iter().take(count).collect()
    }

    /// 基于ELO评分选择题目
    fn select_by_elo(&mut self, min: f64, max: f64, count: usize) -> Vec<QuestionContent> {
        let mut filtered_questions = Vec::new();

        // 过滤未答题
        self.unanswered_questions.retain(|q| {
            if q.elo_rating >= min && q.elo_rating <= max {
                filtered_questions.push(q.clone());
                false
            } else {
                true
            }
        });

        // 如果未答题不够，过滤已答题
        if filtered_questions.len() < count {
            self.answered_questions.retain(|q| {
                if q.elo_rating >= min && q.elo_rating <= max && filtered_questions.len() < count {
                    filtered_questions.push(q.clone());
                    false
                } else {
                    true
                }
            });
        }

        if filtered_questions.len() <= count {
            return filtered_questions;
        }

        fastrand::shuffle(&mut filtered_questions);
        filtered_questions.into_iter().take(count).collect()
    }
}

/// 题目选择服务接口
#[async_trait]
pub trait QuestionSelectionService: Send + Sync {
    /// 根据配置选择题目
    async fn select_questions(
        &self,
        questions: Vec<QuestionContent>,
        answered_ids: &HashSet<String>,
        config: SelectionConfig,
    ) -> Result<Vec<QuestionContent>>;

    /// 按小节分组并选择题目
    async fn select_questions_by_sections(
        &self,
        section_questions: Vec<(i32, Vec<QuestionContent>)>,
        answered_ids: &HashSet<String>,
        config: SelectionConfig,
    ) -> Result<Vec<QuestionContent>>;

    /// 计算分组的题目分配
    fn calculate_group_allocations(
        &self,
        groups: &mut [QuestionGroup],
        total_count: usize,
        even_distribution: bool,
    );

    /// 处理题目不足的分组
    fn handle_insufficient_groups(&self, groups: &mut [QuestionGroup]);
}

/// 题目选择服务实现
pub struct QuestionSelectionServiceImpl;

impl QuestionSelectionServiceImpl {
    /// 创建新的选择服务实例
    pub fn new() -> Self {
        Self
    }
}

#[async_trait]
impl QuestionSelectionService for QuestionSelectionServiceImpl {
    async fn select_questions(
        &self,
        questions: Vec<QuestionContent>,
        answered_ids: &HashSet<String>,
        config: SelectionConfig,
    ) -> Result<Vec<QuestionContent>> {
        debug!("开始题目选择: 总题目={}, 已答题目={}, 需要选择={}",
               questions.len(), answered_ids.len(), config.total_count);

        if questions.is_empty() || config.total_count == 0 {
            return Ok(Vec::new());
        }

        match config.grouping {
            GroupingStrategy::None => {
                // 不分组，直接选择
                self.select_from_single_group(questions, answered_ids, &config).await
            }
            GroupingStrategy::BySection => {
                // 按小节分组（需要外部提供分组信息）
                warn!("按小节分组需要使用 select_questions_by_sections 方法");
                self.select_from_single_group(questions, answered_ids, &config).await
            }
            GroupingStrategy::ByKnowledge => {
                // 按知识点分组
                self.select_by_knowledge_groups(questions, answered_ids, &config).await
            }
            GroupingStrategy::ByDifficulty => {
                // 按难度分组
                self.select_by_difficulty_groups(questions, answered_ids, &config).await
            }
        }
    }

    async fn select_questions_by_sections(
        &self,
        section_questions: Vec<(i32, Vec<QuestionContent>)>,
        answered_ids: &HashSet<String>,
        config: SelectionConfig,
    ) -> Result<Vec<QuestionContent>> {
        debug!("按小节选择题目: 小节数={}, 总需要={}",
               section_questions.len(), config.total_count);

        if section_questions.is_empty() || config.total_count == 0 {
            return Ok(Vec::new());
        }

        // 创建小节分组
        let mut groups = Vec::new();
        for (section_id, questions) in section_questions {
            let mut group = QuestionGroup::new(section_id.to_string())
                .with_name(format!("小节{}", section_id));

            // 按已答/未答分类
            for question in questions {
                let is_answered = answered_ids.contains(&question.id);
                group.add_question(question, is_answered);
            }

            groups.push(group);
        }

        // 计算分配
        self.calculate_group_allocations(&mut groups, config.total_count, config.even_distribution);

        // 处理不足的分组
        self.handle_insufficient_groups(&mut groups);

        // 从各分组选择题目
        let mut all_selected = Vec::new();
        for mut group in groups {
            let selected = group.select_questions(&config.strategy, group.allocated_count);
            all_selected.extend(selected);
        }

        // 最终随机打乱
        if matches!(config.strategy, SelectionStrategy::Random | SelectionStrategy::RandomWithPriority) {
            fastrand::shuffle(&mut all_selected);
        }

        info!("按小节选择完成: 返回{}题", all_selected.len());
        Ok(all_selected)
    }

    fn calculate_group_allocations(
        &self,
        groups: &mut [QuestionGroup],
        total_count: usize,
        even_distribution: bool,
    ) {
        if groups.is_empty() {
            return;
        }

        if even_distribution {
            // 均匀分配
            let group_count = groups.len();
            let base_count_per_group = total_count / group_count;
            let remainder = total_count % group_count;

            debug!("均匀分配: 总题量={}, 分组数={}, 基础分配={}, 余量={}",
                   total_count, group_count, base_count_per_group, remainder);

            for (index, group) in groups.iter_mut().enumerate() {
                let allocated_count = base_count_per_group + if index < remainder { 1 } else { 0 };
                group.set_allocated_count(allocated_count);

                debug!("分组 {} 分配: {}题", group.group_id, allocated_count);
            }
        } else {
            // 按比例分配（基于可用题目数量）
            let total_available: usize = groups.iter().map(|g| g.total_available()).sum();
            if total_available == 0 {
                return;
            }

            for group in groups.iter_mut() {
                let ratio = group.total_available() as f64 / total_available as f64;
                let allocated_count = (total_count as f64 * ratio).round() as usize;
                group.set_allocated_count(allocated_count);

                debug!("分组 {} 按比例分配: 可用={}, 比例={:.2}, 分配={}",
                       group.group_id, group.total_available(), ratio, allocated_count);
            }
        }
    }

    fn handle_insufficient_groups(&self, groups: &mut [QuestionGroup]) {
        let mut total_deficit = 0;
        let mut available_groups = Vec::new();

        // 1. 检查每个分组的题目是否充足
        for group in groups.iter_mut() {
            let available_count = group.total_available();
            if available_count < group.allocated_count {
                let deficit = group.allocated_count - available_count;
                total_deficit += deficit;
                group.set_allocated_count(available_count);

                debug!("分组 {} 题目不足: 需要={}, 可用={}, 缺少={}",
                       group.group_id, group.allocated_count + deficit, available_count, deficit);
            } else {
                available_groups.push(group.group_id.clone());
            }
        }

        // 2. 将缺少的题目重新分配给有余量的分组
        if total_deficit > 0 {
            debug!("开始重新分配 {} 题到有余量的分组", total_deficit);

            let mut remaining_deficit = total_deficit;
            let mut group_index = 0;

            while remaining_deficit > 0 && group_index < groups.len() {
                let group = &mut groups[group_index];

                if available_groups.contains(&group.group_id) {
                    let available_count = group.total_available();
                    let current_allocated = group.allocated_count;
                    let can_add = available_count - current_allocated;

                    if can_add > 0 {
                        let add_count = remaining_deficit.min(can_add);
                        group.set_allocated_count(current_allocated + add_count);
                        remaining_deficit -= add_count;

                        debug!("分组 {} 补充分配: 原分配={}, 新分配={}, 补充={}",
                               group.group_id, current_allocated, group.allocated_count, add_count);
                    }
                }

                group_index += 1;
            }

            if remaining_deficit > 0 {
                warn!("仍有 {} 题无法分配，所有分组题目都已用尽", remaining_deficit);
            }
        }
    }
}

impl QuestionSelectionServiceImpl {
    /// 从单个分组选择题目
    async fn select_from_single_group(
        &self,
        questions: Vec<QuestionContent>,
        answered_ids: &HashSet<String>,
        config: &SelectionConfig,
    ) -> Result<Vec<QuestionContent>> {
        let mut group = QuestionGroup::new("single".to_string());

        // 按已答/未答分类
        for question in questions {
            let is_answered = answered_ids.contains(&question.id);
            group.add_question(question, is_answered);
        }

        // 应用排除列表
        if let Some(exclude_ids) = &config.exclude_ids {
            group.unanswered_questions.retain(|q| !exclude_ids.contains(&q.id));
            group.answered_questions.retain(|q| !exclude_ids.contains(&q.id));
        }

        let selected = group.select_questions(&config.strategy, config.total_count);
        Ok(selected)
    }

    /// 按知识点分组选择
    async fn select_by_knowledge_groups(
        &self,
        questions: Vec<QuestionContent>,
        answered_ids: &HashSet<String>,
        config: &SelectionConfig,
    ) -> Result<Vec<QuestionContent>> {
        use std::collections::HashMap;

        let mut knowledge_groups: HashMap<i32, QuestionGroup> = HashMap::new();

        // 按知识点分组
        for question in questions {
            let knowledge_id = question.knowledge_id;
            let group = knowledge_groups.entry(knowledge_id)
                .or_insert_with(|| QuestionGroup::new(knowledge_id.to_string())
                    .with_name(format!("知识点{}", knowledge_id)));

            let is_answered = answered_ids.contains(&question.id);
            group.add_question(question, is_answered);
        }

        let mut groups: Vec<QuestionGroup> = knowledge_groups.into_values().collect();

        // 计算分配
        self.calculate_group_allocations(&mut groups, config.total_count, config.even_distribution);
        self.handle_insufficient_groups(&mut groups);

        // 选择题目
        let mut all_selected = Vec::new();
        for mut group in groups {
            let selected = group.select_questions(&config.strategy, group.allocated_count);
            all_selected.extend(selected);
        }

        fastrand::shuffle(&mut all_selected);
        Ok(all_selected)
    }

    /// 按难度分组选择
    async fn select_by_difficulty_groups(
        &self,
        questions: Vec<QuestionContent>,
        answered_ids: &HashSet<String>,
        config: &SelectionConfig,
    ) -> Result<Vec<QuestionContent>> {
        use std::collections::HashMap;

        let mut difficulty_groups: HashMap<i32, QuestionGroup> = HashMap::new();

        // 按难度分组（将难度向下取整作为分组键）
        for question in questions {
            let difficulty_level = question.difficulty.floor() as i32;
            let group = difficulty_groups.entry(difficulty_level)
                .or_insert_with(|| QuestionGroup::new(difficulty_level.to_string())
                    .with_name(format!("难度{}", difficulty_level)));

            let is_answered = answered_ids.contains(&question.id);
            group.add_question(question, is_answered);
        }

        let mut groups: Vec<QuestionGroup> = difficulty_groups.into_values().collect();

        // 计算分配
        self.calculate_group_allocations(&mut groups, config.total_count, config.even_distribution);
        self.handle_insufficient_groups(&mut groups);

        // 选择题目
        let mut all_selected = Vec::new();
        for mut group in groups {
            let selected = group.select_questions(&config.strategy, group.allocated_count);
            all_selected.extend(selected);
        }

        fastrand::shuffle(&mut all_selected);
        Ok(all_selected)
    }
}

impl Default for QuestionSelectionServiceImpl {
    fn default() -> Self {
        Self::new()
    }
}
