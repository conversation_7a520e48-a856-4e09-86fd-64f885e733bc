//! 依赖注入模块
//!
//! 提供创建和配置服务实例的工厂方法

pub mod ability_factory;
pub mod answer_factory;
pub mod ai_usage_factory;
pub mod conversation_factory;
pub mod exam_factory;
pub mod factory;
pub mod flashcard_factory;
pub mod knowledge_factory;
pub mod new_recommendation_factory;
pub mod plan_factory;
pub mod question_factory;
pub mod service_container;
pub mod user_segmentation_factory;

// 重新导出主要组件
pub use ability_factory::*;
pub use answer_factory::*;
pub use conversation_factory::*;
pub use factory::*;
pub use flashcard_factory::*;
pub use knowledge_factory::*;
pub use new_recommendation_factory::*;
pub use plan_factory::*;
pub use question_factory::*;
pub use service_container::ServiceContainer;
pub use user_segmentation_factory::*;
