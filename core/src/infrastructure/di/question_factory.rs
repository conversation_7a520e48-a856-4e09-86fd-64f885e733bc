//! 问题模块依赖注入
//!
//! 提供问题相关服务的依赖注入，支持配置驱动的服务创建

use sea_orm::DatabaseConnection;
use std::sync::Arc;
use tracing::{debug, info, warn};

use crate::application::question::service::QuestionApplicationService;
use crate::application::question::service_impl::QuestionApplicationServiceImpl;
use crate::application::question::question_bank_service::QuestionBankService;
use crate::application::question::query_service::{QuestionQueryService, QuestionQueryServiceImpl};
use crate::application::question::selection_service::{QuestionSelectionService, QuestionSelectionServiceImpl};
use crate::application::answer::StudentAnswerService;
use crate::domain::question::{QuestionRepository, QuestionBankSessionRepository};
use crate::infrastructure::persistence::question::QuestionRepositoryImpl;
use crate::infrastructure::persistence::StorageManager;
use crate::infrastructure::persistence::question::QuestionBankSessionRepositoryImpl;


/// 题库服务配置
#[derive(Debug, Clone, PartialEq)]
pub enum QuestionBankServiceConfig {
    /// 仅随机推荐
    RandomOnly,
    /// 支持自适应推荐
    WithRecommendation,
}

impl Default for QuestionBankServiceConfig {
    fn default() -> Self {
        Self::WithRecommendation
    }
}

/// 提供问题仓储
pub fn provide_question_repository(db: Arc<DatabaseConnection>) -> Arc<dyn QuestionRepository> {
    Arc::new(QuestionRepositoryImpl::new(db))
}

/// 提供问题查询服务
pub fn provide_question_query_service(
    question_repository: Arc<dyn QuestionRepository>,
) -> Arc<dyn QuestionQueryService> {
    Arc::new(QuestionQueryServiceImpl::new(question_repository))
}

/// 提供问题选择服务
pub fn provide_question_selection_service() -> Arc<dyn QuestionSelectionService> {
    Arc::new(QuestionSelectionServiceImpl::new())
}

/// 提供问题应用服务
pub fn provide_question_service(
    storage_manager: Arc<StorageManager>,
) -> crate::Result<Arc<dyn QuestionApplicationService>> {
    // 获取数据库连接
    let db = storage_manager.sea_orm_db();
    // 创建repository
    let question_repository = provide_question_repository(db.clone());
    // 创建查询服务
    let query_service = provide_question_query_service(question_repository.clone());
    // 创建应用服务
    Ok(Arc::new(QuestionApplicationServiceImpl::new(question_repository, query_service)))
}

/// 提供会话仓储
pub fn provide_question_bank_session_repository(
    storage_manager: Arc<StorageManager>,
) -> Arc<dyn QuestionBankSessionRepository> {
    Arc::new(QuestionBankSessionRepositoryImpl::new(storage_manager))
}

/// 提供答题服务
pub fn provide_answer_service(
    storage_manager: Arc<StorageManager>,
) -> crate::Result<Arc<StudentAnswerService>> {
    // 使用现有的工厂方法创建答题服务
    let db = storage_manager.sea_orm_db().as_ref().clone();
    Ok(crate::infrastructure::di::factory::create_student_answer_service(db, None))
}

/// 提供题库服务
///
/// 默认支持自适应推荐，如果推荐服务创建失败则回退到随机推荐
pub fn provide_question_bank_service(
    storage_manager: Arc<StorageManager>,
) -> crate::Result<Arc<QuestionBankService>> {
    provide_question_bank_service_with_config(storage_manager, QuestionBankServiceConfig::WithRecommendation)
}

/// 提供题库服务（指定配置）
pub fn provide_question_bank_service_with_config(
    storage_manager: Arc<StorageManager>,
    config: QuestionBankServiceConfig,
) -> crate::Result<Arc<QuestionBankService>> {
    debug!("创建题库服务，配置策略: {:?}", config);

    let question_service = provide_question_service(storage_manager.clone())?;
    let selection_service = provide_question_selection_service();
    let session_repository = provide_question_bank_session_repository(storage_manager.clone());

    Ok(match config {
        QuestionBankServiceConfig::RandomOnly => {
            debug!("创建仅随机推荐的题库服务");
            Arc::new(QuestionBankService::new(question_service, selection_service.clone(), session_repository))
        }
        QuestionBankServiceConfig::WithRecommendation => {
            debug!("创建支持自适应推荐的题库服务");
            // 暂时创建基础版本，推荐服务需要在应用启动时异步创建
            warn!("当前使用同步工厂方法，无法创建推荐服务。自适应推荐将回退到随机推荐。");
            info!("要启用自适应推荐，请在应用启动时使用 provide_question_bank_service_async");
            Arc::new(QuestionBankService::new(question_service, selection_service, session_repository))
        }
    })
}

/// 异步提供题库服务（完整功能）
///
/// 在异步上下文中创建支持自适应推荐的题库服务
pub async fn provide_question_bank_service_async(
    storage_manager: Arc<StorageManager>,
) -> crate::Result<Arc<QuestionBankService>> {
    let config = determine_service_config_from_environment();
    provide_question_bank_service_with_config_async(storage_manager, config).await
}

/// 异步提供题库服务（指定配置）
pub async fn provide_question_bank_service_with_config_async(
    storage_manager: Arc<StorageManager>,
    config: QuestionBankServiceConfig,
) -> crate::Result<Arc<QuestionBankService>> {
    debug!("异步创建题库服务，配置策略: {:?}", config);

    let question_service = provide_question_service(storage_manager.clone())?;
    let selection_service = provide_question_selection_service();
    let session_repository = provide_question_bank_session_repository(storage_manager.clone());

    // 尝试创建答题服务（用于已答题判断）
    let answer_service = match provide_answer_service(storage_manager.clone()) {
        Ok(service) => {
            debug!("答题服务创建成功，启用已答题判断功能");
            Some(service)
        }
        Err(e) => {
            warn!("答题服务创建失败，禁用已答题判断功能: {}", e);
            None
        }
    };

    match config {
        QuestionBankServiceConfig::RandomOnly => {
            debug!("创建仅随机推荐的题库服务");
            if let Some(answer_svc) = answer_service {
                Ok(Arc::new(QuestionBankService::with_answer_service(
                    question_service,
                    selection_service.clone(),
                    session_repository,
                    answer_svc,
                )))
            } else {
                Ok(Arc::new(QuestionBankService::new(question_service, selection_service.clone(), session_repository)))
            }
        }
        QuestionBankServiceConfig::WithRecommendation => {
            debug!("创建支持自适应推荐的题库服务");
            // 异步创建推荐服务
            match create_recommendation_service_async(storage_manager).await {
                Ok(recommendation_service) => {
                    debug!("推荐服务创建成功，启用自适应推荐功能");
                    if let Some(answer_svc) = answer_service {
                        Ok(Arc::new(QuestionBankService::with_full_services(
                            question_service,
                            selection_service.clone(),
                            session_repository,
                            recommendation_service,
                            answer_svc,
                        )))
                    } else {
                        Ok(Arc::new(QuestionBankService::with_recommendation_service(
                            question_service,
                            selection_service.clone(),
                            session_repository,
                            recommendation_service,
                        )))
                    }
                }
                Err(e) => {
                    warn!("推荐服务创建失败，回退到随机推荐: {}", e);
                    if let Some(answer_svc) = answer_service {
                        Ok(Arc::new(QuestionBankService::with_answer_service(
                            question_service,
                            selection_service,
                            session_repository,
                            answer_svc,
                        )))
                    } else {
                        Ok(Arc::new(QuestionBankService::new(question_service, selection_service, session_repository)))
                    }
                }
            }
        }
    }
}

/// 异步创建推荐服务
///
/// 在异步上下文中创建推荐服务
async fn create_recommendation_service_async(
    storage_manager: Arc<StorageManager>,
) -> crate::Result<Arc<crate::application::recommendation::NewRecommendationApplicationService>> {
    crate::infrastructure::di::new_recommendation_factory::create_new_recommendation_service_concrete_from_storage_manager(
        storage_manager
    ).await.map_err(|e| crate::error::Error::service(format!("创建推荐服务失败: {}", e)))
}

/// 根据环境确定服务配置
pub fn determine_service_config_from_environment() -> QuestionBankServiceConfig {
    // 检查是否启用自适应推荐
    let enable_adaptive = std::env::var("ENABLE_ADAPTIVE_RECOMMENDATION")
        .map(|v| v.to_lowercase() == "true")
        .unwrap_or(true); // 默认启用

    debug!("环境配置检测: adaptive={}", enable_adaptive);

    if enable_adaptive {
        QuestionBankServiceConfig::WithRecommendation
    } else {
        QuestionBankServiceConfig::RandomOnly
    }
}

/// 检查推荐服务是否可用（异步）
pub async fn is_recommendation_service_available_async(storage_manager: &Arc<StorageManager>) -> bool {
    match create_recommendation_service_async(storage_manager.clone()).await {
        Ok(_) => {
            debug!("推荐服务可用性检查: 可用");
            true
        }
        Err(e) => {
            debug!("推荐服务可用性检查: 不可用 - {}", e);
            false
        }
    }
}

/// 检查推荐服务是否可用（同步，简化版）
///
/// 基于配置和环境变量进行简单检查，不实际创建服务
pub fn is_recommendation_service_likely_available() -> bool {
    let config = determine_service_config_from_environment();
    matches!(config, QuestionBankServiceConfig::WithRecommendation)
}

/// 提供基于仓储的问题应用服务
///
/// 使用领域仓储创建问题应用服务，遵循DDD架构原则
pub fn provide_question_service_with_repository(
    question_repository: Arc<dyn QuestionRepository>,
) -> Arc<dyn QuestionApplicationService> {
    let query_service = provide_question_query_service(question_repository.clone());
    Arc::new(QuestionApplicationServiceImpl::new(question_repository, query_service))
}
