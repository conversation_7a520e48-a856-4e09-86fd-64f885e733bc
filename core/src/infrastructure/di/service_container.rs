//! 服务容器
//!
//! 提供统一的依赖注入容器，管理所有服务的创建和依赖关系

use anyhow::Result;
use sea_orm::DatabaseConnection;
use std::sync::Arc;

use crate::algorithms::AlgorithmRegistry;
use crate::application::answer::AnswerProcessingService;
use crate::application::exam::ExamApplicationService;
use crate::application::flashcard::FlashcardRecommendationService;
use crate::application::flashcard::service::FlashCardService;
use crate::application::plan::PlanApplicationService;
use crate::application::question::service::QuestionApplicationService;
use crate::application::recommendation::MasteryRecommendationService;
use crate::config::Config as CoreConfig;
use crate::infrastructure::di::user_segmentation_factory::{
    UserSegmentationFactory, UserSegmentationServices,
};
use crate::services::AbilityService;
// 已删除：RecommendationService 导入 - 未被任何API处理器使用
use crate::services::elo::KnowledgeEloServiceTrait;
use crate::services::knowledge::KnowledgeServiceTrait;

use crate::services::user::UserService;
use crate::infrastructure::persistence::StorageManager;

/// 服务容器，管理所有服务实例
pub struct ServiceContainer {
    /// 核心配置
    config: Arc<CoreConfig>,
    /// 存储管理器
    storage_manager: Arc<StorageManager>,
    /// 数据库连接
    db_connection: DatabaseConnection,
    /// 算法注册表
    algorithm_registry: Option<Arc<AlgorithmRegistry>>,
    /// 知识服务
    knowledge_service: Option<Arc<dyn KnowledgeServiceTrait>>,
    /// 能力服务
    ability_service: Option<Arc<AbilityService>>,
    // 已删除：recommendation_service - 未被任何API处理器使用
    /// ELO服务
    elo_service: Option<Arc<dyn KnowledgeEloServiceTrait + Send + Sync>>,
    /// 用户服务
    user_service: Option<Arc<UserService>>,
    /// 问题服务
    question_service: Option<Arc<dyn QuestionApplicationService>>,
    /// 题库服务
    question_bank_service: Option<Arc<crate::application::question::question_bank_service::QuestionBankService>>,
    /// 计划服务
    plan_service: Option<Arc<PlanApplicationService>>,
    /// 计划门面服务 (新架构)
    plan_facade_service: Option<Arc<crate::application::plan::PlanFacadeService>>,
    /// 计划任务服务 (新架构)
    plan_task_service: Option<Arc<crate::application::plan::PlanTaskService>>,
    /// 闪卡推荐服务
    flashcard_recommendation_service: Option<Arc<FlashcardRecommendationService>>,
    /// 闪卡服务
    flashcard_service: Option<Arc<FlashCardService>>,

    /// 掌握度服务V2
    mastery_v2_service: Option<Arc<dyn MasteryRecommendationService>>,
    /// 新的DDD架构推荐服务
    new_recommendation_service: Option<Arc<dyn MasteryRecommendationService>>,
    /// 答题处理服务
    answer_processing_service: Option<Arc<AnswerProcessingService>>,
    /// 考试服务
    exam_service: Option<Arc<dyn ExamApplicationService>>,
    /// 用户分群服务
    user_segmentation_services: Option<UserSegmentationServices>,
}

impl ServiceContainer {
    /// 创建新的服务容器
    pub fn new(
        config: Arc<CoreConfig>,
        storage_manager: Arc<StorageManager>,
        db_connection: DatabaseConnection,
    ) -> Self {
        Self {
            config,
            storage_manager,
            db_connection,
            algorithm_registry: None,
            knowledge_service: None,
            ability_service: None,
            // 已删除：recommendation_service - 未被任何API处理器使用
            elo_service: None,
            user_service: None,
            question_service: None,
            question_bank_service: None,
            plan_service: None,
            plan_facade_service: None,
            plan_task_service: None,
            flashcard_recommendation_service: None,
            flashcard_service: None,

            mastery_v2_service: None,
            new_recommendation_service: None,
            answer_processing_service: None,
            exam_service: None,
            user_segmentation_services: None,
        }
    }

    /// 初始化所有核心服务
    pub async fn initialize_core_services(&mut self) -> Result<()> {
        // 1. 初始化算法注册表
        self.initialize_algorithm_registry().await?;

        // 2. 初始化知识服务
        self.initialize_knowledge_service().await?;

        // 3. 初始化能力服务
        self.initialize_ability_service()?;

        // 已删除：initialize_recommendation_service - 未被任何API处理器使用

        // 5. 初始化ELO服务
        self.initialize_elo_service()?;

        // 6. 初始化用户服务
        self.initialize_user_service()?;

        // 7. 初始化问题服务
        self.initialize_question_service()?;

        // 8. 初始化题库服务
        self.initialize_question_bank_service().await?;

        Ok(())
    }

    /// 初始化应用层服务
    pub async fn initialize_application_services(&mut self) -> Result<()> {
        // 1. 初始化计划服务
        self.initialize_plan_service()?;

        // 2. 初始化闪卡相关服务
        self.initialize_flashcard_services()?;

        // 3. 初始化闪卡推荐服务（需要计划服务）
        self.initialize_flashcard_recommendation_service()?;

        // 4. 初始化掌握度服务
        self.initialize_mastery_services().await?;

        // 5. 初始化答题处理服务
        self.initialize_answer_processing_service()?;

        // 6. 初始化用户分群服务
        self.initialize_user_segmentation_services()?;

        // 7. 初始化考试服务
        self.initialize_exam_service()?;

        Ok(())
    }

    /// 获取算法注册表
    pub fn algorithm_registry(&self) -> Option<Arc<AlgorithmRegistry>> {
        self.algorithm_registry.clone()
    }

    /// 获取知识服务
    pub fn knowledge_service(&self) -> Option<Arc<dyn KnowledgeServiceTrait>> {
        self.knowledge_service.clone()
    }

    /// 获取能力服务
    pub fn ability_service(&self) -> Option<Arc<AbilityService>> {
        self.ability_service.clone()
    }

    // 已删除：recommendation_service getter - 未被任何API处理器使用

    /// 获取ELO服务
    pub fn elo_service(&self) -> Option<Arc<dyn KnowledgeEloServiceTrait + Send + Sync>> {
        self.elo_service.clone()
    }

    /// 获取用户服务
    pub fn user_service(&self) -> Option<Arc<UserService>> {
        self.user_service.clone()
    }

    /// 获取问题服务
    pub fn question_service(&self) -> Option<Arc<dyn QuestionApplicationService>> {
        self.question_service.clone()
    }

    /// 获取题库服务
    pub fn question_bank_service(&self) -> Option<Arc<crate::application::question::question_bank_service::QuestionBankService>> {
        self.question_bank_service.clone()
    }

    /// 获取计划服务
    pub fn plan_service(&self) -> Option<Arc<PlanApplicationService>> {
        self.plan_service.clone()
    }

    /// 获取计划门面服务 (新架构)
    pub fn plan_facade_service(&self) -> Option<Arc<crate::application::plan::PlanFacadeService>> {
        self.plan_facade_service.clone()
    }

    /// 获取计划任务服务 (新架构)
    pub fn plan_task_service(&self) -> Option<Arc<crate::application::plan::PlanTaskService>> {
        self.plan_task_service.clone()
    }

    /// 获取闪卡推荐服务
    pub fn flashcard_recommendation_service(&self) -> Option<Arc<FlashcardRecommendationService>> {
        self.flashcard_recommendation_service.clone()
    }

    /// 获取闪卡服务
    pub fn flashcard_service(&self) -> Option<Arc<FlashCardService>> {
        self.flashcard_service.clone()
    }



    /// 获取掌握度服务V2
    pub fn mastery_v2_service(&self) -> Option<Arc<dyn MasteryRecommendationService>> {
        self.mastery_v2_service.clone()
    }

    /// 获取新的DDD架构推荐服务
    pub fn new_recommendation_service(&self) -> Option<Arc<dyn MasteryRecommendationService>> {
        self.new_recommendation_service.clone()
    }

    /// 获取答题处理服务
    pub fn answer_processing_service(&self) -> Option<Arc<AnswerProcessingService>> {
        self.answer_processing_service.clone()
    }

    /// 获取考试服务
    pub fn exam_service(&self) -> Option<Arc<dyn ExamApplicationService>> {
        self.exam_service.clone()
    }

    /// 获取用户分群服务
    pub fn user_segmentation_services(&self) -> Option<UserSegmentationServices> {
        self.user_segmentation_services.clone()
    }

    /// 获取存储管理器
    pub fn storage_manager(&self) -> Arc<StorageManager> {
        self.storage_manager.clone()
    }

    /// 获取配置
    pub fn config(&self) -> Arc<CoreConfig> {
        self.config.clone()
    }

    // 私有初始化方法
    async fn initialize_algorithm_registry(&mut self) -> Result<()> {
        let mut registry = AlgorithmRegistry::new();

        // 暂时不提供 QuestionStorage，RandomRecommender 将不可用
        // TODO: 将 RandomRecommender 迁移到新的 DDD 架构后重新启用

        registry.register_all_algorithms()?;
        self.algorithm_registry = Some(Arc::new(registry));

        Ok(())
    }

    async fn initialize_knowledge_service(&mut self) -> Result<()> {
        use crate::services::knowledge::{KnowledgeService, KnowledgeServiceConfig};

        let config = KnowledgeServiceConfig::default();
        let service = Arc::new(KnowledgeService::new(self.db_connection.clone(), config));

        // 初始化缓存
        if let Err(e) = service.clear_knowledge_tree_cache().await {
            tracing::warn!("清除旧的知识树缓存失败: {}", e);
        }

        if let Err(e) = service.init_and_cache_knowledge_tree().await {
            tracing::warn!("知识树预加载失败: {}", e);
        }

        self.knowledge_service = Some(service);
        Ok(())
    }

    fn initialize_ability_service(&mut self) -> Result<()> {
        let algorithm_registry = self
            .algorithm_registry
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("算法注册表未初始化"))?;

        let ability_storage = self.storage_manager.ability_storage();

        self.ability_service = Some(super::ability_factory::create_ability_service(
            &self.config,
            algorithm_registry.clone(),
            ability_storage,
        )?);

        Ok(())
    }

    // 已删除：initialize_recommendation_service - 未被任何API处理器使用

    fn initialize_elo_service(&mut self) -> Result<()> {
        self.elo_service = Some(super::ability_factory::create_elo_service(
            self.storage_manager.clone(),
            &self.config,
        )?);

        Ok(())
    }

    fn initialize_user_service(&mut self) -> Result<()> {
        self.user_service = Some(Arc::new(UserService::new(&self.config)));
        Ok(())
    }

    fn initialize_question_service(&mut self) -> Result<()> {
        self.question_service = Some(super::factory::create_question_service(
            self.storage_manager.clone(),
        )?);
        Ok(())
    }

    async fn initialize_question_bank_service(&mut self) -> Result<()> {
        use crate::infrastructure::di::question_factory;

        // 使用异步工厂方法创建题库服务
        let service = question_factory::provide_question_bank_service_async(
            self.storage_manager.clone()
        ).await.map_err(|e| crate::error::Error::service(format!("创建题库服务失败: {}", e)))?;

        self.question_bank_service = Some(service);
        tracing::info!("题库服务初始化成功");
        Ok(())
    }

    fn initialize_plan_service(&mut self) -> Result<()> {
        let knowledge_service = self
            .knowledge_service
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("知识服务未初始化"))?;

        let ability_service = self
            .ability_service
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("能力服务未初始化"))?;

        // 初始化旧的plan服务 (保持向后兼容)
        self.plan_service = Some(super::plan_factory::create_plan_service(
            self.storage_manager.clone(),
            ability_service.clone(),
            knowledge_service.clone(),
        )?);

        // 初始化新的plan门面服务 (推荐使用)
        self.plan_facade_service = Some(super::plan_factory::create_plan_facade_service(
            self.storage_manager.clone(),
            ability_service.clone(),
            knowledge_service.clone(),
        )?);

        // 初始化新的plan任务服务
        self.plan_task_service = Some(super::plan_factory::create_plan_task_service(
            self.storage_manager.clone(),
            ability_service.clone(),
            knowledge_service.clone(),
        )?);

        Ok(())
    }

    fn initialize_flashcard_services(&mut self) -> Result<()> {
        // 初始化闪卡服务（注入计划服务）
        self.flashcard_service = Some(
            super::flashcard_factory::create_full_flashcard_service_with_plan(
                Arc::new(self.db_connection.clone()),
                super::flashcard_factory::create_card_repository(Arc::new(
                    self.db_connection.clone(),
                )),
                super::flashcard_factory::create_user_card_progress_repository(Arc::new(
                    self.db_connection.clone(),
                )),
                super::factory::create_fsrs_submission_repository(Arc::new(
                    self.db_connection.clone(),
                )),
                self.plan_service.clone(), // 注入计划服务
            ),
        );

        Ok(())
    }

    /// 初始化闪卡推荐服务（需要在计划服务之后）
    fn initialize_flashcard_recommendation_service(&mut self) -> Result<()> {
        let db = Arc::new(self.db_connection.clone());

        // 创建推荐服务，注入计划服务
        self.flashcard_recommendation_service = Some(
            super::factory::create_flashcard_recommendation_service_with_plan(
                db,
                self.plan_service.clone(), // 注入计划服务
            ),
        );

        Ok(())
    }

    async fn initialize_mastery_services(&mut self) -> Result<()> {
        // 初始化掌握度服务V2
        self.mastery_v2_service = super::new_recommendation_factory::create_mastery_v2_service(
            self.storage_manager.clone(),
            self.db_connection.clone(),
            self.plan_service.clone(),
        )
        .await
        .ok();

        // 初始化新的DDD架构推荐服务（注入计划服务）
        match self.create_new_recommendation_service_with_plan_service().await {
            Ok(service) => {
                self.new_recommendation_service = Some(service);
                tracing::info!("新推荐服务初始化成功（已注入计划服务）");
            }
            Err(e) => {
                tracing::error!("新推荐服务初始化失败: {}", e);
                self.new_recommendation_service = None;
            }
        }

        Ok(())
    }

    /// 创建带计划服务的新推荐服务
    async fn create_new_recommendation_service_with_plan_service(
        &self,
    ) -> anyhow::Result<Arc<dyn MasteryRecommendationService>> {
        // 获取数据库连接
        let db = Arc::new(self.db_connection.clone());

        // 创建知识服务实例
        let knowledge_service_config = crate::services::knowledge::models::KnowledgeServiceConfig::default();
        let knowledge_service = Arc::new(crate::services::knowledge::KnowledgeService::new(
            self.db_connection.clone(),
            knowledge_service_config,
        ));

        // 使用带知识服务和计划服务的工厂方法
        let new_service = super::new_recommendation_factory::create_new_recommendation_service_with_knowledge_and_plan_service(
            db,
            knowledge_service,
            self.plan_service.clone(), // 注入计划服务
        ).await?;

        Ok(new_service)
    }

    fn initialize_answer_processing_service(&mut self) -> Result<()> {
        if let (Some(elo_service), Some(plan_service), Some(flashcard_service)) = (
            self.elo_service.as_ref(),
            self.plan_service.as_ref(),
            self.flashcard_service.as_ref(),
        ) {
            self.answer_processing_service =
                Some(super::answer_factory::create_answer_processing_service(
                    elo_service.clone(),
                    plan_service.clone(),
                    flashcard_service.clone(),
                )?);
        }

        Ok(())
    }

    fn initialize_user_segmentation_services(&mut self) -> Result<()> {
        // 创建能力统计服务 - 需要知识服务
        if let Some(knowledge_service) = &self.knowledge_service {
            let ability_stats_service =
                crate::infrastructure::di::ability_factory::create_ability_stats_service(
                    Arc::new(self.db_connection.clone()),
                    knowledge_service.clone(),
                    Arc::new(crate::algorithms::elo::EloConfig::default()),
                );

            // 使用工厂创建用户分群服务 - 启用仓储
            let services = UserSegmentationFactory::create_services(
                Arc::new(self.db_connection.clone()),
                ability_stats_service,
                self.flashcard_service.clone(),
                self.knowledge_service.clone().unwrap(),
                true, // 启用仓储
            )?;

            self.user_segmentation_services = Some(services);
        } else {
            return Err(anyhow::anyhow!("知识服务未初始化，无法创建用户分群服务"));
        }

        Ok(())
    }

    fn initialize_exam_service(&mut self) -> Result<()> {
        if let Some(question_service) = &self.question_service {
            self.exam_service = Some(super::exam_factory::create_exam_application_service(
                self.db_connection.clone(),
                question_service.clone(),
            )?);
        }
        Ok(())
    }
}
