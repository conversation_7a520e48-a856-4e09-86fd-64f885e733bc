//! 新推荐应用服务的DDD工厂
//!
//! 使用真实的DDD架构创建NewRecommendationApplicationService
//! 同时提供从旧recommendation_factory迁移过来的功能

use sea_orm::DatabaseConnection;
use std::sync::Arc;

use crate::{
    Result,
    algorithms::adaptive_mastery::AdaptiveMasteryAlgorithm,
    application::{
        question::{
            service_impl::QuestionApplicationServiceImpl,
            query_service::QuestionQueryServiceImpl,
        },
        recommendation::NewRecommendationApplicationService,
    },
    // 已删除：CoreConfig 导入 - 不再需要
    domain::{
        ability::repository::KnowledgeRepository as DomainKnowledgeRepository,
        answer::repository::StudentAnswerRepository, question::repository::QuestionRepository,
        recommendation::services::recommendation_engine::RecommendationEngine,
    },
    infrastructure::persistence::{
        ability::repository_impl::KnowledgeRepositoryImpl,
        answer::repository_impl::AnswerRepositoryImpl,
        question::repository_impl::QuestionRepositoryImpl,
    },
    services::knowledge::KnowledgeService,
    infrastructure::persistence::StorageManager,
    storage::{
        traits::{StudentKnowledgeProgressStorage, knowledge::KnowledgeRepository},
    },
    infrastructure::persistence::storage_impl::{SeaOrmKnowledgeRepository, SeaOrmStudentKnowledgeProgressStorage},
};

/// 创建新的推荐应用服务
///
/// 使用DDD架构，注入所有必要的仓储依赖
pub async fn create_new_recommendation_service(
    db: Arc<DatabaseConnection>,
) -> Result<Arc<NewRecommendationApplicationService>> {
    create_new_recommendation_service_with_plan_service(db, None).await
}

/// 创建带计划服务的推荐应用服务
///
/// 使用DDD架构，注入所有必要的仓储依赖，包括计划服务
pub async fn create_new_recommendation_service_with_plan_service(
    db: Arc<DatabaseConnection>,
    plan_service: Option<Arc<crate::application::plan::service::PlanApplicationService>>,
) -> Result<Arc<NewRecommendationApplicationService>> {
    // 使用全局StorageManager
    let _storage_manager = StorageManager::global().ok_or_else(|| {
        crate::Error::service("需要初始化全局StorageManager才能使用高效查询".to_string())
    })?;
    // 1. 创建推荐引擎
    let adaptive_algo = AdaptiveMasteryAlgorithm::default();
    let recommendation_engine = RecommendationEngine::new(adaptive_algo);

    // 2. 创建知识点仓储（存储层）
    let knowledge_repository: Arc<dyn KnowledgeRepository + Send + Sync> =
        Arc::new(SeaOrmKnowledgeRepository::new(db.clone()));

    // 3. 创建domain层的KnowledgeRepository（用于批量查询优化）
    // 由于这个工厂方法没有knowledge_service，我们创建一个临时的
    let knowledge_service_config = crate::services::knowledge::models::KnowledgeServiceConfig::default();
    let temp_knowledge_service = Arc::new(crate::services::knowledge::KnowledgeService::new(
        (*db).clone(),
        knowledge_service_config,
    ));
    let domain_knowledge_repository: Arc<dyn DomainKnowledgeRepository + Send + Sync> =
        Arc::new(KnowledgeRepositoryImpl::new(temp_knowledge_service, db.clone()));

    // 已删除：ability_repository - 未被使用

    // 4. 创建答题记录仓储（领域层）
    let answer_repository: Arc<dyn StudentAnswerRepository + Send + Sync> =
        Arc::new(AnswerRepositoryImpl::new((*db).clone()));

    // 5. 创建学生进度存储（存储层）
    let progress_storage: Arc<dyn StudentKnowledgeProgressStorage + Send + Sync> =
        Arc::new(SeaOrmStudentKnowledgeProgressStorage::new(db.clone()));

    // 6. 创建题目仓储（领域层）
    let question_repository: Arc<dyn QuestionRepository + Send + Sync> =
        Arc::new(QuestionRepositoryImpl::new(db.clone()));

    // 7. 创建题目应用服务（用于高效查询）
    // 需要获取StorageManager来创建question_storage
    let storage_manager = StorageManager::global().ok_or_else(|| {
        crate::Error::service("需要初始化全局StorageManager才能使用高效查询".to_string())
    })?;
    let query_service = Arc::new(QuestionQueryServiceImpl::new(question_repository.clone()));
    let question_service: Arc<
        dyn crate::application::question::service::QuestionApplicationService,
    > = Arc::new(QuestionApplicationServiceImpl::new(question_repository.clone(), query_service));

    // 8. 创建能力服务（与V1一致）
    let ability_config = crate::services::ability::types::AbilityServiceConfig::default();
    let algorithm_registry = Arc::new(crate::algorithms::AlgorithmRegistry::new());
    let ability_service = Arc::new(crate::services::ability::AbilityService::new(
        ability_config,
        algorithm_registry,
        Some(storage_manager.ability_storage()),
        None, // ability_view暂时为None
    ));

    // 9. 创建能力存储（与V1一致）
    let ability_storage: Arc<dyn crate::storage::traits::ability::AbilityStorage + Send + Sync> =
        Arc::new(crate::storage::entities::student::SeaOrmAbilityStorage::new((*db).clone()));

    // 10. 创建自适应掌握算法（与V1一致）
    let adaptive_algo_for_service = AdaptiveMasteryAlgorithm::default();

    // 11. 创建服务
    let mut service = NewRecommendationApplicationService::new(
        recommendation_engine,
        knowledge_repository,
        domain_knowledge_repository,
        answer_repository,
        progress_storage,
        question_repository,
        question_service,
        ability_service,
        ability_storage,
        adaptive_algo_for_service,
        None, // knowledge_service: 在这个工厂方法中不使用知识服务
    );

    // 12. 注入计划服务（如果提供）
    if let Some(plan_svc) = plan_service {
        service = service.with_plan_service(plan_svc);
    }

    Ok(Arc::new(service))
}

/// 创建带知识服务的推荐应用服务
///
/// 当需要KnowledgeRepositoryImpl时使用（需要知识服务依赖）
pub async fn create_new_recommendation_service_with_knowledge_service(
    db: Arc<DatabaseConnection>,
    knowledge_service: Arc<KnowledgeService>,
) -> Result<Arc<NewRecommendationApplicationService>> {
    create_new_recommendation_service_with_knowledge_and_plan_service(db, knowledge_service, None).await
}

/// 创建带知识服务和计划服务的推荐应用服务
///
/// 当需要KnowledgeRepositoryImpl和计划服务时使用
pub async fn create_new_recommendation_service_with_knowledge_and_plan_service(
    db: Arc<DatabaseConnection>,
    knowledge_service: Arc<KnowledgeService>,
    plan_service: Option<Arc<crate::application::plan::service::PlanApplicationService>>,
) -> Result<Arc<NewRecommendationApplicationService>> {
    // 1. 创建推荐引擎
    let adaptive_algo = AdaptiveMasteryAlgorithm::default();
    let recommendation_engine = RecommendationEngine::new(adaptive_algo);

    // 2. 创建知识点仓储（存储层）
    let knowledge_repository: Arc<dyn KnowledgeRepository + Send + Sync> =
        Arc::new(SeaOrmKnowledgeRepository::new(db.clone()));

    // 已删除：ability_repository - 未被使用
    // 创建domain层的KnowledgeRepository（用于批量查询优化）
    let domain_knowledge_repository: Arc<dyn DomainKnowledgeRepository + Send + Sync> =
        Arc::new(KnowledgeRepositoryImpl::new(knowledge_service.clone(), db.clone()));

    // 4. 创建答题记录仓储（领域层）
    let answer_repository: Arc<dyn StudentAnswerRepository + Send + Sync> =
        Arc::new(AnswerRepositoryImpl::new((*db).clone()));

    // 5. 创建学生进度存储（存储层）
    let progress_storage: Arc<dyn StudentKnowledgeProgressStorage + Send + Sync> =
        Arc::new(SeaOrmStudentKnowledgeProgressStorage::new(db.clone()));

    // 6. 创建题目仓储（领域层）
    let question_repository: Arc<dyn QuestionRepository + Send + Sync> =
        Arc::new(QuestionRepositoryImpl::new(db.clone()));

    // 7. 创建题目应用服务（用于高效查询）
    // 注意：不再需要 StorageManager，已迁移到新的 DDD 架构
    let query_service = Arc::new(QuestionQueryServiceImpl::new(question_repository.clone()));
    let question_service: Arc<
        dyn crate::application::question::service::QuestionApplicationService,
    > = Arc::new(QuestionApplicationServiceImpl::new(question_repository.clone(), query_service));

    // 8. 创建能力服务（与V1一致）
    let ability_config = crate::services::ability::types::AbilityServiceConfig::default();
    let algorithm_registry = Arc::new(crate::algorithms::AlgorithmRegistry::new());

    // 创建能力存储（与V1一致）
    let ability_storage: Arc<dyn crate::storage::traits::ability::AbilityStorage + Send + Sync> =
        Arc::new(crate::storage::entities::student::SeaOrmAbilityStorage::new((*db).clone()));

    let ability_service = Arc::new(crate::services::ability::AbilityService::new(
        ability_config,
        algorithm_registry,
        Some(ability_storage.clone()),
        None, // ability_view暂时为None
    ));

    // 能力存储已在上面创建

    // 10. 创建自适应掌握算法（与V1一致）
    let adaptive_algo_for_service = AdaptiveMasteryAlgorithm::default();

    // 11. 创建服务
    let mut service = NewRecommendationApplicationService::new(
        recommendation_engine,
        knowledge_repository,
        domain_knowledge_repository,
        answer_repository,
        progress_storage,
        question_repository,
        question_service,
        ability_service,
        ability_storage,
        adaptive_algo_for_service,
        Some(knowledge_service), // knowledge_service: 在这个工厂方法中使用知识服务
    );

    // 12. 注入计划服务（如果提供）
    if let Some(plan_svc) = plan_service {
        service = service.with_plan_service(plan_svc);
    }

    Ok(Arc::new(service))
}

// ============================================================================
// 从 recommendation_factory.rs 迁移过来的功能
// ============================================================================

// 已删除：create_recommendation_service - 未被任何API处理器使用



/// 创建掌握度服务V2（已重构为使用新的DDD架构）
///
/// 从旧的recommendation_factory.rs迁移过来
pub async fn create_mastery_v2_service(
    storage_manager: Arc<StorageManager>,
    _db_conn: sea_orm::DatabaseConnection,
    _plan_service: Option<Arc<crate::application::plan::PlanApplicationService>>,
) -> anyhow::Result<Arc<dyn crate::application::recommendation::MasteryRecommendationService>> {
    // 直接使用新的DDD架构推荐服务
    create_new_recommendation_service_from_storage_manager(storage_manager).await
}

/// 创建新的DDD架构推荐服务（从StorageManager）
///
/// 从旧的recommendation_factory.rs迁移过来，适配StorageManager接口
pub async fn create_new_recommendation_service_from_storage_manager(
    storage_manager: Arc<StorageManager>,
) -> anyhow::Result<Arc<dyn crate::application::recommendation::MasteryRecommendationService>> {
    // 创建推荐引擎领域服务
    let adaptive_algo = AdaptiveMasteryAlgorithm::default();
    let _recommendation_engine =
        crate::domain::recommendation::services::recommendation_engine::RecommendationEngine::new(
            adaptive_algo,
        );

    // 获取数据库连接
    let db = storage_manager.sea_orm_db();

    // 创建知识服务实例
    let knowledge_service_config = crate::services::knowledge::models::KnowledgeServiceConfig::default();
    let knowledge_service = Arc::new(crate::services::knowledge::KnowledgeService::new(
        (*db).clone(),
        knowledge_service_config,
    ));

    // 使用带知识服务的工厂方法
    let new_service = create_new_recommendation_service_with_knowledge_service(
        db,
        knowledge_service
    ).await?;

    Ok(new_service)
}

/// 创建新的DDD架构推荐服务（从StorageManager，返回具体类型）
///
/// 用于需要具体类型的场景，如 QuestionBankService
pub async fn create_new_recommendation_service_concrete_from_storage_manager(
    storage_manager: Arc<StorageManager>,
) -> anyhow::Result<Arc<NewRecommendationApplicationService>> {
    // 创建推荐引擎领域服务
    let adaptive_algo = AdaptiveMasteryAlgorithm::default();
    let _recommendation_engine =
        crate::domain::recommendation::services::recommendation_engine::RecommendationEngine::new(
            adaptive_algo,
        );

    // 获取数据库连接
    let db = storage_manager.sea_orm_db();

    // 创建知识服务实例
    let knowledge_service_config = crate::services::knowledge::models::KnowledgeServiceConfig::default();
    let knowledge_service = Arc::new(crate::services::knowledge::KnowledgeService::new(
        (*db).clone(),
        knowledge_service_config,
    ));

    // 使用带知识服务的工厂方法
    create_new_recommendation_service_with_knowledge_service(
        db,
        knowledge_service
    ).await.map_err(|e| anyhow::anyhow!("Failed to create recommendation service: {}", e))
}


