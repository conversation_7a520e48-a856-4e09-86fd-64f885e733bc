//! 数据存储实现模块
//!
//! 提供各种存储接口的具体实现
//! 注意：新的存储实现应该在infrastructure层实现，这里的实现主要用于向后兼容
// 导出子模块
pub mod ability_view;
pub mod elo;
pub mod knowledge;
// pub mod question_storage; // 已删除，请使用新的 DDD 架构
pub mod streak;
pub mod student_knowledge_progress;

// 重导出所有实现
pub use ability_view::SeaOrmAbilityView;
pub use elo::*;
pub use knowledge::SeaOrmKnowledgeRepository;
// 注意：SeaOrmQuestionStorage 已被删除，请使用新的 DDD 架构
pub use student_knowledge_progress::SeaOrmStudentKnowledgeProgressStorage;
