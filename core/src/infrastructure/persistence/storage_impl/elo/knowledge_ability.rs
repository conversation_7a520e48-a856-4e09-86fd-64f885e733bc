use async_trait::async_trait;
use chrono::Utc;
use sea_orm::query::*;
use sea_orm::{ActiveModelTrait, ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter, Set};
use std::fmt;

use crate::Error;
use crate::Result;
use crate::algorithms::elo::EloConfig;
use crate::storage::DatabaseType;
use crate::storage::entities::knowledge_ability::{ActiveModel, Entity, Model};
use crate::storage::entities::{student, question, student_answer};
use crate::storage::traits::{AbilityStorage, KnowledgeEloStorage, Storage};

/// 知识点Elo存储的SeaORM实现
#[derive(Clone)]
pub struct SeaOrmKnowledgeEloStorage {
    /// 数据库连接
    db: DatabaseConnection,
    elo_config: EloConfig,
}

impl fmt::Debug for SeaOrmKnowledgeEloStorage {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("SeaOrmKnowledgeEloStorage")
            .field("db", &"DatabaseConnection")
            .finish()
    }
}

impl SeaOrmKnowledgeEloStorage {
    /// 创建新的知识点Elo存储
    pub fn new(db: DatabaseConnection, elo_config: EloConfig) -> Self {
        Self { db, elo_config }
    }

    /// 创建空的知识点Elo存储，用于测试和无需数据库的场景
    pub fn new_empty() -> Self {
        Self {
            db: sea_orm::DatabaseConnection::default(),
            elo_config: EloConfig::default(),
        }
    }
}

#[async_trait]
impl Storage for SeaOrmKnowledgeEloStorage {
    fn name(&self) -> &str {
        "sea_orm_knowledge_elo"
    }

    fn db_type(&self) -> DatabaseType {
        DatabaseType::Postgres
    }

    async fn initialize(&mut self, _config: Box<dyn crate::storage::StorageConfig>) -> Result<()> {
        // 无需额外初始化
        Ok(())
    }

    async fn health_check(&self) -> Result<bool> {
        match Entity::find().one(&self.db).await {
            Ok(_) => Ok(true),
            Err(e) => {
                tracing::error!("知识点Elo存储健康检查失败: {}", e);
                Ok(false)
            }
        }
    }

    fn as_any(&self) -> &dyn std::any::Any {
        self
    }
}

// 实现 AbilityStorage trait
#[async_trait]
impl AbilityStorage for SeaOrmKnowledgeEloStorage {
    /// 获取用户能力值
    async fn get_user_ability(&self, user_id: &str) -> Result<Option<f64>> {
        // 转发到 student 实体进行查询
        match user_id.parse::<i64>() {
            Ok(student_id) => {
                let student =
                    student::Entity::find_by_id(student_id)
                        .one(&self.db)
                        .await
                        .map_err(|e| Error::storage(format!("获取用户能力值失败: {}", e)))?;

                Ok(student.map(|s| s.elo_rating as f64))
            }
            Err(_) => Err(Error::storage(format!("无效的用户ID: {}", user_id))),
        }
    }

    /// 设置用户能力值
    async fn set_user_ability(&self, user_id: &str, ability: f64) -> Result<()> {
        // 转发到 student 实体进行更新
        match user_id.parse::<i64>() {
            Ok(student_id) => {
                let student =
                    student::Entity::find_by_id(student_id)
                        .one(&self.db)
                        .await
                        .map_err(|e| Error::storage(format!("查询用户失败: {}", e)))?;

                if let Some(student) = student {
                    // 更新现有记录
                    let mut student_model: student::ActiveModel =
                        student.into();
                    student_model.elo_rating = Set(ability as f64);
                    student_model.updated_at = Set(Utc::now());

                    student_model
                        .update(&self.db)
                        .await
                        .map_err(|e| Error::storage(format!("更新用户能力值失败: {}", e)))?;
                } else {
                    return Err(Error::storage(format!("用户不存在: {}", user_id)));
                }

                Ok(())
            }
            Err(_) => Err(Error::storage(format!("无效的用户ID: {}", user_id))),
        }
    }

    /// 批量获取用户能力值
    async fn get_user_abilities(&self, user_ids: &[String]) -> Result<Vec<(String, Option<f64>)>> {
        // 转发到 student 实体进行批量查询
        let mut int_ids = Vec::new();
        let mut id_map = Vec::new();

        for id in user_ids {
            match id.parse::<i64>() {
                Ok(num_id) => {
                    int_ids.push(num_id);
                    id_map.push(id.clone());
                }
                Err(_) => return Err(Error::storage(format!("无效的用户ID: {}", id))),
            }
        }

        // 批量查询学生能力值
        let students = student::Entity::find()
            .filter(
                student::Column::StudentId.is_in(int_ids.clone()),
            )
            .all(&self.db)
            .await
            .map_err(|e| Error::storage(format!("批量获取用户能力值失败: {}", e)))?;

        // 构建ID到能力值的映射
        let mut ability_map = std::collections::HashMap::new();
        for student in students {
            ability_map.insert(student.student_id, student.elo_rating as f64);
        }

        // 构建结果
        let mut result = Vec::new();
        for (i, id) in int_ids.iter().enumerate() {
            let ability = ability_map.get(id).copied();
            result.push((id_map[i].clone(), ability));
        }

        Ok(result)
    }

    /// 获取题目难度
    async fn get_question_difficulty(&self, question_id: &str) -> Result<Option<f64>> {
        // 转发到 question 实体进行查询
        match question_id.parse::<i32>() {
            Ok(question_id) => {
                let question =
                    question::Entity::find_by_id(question_id)
                        .one(&self.db)
                        .await
                        .map_err(|e| Error::storage(format!("获取题目难度失败: {}", e)))?;

                Ok(question.map(|q| q.elo_rating as f64))
            }
            Err(_) => Err(Error::storage(format!("无效的题目ID: {}", question_id))),
        }
    }

    /// 设置题目难度
    async fn set_question_difficulty(&self, question_id: &str, difficulty: f64) -> Result<()> {
        // 转发到 question 实体进行更新
        match question_id.parse::<i32>() {
            Ok(question_id) => {
                let question =
                    question::Entity::find_by_id(question_id)
                        .one(&self.db)
                        .await
                        .map_err(|e| Error::storage(format!("查询题目失败: {}", e)))?;

                if let Some(question) = question {
                    // 更新现有记录
                    let mut question_model: question::ActiveModel =
                        question.into();
                    question_model.elo_rating = Set(difficulty as f64);
                    question_model.updated_at = Set(Utc::now().into());

                    question_model
                        .update(&self.db)
                        .await
                        .map_err(|e| Error::storage(format!("更新题目难度失败: {}", e)))?;
                } else {
                    return Err(Error::storage(format!("题目不存在: {}", question_id)));
                }

                Ok(())
            }
            Err(_) => Err(Error::storage(format!("无效的题目ID: {}", question_id))),
        }
    }

    /// 批量获取题目难度
    async fn get_question_difficulties(
        &self,
        question_ids: &[String],
    ) -> Result<Vec<(String, Option<f64>)>> {
        // 转发到 question 实体进行批量查询
        let mut int_ids = Vec::new();
        let mut id_map = Vec::new();

        for id in question_ids {
            match id.parse::<i32>() {
                Ok(num_id) => {
                    int_ids.push(num_id);
                    id_map.push(id.clone());
                }
                Err(_) => return Err(Error::storage(format!("无效的题目ID: {}", id))),
            }
        }

        // 批量查询题目难度
        let questions = question::Entity::find()
            .filter(
                question::Column::QuestionId.is_in(int_ids.clone()),
            )
            .all(&self.db)
            .await
            .map_err(|e| Error::storage(format!("批量获取题目难度失败: {}", e)))?;

        // 构建ID到难度的映射
        let mut difficulty_map = std::collections::HashMap::new();
        for question in questions {
            difficulty_map.insert(question.question_id, question.elo_rating as f64);
        }

        // 构建结果
        let mut result = Vec::new();
        for (i, id) in int_ids.iter().enumerate() {
            let difficulty = difficulty_map.get(id).copied();
            result.push((id_map[i].clone(), difficulty));
        }

        Ok(result)
    }

    /// 获取用户在特定知识点上的完整能力数据
    async fn get_knowledge_ability(
        &self,
        user_id: &str,
        knowledge_id: &str,
    ) -> Result<Option<Model>> {
        // 将字符串ID转换为整数
        let student_id = match user_id.parse::<i64>() {
            Ok(id) => id,
            Err(_) => return Err(Error::storage(format!("无效的用户ID: {}", user_id))),
        };

        let knowledge_id_int = match knowledge_id.parse::<i32>() {
            Ok(id) => id,
            Err(_) => return Err(Error::storage(format!("无效的知识点ID: {}", knowledge_id))),
        };

        // 使用Entity实体提供的辅助方法查询
        let ability = Entity::find_by_student_and_knowledge(student_id, knowledge_id_int)
            .one(&self.db)
            .await
            .map_err(|e| Error::storage(format!("查询知识点能力数据失败: {}", e)))?;

        Ok(ability)
    }

    /// 获取用户在各知识点上的能力值 (空实现)
    async fn get_knowledge_abilities(
        &self,
        _user_id: &str,
    ) -> Result<std::collections::HashMap<String, f64>> {
        // 空实现: 总是返回空的HashMap
        Ok(std::collections::HashMap::new())
    }

    /// 设置用户在某个知识点上的能力值 (空实现)
    async fn set_knowledge_ability(
        &self,
        _user_id: &str,
        _knowledge_id: &str,
        _ability: f64,
    ) -> Result<()> {
        // 空实现: 不执行任何操作
        Ok(())
    }

    /// 获取用户ELO分 (空实现)
    async fn get_user_elo(&self, _user_id: &str) -> Result<Option<f64>> {
        // 空实现: 总是返回None
        Ok(None)
    }

    /// 设置用户ELO分 (空实现)
    async fn set_user_elo(&self, _user_id: &str, _rating: f64) -> Result<()> {
        // 空实现: 不执行任何操作
        Ok(())
    }

    /// 获取题目ELO分
    async fn get_question_elo(&self, question_id: &str) -> Result<Option<f64>> {
        // 转发到 question 实体进行查询
        match question_id.parse::<i32>() {
            Ok(question_id) => {
                let question =
                    question::Entity::find_by_id(question_id)
                        .one(&self.db)
                        .await
                        .map_err(|e| Error::storage(format!("查询题目失败: {}", e)))?;

                Ok(question.map(|q| q.elo_rating as f64))
            }
            Err(_) => Err(Error::storage(format!("无效的题目ID: {}", question_id))),
        }
    }

    /// 设置题目ELO分
    async fn set_question_elo(&self, question_id: &str, rating: f64) -> Result<()> {
        // 转发到 question 实体进行更新
        match question_id.parse::<i32>() {
            Ok(question_id) => {
                let question =
                    question::Entity::find_by_id(question_id)
                        .one(&self.db)
                        .await
                        .map_err(|e| Error::storage(format!("查询题目失败: {}", e)))?;

                if let Some(question) = question {
                    // 更新现有记录
                    let mut question_model: question::ActiveModel =
                        question.into();
                    question_model.elo_rating = Set(rating as f64);
                    question_model.updated_at = Set(Utc::now().into());

                    question_model
                        .update(&self.db)
                        .await
                        .map_err(|e| Error::storage(format!("更新题目ELO评分失败: {}", e)))?;
                } else {
                    return Err(Error::storage(format!("题目不存在: {}", question_id)));
                }

                Ok(())
            }
            Err(_) => Err(Error::storage(format!("无效的题目ID: {}", question_id))),
        }
    }
}

#[async_trait]
impl KnowledgeEloStorage for SeaOrmKnowledgeEloStorage {
    /// 获取知识点Elo评分
    async fn get_knowledge_elo(&self, student_id: i64, knowledge_id: i32) -> Result<Option<f64>> {
        // 查询该学生在指定知识点的能力记录
        let ability = Entity::find_by_student_and_knowledge(student_id, knowledge_id)
            .one(&self.db)
            .await
            .map_err(|e| Error::storage(format!("查询知识点能力失败: {}", e)))?;

        // 返回Elo评分（如果存在）
        Ok(ability.map(|a| a.elo_rating as f64))
    }

    /// 设置知识点Elo评分
    async fn set_knowledge_elo(
        &self,
        student_id: i64,
        knowledge_id: i32,
        rating: f64,
        k_value: f64,
    ) -> Result<()> {
        // 查询该学生在指定知识点的能力记录
        let existing = Entity::find_by_student_and_knowledge(student_id, knowledge_id)
            .one(&self.db)
            .await
            .map_err(|e| Error::storage(format!("查询知识点能力失败: {}", e)))?;

        if let Some(record) = existing {
            // 更新现有记录
            let mut active_model: ActiveModel = record.into();
            active_model.elo_rating = Set(rating as f64);
            active_model.last_updated = Set(Utc::now());

            active_model
                .update(&self.db)
                .await
                .map_err(|e| Error::storage(format!("更新知识点Elo评分失败: {}", e)))?;
        } else {
            // 创建新记录
            let active_model = ActiveModel {
                ability_id: Default::default(), // 自动生成ID
                student_id: Set(student_id),
                knowledge_id: Set(knowledge_id),
                elo_rating: Set(rating as f64),
                irt_ability: Set(None),
                question_count: Set(0),
                correct_count: Set(0),
                last_updated: Set(Utc::now()),
                created_at: Set(Utc::now()),
                k_value: Set(Some(k_value)),
            };

            active_model
                .insert(&self.db)
                .await
                .map_err(|e| Error::storage(format!("插入知识点Elo评分失败: {}", e)))?;
        }

        Ok(())
    }

    /// 获取题目在特定知识点的Elo评分
    async fn get_question_knowledge_elo(
        &self,
        question_id: i32,
        _knowledge_id: i32,
    ) -> Result<Option<f64>> {
        // 由于没有 question_knowledge_difficulty 实体，这里直接从问题表获取通用难度
        let question = question::Entity::find_by_id(question_id)
            .one(&self.db)
            .await
            .map_err(|e| Error::storage(format!("查询题目知识点难度失败: {}", e)))?;

        // 返回难度评分（如果存在）
        Ok(question.map(|q| q.elo_rating as f64))
    }

    /// 设置题目在特定知识点的Elo评分
    async fn set_question_knowledge_elo(
        &self,
        question_id: i32,
        _knowledge_id: i32,
        rating: f64,
    ) -> Result<()> {
        // 简化实现：直接更新题目的通用难度
        let question = question::Entity::find_by_id(question_id)
            .one(&self.db)
            .await
            .map_err(|e| Error::storage(format!("查询题目失败: {}", e)))?;

        if let Some(question) = question {
            // 更新现有题目
            let mut question_model: question::ActiveModel =
                question.into();
            question_model.elo_rating = Set(rating as f64);
            question_model.updated_at = Set(Utc::now().into());

            question_model
                .update(&self.db)
                .await
                .map_err(|e| Error::storage(format!("更新题目Elo评分失败: {}", e)))?;
        } else {
            return Err(Error::storage(format!("题目不存在: {}", question_id)));
        }

        Ok(())
    }

    /// 获取学生在指定知识点的答题数量
    async fn get_knowledge_question_count(
        &self,
        student_id: i64,
        knowledge_id: i32,
    ) -> Result<u32> {
        // 查询该学生在指定知识点的能力记录
        let ability = Entity::find_by_student_and_knowledge(student_id, knowledge_id)
            .one(&self.db)
            .await
            .map_err(|e| Error::storage(format!("查询知识点能力失败: {}", e)))?;

        // 返回题目数量（如果存在）
        Ok(ability.map(|a| a.question_count as u32).unwrap_or(0))
    }

    /// 增加学生在指定知识点的答题数量
    async fn increment_knowledge_question_count(
        &self,
        student_id: i64,
        knowledge_id: i32,
    ) -> Result<()> {
        // 查询该学生在指定知识点的能力记录
        let existing = Entity::find_by_student_and_knowledge(student_id, knowledge_id)
            .one(&self.db)
            .await
            .map_err(|e| Error::storage(format!("查询知识点能力失败: {}", e)))?;

        if let Some(record) = existing {
            // 更新现有记录
            let mut active_model: ActiveModel = record.into();
            active_model.question_count = Set(active_model.question_count.unwrap() + 1);
            active_model.last_updated = Set(Utc::now());

            active_model
                .update(&self.db)
                .await
                .map_err(|e| Error::storage(format!("更新知识点答题数量失败: {}", e)))?;
        } else {
            // 创建新记录

            let default_k = self.elo_config.k_factor;
            let active_model = ActiveModel {
                ability_id: Default::default(), // 自动生成ID
                student_id: Set(student_id),
                knowledge_id: Set(knowledge_id),
                elo_rating: Set(1700.0), // 默认Elo评分
                irt_ability: Set(None),
                question_count: Set(1),
                correct_count: Set(0),
                last_updated: Set(Utc::now()),
                created_at: Set(Utc::now()),
                k_value: Set(Some(default_k)),
            };

            active_model
                .insert(&self.db)
                .await
                .map_err(|e| Error::storage(format!("插入知识点能力记录失败: {}", e)))?;
        }

        Ok(())
    }

    /// 增加学生在指定知识点的正确答题数量
    async fn increment_knowledge_correct_count(
        &self,
        student_id: i64,
        knowledge_id: i32,
    ) -> Result<()> {
        // 查询该学生在指定知识点的能力记录
        let existing = Entity::find_by_student_and_knowledge(student_id, knowledge_id)
            .one(&self.db)
            .await
            .map_err(|e| Error::storage(format!("查询知识点能力失败: {}", e)))?;

        if let Some(record) = existing {
            // 更新现有记录
            let mut active_model: ActiveModel = record.into();
            active_model.correct_count = Set(active_model.correct_count.unwrap() + 1);
            active_model.last_updated = Set(Utc::now());

            active_model
                .update(&self.db)
                .await
                .map_err(|e| Error::storage(format!("更新知识点正确答题数量失败: {}", e)))?;
        } else {
            // 创建新记录
            let default_k = self.elo_config.k_factor;
            let active_model = ActiveModel {
                ability_id: Default::default(), // 自动生成ID
                student_id: Set(student_id),
                knowledge_id: Set(knowledge_id),
                elo_rating: Set(1700.0), // 默认Elo评分
                irt_ability: Set(None),
                question_count: Set(1),
                correct_count: Set(1),
                last_updated: Set(Utc::now()),
                created_at: Set(Utc::now()),
                k_value: Set(Some(default_k)),
            };

            active_model
                .insert(&self.db)
                .await
                .map_err(|e| Error::storage(format!("插入知识点能力记录失败: {}", e)))?;
        }

        Ok(())
    }

    /// 获取知识点的所有题目ELO信息
    async fn get_knowledge_questions(
        &self,
        knowledge_id: i32,
    ) -> Result<Vec<crate::infrastructure::dto::question::QuestionEloInfo>> {
        // 直接查询指定知识点的题目
        let questions =
            question::Entity::find_by_knowledge_id(knowledge_id)
                .all(&self.db)
                .await
                .map_err(|e| Error::storage(format!("查询知识点题目失败: {}", e)))?;

        // 转换为语义清晰的DTO
        let question_elo_infos = questions
            .iter()
            .map(|q| crate::infrastructure::dto::question::QuestionEloInfo {
                question_id: q.question_id.to_string(),
                knowledge_id: q.knowledge_id,
                elo_rating: q.elo_rating as f64,
            })
            .collect();

        Ok(question_elo_infos)
    }

    /// 检查学生是否已做过某题
    async fn has_student_answered(&self, student_id: i64, question_id: i32) -> Result<bool> {
        // 直接使用 sea-orm 的 count 方法
        let count = student_answer::Entity::find()
            .filter(student_answer::Column::StudentId.eq(student_id))
            .filter(
                student_answer::Column::QuestionId.eq(question_id),
            )
            .count(&self.db)
            .await
            .map_err(|e| Error::storage(format!("查询学生答题记录失败: {}", e)))?;

        Ok(count > 0)
    }
}


