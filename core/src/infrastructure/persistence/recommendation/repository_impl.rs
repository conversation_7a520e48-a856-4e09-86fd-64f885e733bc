//! 推荐快照仓储实现
//!
//! 实现推荐系统数据一致性快照相关的数据访问逻辑
//! 专门用于支持推荐引擎的快照功能，确保推荐过程中数据的一致性

use async_trait::async_trait;
use std::sync::Arc;
use sea_orm::{DatabaseConnection, EntityTrait, QueryFilter, ColumnTrait, QuerySelect};

use crate::Result;
use crate::domain::answer::repository::StudentAnswerRepository;
use crate::domain::recommendation::services::recommendation_engine::SnapshotRecommendationRepository;
use crate::storage::entities::student_answer::Entity as StudentAnswerEntity;
use crate::storage::entities::student_answer::Column as StudentAnswerColumn;

/// 推荐快照仓储实现
///
/// 专门用于支持推荐引擎数据一致性快照功能的仓储实现
/// 实现SnapshotRecommendationRepository接口，提供版本化的数据访问
pub struct RecommendationRepositoryImpl {
    answer_repo: Arc<dyn StudentAnswerRepository + Send + Sync>,
    db: Arc<DatabaseConnection>,
}

impl RecommendationRepositoryImpl {
    /// 创建新实例
    pub fn new(
        answer_repo: Arc<dyn StudentAnswerRepository + Send + Sync>,
        db: Arc<DatabaseConnection>,
    ) -> Self {
        Self {
            answer_repo,
            db,
        }
    }
}

// 删除了未使用的RecommendationRepository实现
// 该实现返回空结果且未被实际使用

/// 实现快照推荐仓储接口（用于数据一致性）
#[async_trait]
impl SnapshotRecommendationRepository for RecommendationRepositoryImpl {
    /// 获取用户已答题目ID列表
    async fn get_answered_question_ids(&self, student_id: &str) -> Result<Vec<i32>> {
        let student_id_int = student_id.parse::<i64>().unwrap_or(0);
        self.answer_repo
            .get_answered_question_ids(student_id_int)
            .await
    }

    /// 获取用户已答题目ID列表（带版本号，用于检测数据变更）
    async fn get_answered_question_ids_with_version(&self, student_id: &str) -> Result<(Vec<i32>, u64)> {
        let student_id_i64 = student_id
            .parse::<i64>()
            .map_err(|_| crate::Error::service("Invalid student_id format"))?;

        // 查询学生的所有答题记录，包括时间戳
        let answered_records = StudentAnswerEntity::find()
            .filter(StudentAnswerColumn::StudentId.eq(student_id_i64))
            .select_only()
            .columns([StudentAnswerColumn::QuestionId, StudentAnswerColumn::CreatedAt])
            .into_tuple::<(i32, chrono::DateTime<chrono::Utc>)>()
            .all(&*self.db)
            .await
            .map_err(|e| crate::Error::storage(format!("Failed to get answered question IDs with version: {}", e)))?;

        // 提取题目ID列表
        let question_ids: Vec<i32> = answered_records.iter().map(|(id, _)| *id).collect();

        // 计算版本号：使用最新答题时间的时间戳
        let version = if let Some(latest_time) = answered_records.iter().map(|(_, time)| time).max() {
            latest_time.timestamp() as u64
        } else {
            // 如果没有答题记录，使用0作为版本号
            0
        };

        Ok((question_ids, version))
    }
}
