//! SAT 组卷会话仓储实现
//!
//! 使用 SeaORM 实现的会话仓储

use std::sync::Arc;
use async_trait::async_trait;
use sea_orm::{ActiveModelTrait, ColumnTrait, EntityTrait, QueryFilter, QueryOrder, Set, PaginatorTrait};
use chrono::Utc;

use crate::domain::question::{QuestionBankSession, QuestionBankSessionRepository};
use crate::domain::question::value_objects::{GenerateType, Subject};
use crate::infrastructure::database::models::question_bank_session::{self, Entity as QuestionBankSessionEntity};
use crate::infrastructure::persistence::StorageManager;
use crate::error::{Error, Result};

/// SAT 组卷会话仓储实现
pub struct QuestionBankSessionRepositoryImpl {
    storage_manager: Arc<StorageManager>,
}

impl QuestionBankSessionRepositoryImpl {
    pub fn new(storage_manager: Arc<StorageManager>) -> Self {
        Self { storage_manager }
    }

    /// 反序列化题目ID列表，支持字符串和整数两种格式
    fn deserialize_question_ids(&self, json_value: serde_json::Value) -> Result<Vec<i32>> {
        // 首先尝试直接反序列化为 Vec<i32>
        if let Ok(ids) = serde_json::from_value::<Vec<i32>>(json_value.clone()) {
            return Ok(ids);
        }

        // 如果失败，尝试反序列化为 Vec<String> 然后转换为 Vec<i32>
        if let Ok(string_ids) = serde_json::from_value::<Vec<String>>(json_value.clone()) {
            let mut ids = Vec::new();
            for string_id in string_ids {
                match string_id.parse::<i32>() {
                    Ok(id) => ids.push(id),
                    Err(_) => {
                        return Err(Error::service(format!(
                            "无法将题目ID字符串 '{}' 转换为整数",
                            string_id
                        )));
                    }
                }
            }
            return Ok(ids);
        }

        // 如果都失败了，返回错误
        Err(Error::service(format!(
            "反序列化题目ID列表失败: 期望数组格式，实际值: {}",
            json_value
        )))
    }

    /// 将领域实体转换为数据库模型
    fn domain_to_model(&self, session: &QuestionBankSession) -> Result<question_bank_session::ActiveModel> {
        // 序列化题目ID列表
        let question_ids_json = serde_json::to_value(session.question_ids())
            .map_err(|e| Error::service(format!("序列化题目ID列表失败: {}", e)))?;

        // 序列化小节ID列表
        let section_ids_json = serde_json::to_value(session.section_ids())
            .map_err(|e| Error::service(format!("序列化小节ID列表失败: {}", e)))?;

        // 转换生成类型
        let generate_type_str = match session.generate_type() {
            GenerateType::Random => "random",
            GenerateType::Adaptive => "adaptive",
        };

        // 转换学科
        let subject_str = match session.subject() {
            Subject::Math => "math",
            Subject::Reading => "reading",
        };

        // 转换时间格式
        let created_at = session.created_at().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap());
        let expires_at = session.expires_at().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap());
        let updated_at = Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap());

        Ok(question_bank_session::ActiveModel {
            session_id: Set(session.session_id().to_string()),
            user_id: Set(session.user_id()),
            question_ids: Set(question_ids_json),
            generate_type: Set(generate_type_str.to_string()),
            subject: Set(subject_str.to_string()),
            section_ids: Set(section_ids_json),
            total_count: Set(session.total_count()),
            created_at: Set(created_at),
            updated_at: Set(updated_at),
            expires_at: Set(expires_at),
        })
    }

    /// 将数据库模型转换为领域实体
    fn model_to_domain(&self, model: question_bank_session::Model) -> Result<QuestionBankSession> {
        // 反序列化题目ID列表 - 支持字符串和整数两种格式
        let question_ids: Vec<i32> = self.deserialize_question_ids(model.question_ids)?;

        // 反序列化小节ID列表
        let section_ids: Vec<i32> = serde_json::from_value(model.section_ids)
            .map_err(|e| Error::service(format!("反序列化小节ID列表失败: {}", e)))?;

        // 转换生成类型
        let generate_type = match model.generate_type.as_str() {
            "random" => GenerateType::Random,
            "adaptive" => GenerateType::Adaptive,
            _ => GenerateType::Random, // 默认值
        };

        // 转换学科
        let subject = match model.subject.as_str() {
            "math" => Subject::Math,
            "reading" => Subject::Reading,
            _ => Subject::Math, // 默认值
        };

        // 转换时间格式
        let created_at = model.created_at.with_timezone(&Utc);
        let expires_at = model.expires_at.with_timezone(&Utc);

        Ok(QuestionBankSession::from_existing(
            model.session_id,
            model.user_id,
            question_ids,
            generate_type,
            subject,
            section_ids,
            model.total_count,
            created_at,
            expires_at,
        ))
    }
}

#[async_trait]
impl QuestionBankSessionRepository for QuestionBankSessionRepositoryImpl {
    async fn save(&self, session: &QuestionBankSession) -> Result<()> {
        let db = self.storage_manager.sea_orm_db();
        let active_model = self.domain_to_model(session)?;

        active_model.insert(db.as_ref()).await
            .map_err(|e| Error::database(format!("保存会话失败: {}", e)))?;

        Ok(())
    }

    async fn find_by_id(&self, session_id: &str) -> Result<Option<QuestionBankSession>> {
        let db = self.storage_manager.sea_orm_db();

        let model = QuestionBankSessionEntity::find()
            .filter(question_bank_session::Column::SessionId.eq(session_id))
            .one(db.as_ref())
            .await
            .map_err(|e| Error::database(format!("查询会话失败: {}", e)))?;

        match model {
            Some(m) => Ok(Some(self.model_to_domain(m)?)),
            None => Ok(None),
        }
    }

    async fn find_latest_by_user_and_subject(&self, user_id: i64, subject: &str) -> Result<Option<QuestionBankSession>> {
        let db = self.storage_manager.sea_orm_db();

        let model = QuestionBankSessionEntity::find()
            .filter(question_bank_session::Column::UserId.eq(user_id))
            .filter(question_bank_session::Column::Subject.eq(subject))
            .order_by_desc(question_bank_session::Column::CreatedAt)
            .one(db.as_ref())
            .await
            .map_err(|e| Error::database(format!("查询用户最新会话失败: {}", e)))?;

        match model {
            Some(m) => Ok(Some(self.model_to_domain(m)?)),
            None => Ok(None),
        }
    }

    async fn find_latest_by_user_all_subjects(&self, user_id: i64) -> Result<Vec<QuestionBankSession>> {
        let db = self.storage_manager.sea_orm_db();

        // 查询用户在每个科目的最新session
        // 使用窗口函数按科目分组并按创建时间排序，取每组的第一条记录
        let models = QuestionBankSessionEntity::find()
            .filter(question_bank_session::Column::UserId.eq(user_id))
            .order_by_desc(question_bank_session::Column::CreatedAt)
            .all(db.as_ref())
            .await
            .map_err(|e| Error::database(format!("查询用户所有科目最新会话失败: {}", e)))?;

        // 手动按科目分组并取每个科目的最新记录
        let mut subject_sessions: std::collections::HashMap<String, question_bank_session::Model> = std::collections::HashMap::new();

        for model in models {
            let subject = model.subject.clone();
            if !subject_sessions.contains_key(&subject) {
                subject_sessions.insert(subject, model);
            }
        }

        // 转换为领域对象
        let mut sessions = Vec::new();
        for (_, model) in subject_sessions {
            sessions.push(self.model_to_domain(model)?);
        }

        Ok(sessions)
    }

    async fn cleanup_expired(&self) -> Result<u64> {
        let db = self.storage_manager.sea_orm_db();

        let result = QuestionBankSessionEntity::delete_many()
            .filter(question_bank_session::Column::ExpiresAt.lt(Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap())))
            .exec(db.as_ref())
            .await
            .map_err(|e| Error::database(format!("清理过期会话失败: {}", e)))?;

        Ok(result.rows_affected)
    }

    async fn exists(&self, session_id: &str) -> Result<bool> {
        let db = self.storage_manager.sea_orm_db();

        let count = QuestionBankSessionEntity::find()
            .filter(question_bank_session::Column::SessionId.eq(session_id))
            .filter(question_bank_session::Column::ExpiresAt.gt(Utc::now().with_timezone(&chrono::FixedOffset::east_opt(0).unwrap())))
            .count(db.as_ref())
            .await
            .map_err(|e| Error::database(format!("检查会话存在性失败: {}", e)))?;

        Ok(count > 0)
    }
}
