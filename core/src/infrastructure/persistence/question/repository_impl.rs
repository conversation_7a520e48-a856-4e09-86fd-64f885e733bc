//! 问题仓储实现
//!
//! 实现问题仓储接口，提供数据库访问功能

use async_trait::async_trait;
use sea_orm::{
    ColumnTrait, DatabaseConnection, EntityTrait, PaginatorTrait, QueryFilter, QuerySelect,
};
use std::collections::HashMap;
use std::sync::Arc;
use tracing::{error, warn};

use crate::domain::question::{Question, QuestionMetadata, QuestionRepository, QuestionConverter};
use crate::error::{Error, Result};
use crate::storage::entities::{self, question};

/// 问题仓储实现
pub struct QuestionRepositoryImpl {
    db: Arc<DatabaseConnection>,
}

impl QuestionRepositoryImpl {
    /// 创建新的问题仓储实例
    pub fn new(db: Arc<DatabaseConnection>) -> Self {
        Self {
            db,
        }
    }
}

#[async_trait]
impl QuestionRepository for QuestionRepositoryImpl {
    async fn find_by_id(&self, id: &str) -> Result<Option<Question>> {
        // 解析ID
        let question_id = match id.parse::<i32>() {
            Ok(id) => id,
            Err(_) => {
                warn!("无效的题目ID格式: {}", id);
                return Ok(None);
            }
        };

        // 查询数据库
        match entities::question::Entity::find_by_id(question_id)
            .one(&*self.db)
            .await
        {
            Ok(model) => Ok(model.map(|m| QuestionConverter::from_db_model(&m))),
            Err(e) => {
                error!("查询题目失败: {}", e);
                Err(Error::storage(format!("查询题目失败: {}", e)))
            }
        }
    }

    async fn find_by_ids(&self, ids: &[String]) -> Result<Vec<Question>> {
        if ids.is_empty() {
            return Ok(Vec::new());
        }

        // 解析ID
        let mut valid_ids = Vec::new();
        for id in ids {
            match id.parse::<i32>() {
                Ok(parsed_id) => valid_ids.push(parsed_id),
                Err(_) => {
                    warn!("跳过无效的题目ID: {}", id);
                }
            }
        }

        if valid_ids.is_empty() {
            return Ok(Vec::new());
        }

        // 查询数据库
        match entities::question::Entity::find()
            .filter(question::Column::QuestionId.is_in(valid_ids))
            .all(&*self.db)
            .await
        {
            Ok(models) => {
                let questions = QuestionConverter::batch_from_db_models(&models);
                Ok(questions)
            }
            Err(e) => {
                error!("批量查询题目失败: {}", e);

                // 降级策略：如果批量查询失败，尝试单个查询
                if ids.len() <= 3 {
                    // 只对少量ID尝试单个查询
                    warn!("批量查询失败，降级为单个查询");
                    let mut results = Vec::new();

                    for id in ids {
                        match self.find_by_id(id).await {
                            Ok(Some(question)) => results.push(question),
                            Ok(None) => {}
                            Err(e) => {
                                warn!("单个查询题目 {} 也失败: {}", id, e);
                            }
                        }
                    }

                    Ok(results)
                } else {
                    Err(Error::storage(format!("批量查询题目失败: {}", e)))
                }
            }
        }
    }

    async fn find_by_knowledge(&self, knowledge_id: i32) -> Result<Vec<Question>> {
        match entities::question::Entity::find()
            .filter(question::Column::KnowledgeId.eq(knowledge_id))
            .all(&*self.db)
            .await
        {
            Ok(models) => {
                let questions = QuestionConverter::batch_from_db_models(&models);
                Ok(questions)
            }
            Err(e) => {
                error!("根据知识点查询题目失败: {}", e);
                Err(Error::storage(format!("根据知识点查询题目失败: {}", e)))
            }
        }
    }

    async fn is_in_chapter(&self, question_id: &str, chapter_id: i32) -> Result<bool> {
        // 解析ID
        let q_id = match question_id.parse::<i32>() {
            Ok(id) => id,
            Err(_) => {
                warn!("无效的题目ID格式: {}", question_id);
                return Ok(false);
            }
        };

        // 查询数据库
        // 通过两步查询实现：
        // 1. 先获取题目的知识点ID
        // 2. 然后查询该知识点是否属于指定章节

        // 获取题目的知识点ID
        let question = match entities::question::Entity::find_by_id(q_id)
            .one(&*self.db)
            .await
        {
            Ok(Some(q)) => q,
            Ok(None) => {
                warn!("题目不存在: {}", question_id);
                return Ok(false);
            }
            Err(e) => {
                error!("查询题目失败: {}", e);
                return Err(Error::storage(format!("查询题目失败: {}", e)));
            }
        };

        // 获取知识点所属的小节
        let knowledge = match entities::knowledge::Entity::find_by_id(question.knowledge_id)
            .one(&*self.db)
            .await
        {
            Ok(Some(k)) => k,
            Ok(None) => {
                warn!("知识点不存在: {}", question.knowledge_id);
                return Ok(false);
            }
            Err(e) => {
                error!("查询知识点失败: {}", e);
                return Err(Error::storage(format!("查询知识点失败: {}", e)));
            }
        };

        // 获取小节所属的章节
        let section = match entities::section::Entity::find_by_id(knowledge.section_id)
            .one(&*self.db)
            .await
        {
            Ok(Some(s)) => s,
            Ok(None) => {
                warn!("小节不存在: {}", knowledge.section_id);
                return Ok(false);
            }
            Err(e) => {
                error!("查询小节失败: {}", e);
                return Err(Error::storage(format!("查询小节失败: {}", e)));
            }
        };

        // 检查小节是否属于指定章节
        Ok(section.chapter_id == chapter_id)
    }

    async fn is_in_section(&self, question_id: &str, section_id: i32) -> Result<bool> {
        // 解析ID
        let q_id = match question_id.parse::<i32>() {
            Ok(id) => id,
            Err(_) => {
                warn!("无效的题目ID格式: {}", question_id);
                return Ok(false);
            }
        };

        // 查询数据库
        // 直接使用题目的section_id字段
        let count = entities::question::Entity::find_by_id(q_id)
            .filter(question::Column::SectionId.eq(section_id))
            .count(&*self.db)
            .await;

        match count {
            Ok(count) => Ok(count > 0),
            Err(e) => {
                error!("检查题目小节关系失败: {}", e);
                Err(Error::storage(format!("检查题目小节关系失败: {}", e)))
            }
        }
    }

    async fn get_metadata(&self, ids: &[String]) -> Result<HashMap<String, QuestionMetadata>> {
        if ids.is_empty() {
            return Ok(HashMap::new());
        }

        // 解析ID
        let mut valid_ids = Vec::new();
        for id in ids {
            match id.parse::<i32>() {
                Ok(parsed_id) => valid_ids.push(parsed_id),
                Err(_) => {
                    warn!("跳过无效的题目ID: {}", id);
                }
            }
        }

        if valid_ids.is_empty() {
            return Ok(HashMap::new());
        }

        // 查询数据库
        match entities::question::Entity::find()
            .filter(question::Column::QuestionId.is_in(valid_ids))
            .select_only()
            .column(question::Column::QuestionId)
            .column(question::Column::SubjectId)
            .column(question::Column::KnowledgeId)
            .column(question::Column::TypeId)
            .column(question::Column::Difficulty)
            .column(question::Column::EloRating)
            .all(&*self.db)
            .await
        {
            Ok(models) => {
                let mut metadata_map = HashMap::new();

                for model in models {
                    let metadata = QuestionMetadata {
                        id: model.question_id.to_string(),
                        subject_id: model.subject_id,
                        knowledge_id: model.knowledge_id,
                        knowledge_name: None, // 可能需要额外查询
                        type_id: model.type_id,
                        difficulty: model.difficulty as f64,
                        elo_rating: Some(model.elo_rating),
                        correct_ratio: None, // 可能需要额外查询
                    };

                    metadata_map.insert(model.question_id.to_string(), metadata);
                }

                Ok(metadata_map)
            }
            Err(e) => {
                error!("批量查询题目元数据失败: {}", e);
                Err(Error::storage(format!("批量查询题目元数据失败: {}", e)))
            }
        }
    }

    async fn find_by_knowledge_and_difficulty(
        &self,
        knowledge_id: i32,
        min_difficulty: f64,
        max_difficulty: f64,
        status: Option<i32>,
    ) -> Result<Vec<Question>> {
        let mut query = entities::question::Entity::find()
            .filter(question::Column::KnowledgeId.eq(knowledge_id))
            .filter(question::Column::EloRating.between(min_difficulty, max_difficulty))
            .filter(question::Column::IsActive.eq(true));

        // 添加状态过滤（与重构前保持一致）
        if let Some(status_value) = status {
            query = query.filter(question::Column::Status.eq(status_value));
        }

        match query.all(&*self.db).await {
            Ok(models) => {
                let questions = QuestionConverter::batch_from_db_models(&models);
                Ok(questions)
            }
            Err(e) => {
                error!("根据知识点和ELO评分查询题目失败: {}", e);
                Err(Error::storage(format!("根据知识点和ELO评分查询题目失败: {}", e)))
            }
        }
    }

    async fn find_by_knowledge_ids(&self, knowledge_ids: &[i32]) -> Result<Vec<Question>> {
        if knowledge_ids.is_empty() {
            return Ok(Vec::new());
        }

        match entities::question::Entity::find()
            .filter(question::Column::KnowledgeId.is_in(knowledge_ids.to_vec()))
            .filter(question::Column::IsActive.eq(true))
            .all(&*self.db)
            .await
        {
            Ok(models) => {
                let questions = QuestionConverter::batch_from_db_models(&models);
                Ok(questions)
            }
            Err(e) => {
                error!("根据知识点ID列表查询题目失败: {}", e);
                Err(Error::storage(format!("根据知识点ID列表查询题目失败: {}", e)))
            }
        }
    }

    async fn find_by_subject(&self, subject_id: i32) -> Result<Vec<Question>> {
        match entities::question::Entity::find()
            .filter(question::Column::SubjectId.eq(subject_id))
            .filter(question::Column::IsActive.eq(true))
            .all(&*self.db)
            .await
        {
            Ok(models) => {
                let questions = QuestionConverter::batch_from_db_models(&models);
                Ok(questions)
            }
            Err(e) => {
                error!("根据学科ID查询题目失败: {}", e);
                Err(Error::storage(format!("根据学科ID查询题目失败: {}", e)))
            }
        }
    }

    async fn find_by_sections(&self, section_ids: &[i32]) -> Result<Vec<Question>> {
        if section_ids.is_empty() {
            return Ok(Vec::new());
        }

        match entities::question::Entity::find()
            .filter(question::Column::SectionId.is_in(section_ids.iter().cloned()))
            .filter(question::Column::IsActive.eq(true))
            .all(&*self.db)
            .await
        {
            Ok(models) => {
                let questions = QuestionConverter::batch_from_db_models(&models);
                Ok(questions)
            }
            Err(e) => {
                error!("根据小节ID列表查询题目失败: {}", e);
                Err(Error::storage(format!("根据小节ID列表查询题目失败: {}", e)))
            }
        }
    }

    // 删除了不需要的题目增删改实现：
    // - async fn save(&self, question: &Question) -> Result<()>
    // - async fn delete(&self, id: &str) -> Result<bool>
}
