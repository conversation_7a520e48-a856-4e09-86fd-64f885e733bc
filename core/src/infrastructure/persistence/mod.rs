/// 持久化基础设施模块
///
/// 包含各种存储实现和仓储工厂方法
pub mod ability;
pub mod answer;
pub mod batch_ability;
pub mod conversation;
pub mod database;
pub mod flashcard;
pub mod fsrs;
pub mod knowledge;
pub mod storage_impl;  // 从storage/implementations迁移的存储实现
pub mod plan;
pub mod question;
pub mod recommendation;
pub mod user_segmentation;
pub mod exam;

// 重新导出模块内的具体仓储实现，便于使用
// 闪卡相关仓储
pub use flashcard::{
    CardRepository, DeckRepository, RecommendationHistoryRepository,
    UserCardProgressRepository, UserDeckAccessRepository,
};

// FSRS仓储
pub use fsrs::FSRSSubmissionRepositoryImpl;
// Re-export domain FSRSSubmissionRepository trait for convenience
pub use crate::domain::flashcard::FSRSSubmissionRepository;

// 对话相关仓储
pub use conversation::{ConversationRepository, MessageRepository, ContextRepository, LogRepository};

// 其他仓储
pub use ability::repository_impl::{AbilityRepositoryImpl, KnowledgeRepositoryImpl};
pub use answer::repository_impl::AnswerRepositoryImpl;
pub use question::repository_impl::QuestionRepositoryImpl;
pub use recommendation::repository_impl::*;

// 明确重导出具体类型，避免模糊的全局重导出
pub use answer::AnswerRepositoryImpl as AnswerRepoImpl;
pub use plan::PlanRepository;
pub use user_segmentation::*;

// 批次能力记录仓储
pub use batch_ability::BatchAbilityRecordRepositoryImpl;

// 知识仓储（新DDD架构）
pub use knowledge::KnowledgeRepositoryImpl as DomainKnowledgeRepositoryImpl;

// 数据库基础设施
pub use database::{StorageManager, PostgresStorage};

// 存储实现（从storage/implementations迁移）
pub use storage_impl::*;
