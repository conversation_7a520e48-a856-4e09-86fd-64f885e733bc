//! 题目实体模型
//! 存储SAT题目的核心内容和属性

use sea_orm::entity::prelude::*;

#[derive(<PERSON><PERSON>, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "t_sat_question")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub question_id: i32,
    pub subject_id: i32,
    pub knowledge_id: i32,
    pub type_id: i32,
    #[sea_orm(column_type = "SmallInteger")]
    pub difficulty: i16,
    pub question_content: Json,
    pub options: Option<Json>,
    pub answer: <PERSON><PERSON>,
    pub explanation: Option<Json>,
    pub elo_rating: f64,
    pub irt_difficulty: Option<f64>,
    pub irt_discrimination: Option<f64>,
    pub irt_guessing: Option<f64>,
    pub usage_count: i32,
    pub correct_count: i32,
    pub question_set: Option<String>,
    pub url: Option<String>,
    pub is_active: bool,
    pub created_at: DateTimeWithTimeZone,
    pub updated_at: DateTimeWithTimeZone,
    pub section_id: Option<i32>,
    #[sea_orm(column_type = "SmallInteger")]
    pub edit_status: i16,
    pub status: i32,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::subject::Entity",
        from = "Column::SubjectId",
        to = "super::subject::Column::SubjectId"
    )]
    Subject,
    #[sea_orm(
        belongs_to = "super::knowledge::Entity",
        from = "Column::KnowledgeId",
        to = "super::knowledge::Column::KnowledgeId"
    )]
    Knowledge,
    #[sea_orm(has_many = "super::sat_paper_question::Entity")]
    SatPaperQuestion,
}

impl Related<super::subject::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Subject.def()
    }
}

impl Related<super::knowledge::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Knowledge.def()
    }
}

impl Related<super::sat_paper_question::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SatPaperQuestion.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}

impl Entity {
    pub fn find_by_id(id: i32) -> Select<Entity> {
        Self::find().filter(Column::QuestionId.eq(id))
    }

    pub fn find_by_knowledge_id(knowledge_id: i32) -> Select<Entity> {
        Self::find().filter(Column::KnowledgeId.eq(knowledge_id))
    }

    pub fn find_active_by_knowledge_id(knowledge_id: i32) -> Select<Entity> {
        Self::find()
            .filter(Column::KnowledgeId.eq(knowledge_id))
            .filter(Column::IsActive.eq(true))
            .filter(Column::Status.eq(0))
    }

    pub fn find_by_difficulty_range(min: i16, max: i16) -> Select<Entity> {
        Self::find()
            .filter(Column::Difficulty.gte(min))
            .filter(Column::Difficulty.lte(max))
            .filter(Column::IsActive.eq(true))
            .filter(Column::Status.eq(0))
    }

    pub fn find_by_section_id(section_id: i32) -> Select<Entity> {
        Self::find()
            .filter(Column::SectionId.eq(section_id))
            .filter(Column::IsActive.eq(true))
            .filter(Column::Status.eq(0))
    }

    pub fn find_by_section_id_and_status(section_id: i32, status: i32) -> Select<Entity> {
        Self::find()
            .filter(Column::SectionId.eq(section_id))
            .filter(Column::Status.eq(status))
            .filter(Column::IsActive.eq(true))
    }
}
