//! 数据库实体模型，每个文件对应一个数据库表
//!
//! 该模块使用sea-orm定义实体模型，包括表结构和关系
//! 每个文件只包含一个表的实体定义，避免冲突

// 用户数据
pub mod student;
pub mod user;

// 知识体系
pub mod chapter;
pub mod knowledge;
pub mod knowledge_mastery;
pub mod knowledge_type;
pub mod section;
pub mod subject;

// 题目相关
pub mod question;
pub mod question_bank_session;
pub mod student_answer;

// 推荐系统
pub mod recommendation;
pub mod student_knowledge_progress;

// 知识点能力
pub mod knowledge_ability;

// 导出ELO相关实体模块
pub mod elo_factors;
pub mod elo_history;
pub mod knowledge_encounter;
pub mod question_elo_history;
pub mod streak_record;

// 导出自适应相关实体模块
pub mod ability;

// 导出学习计划相关实体模块
pub mod current_plans;
pub mod plan_history;

// SAT相关实体
pub mod sat_goal;
pub mod sat_win_streak;
pub mod sat_paper;
pub mod sat_paper_question;
pub mod exam_session;
pub mod exam_answer;
pub mod exam_statistics;
pub mod exam_module_progress;

// 缓存相关实体
pub mod user_learning_guidance_cache;

// AI使用记录实体
pub mod ai_usage_record;

// 重导出所有实体，便于使用
pub use self::chapter::Entity as Chapter;
pub use self::knowledge::Entity as Knowledge;
pub use self::knowledge_mastery::Entity as KnowledgeMastery;
pub use self::knowledge_type::Entity as KnowledgeType;
pub use self::question::Entity as Question;
pub use self::section::Entity as Section;
pub use self::student::Entity as Student;
pub use self::student_answer::Entity as StudentAnswer;
pub use self::subject::Entity as Subject;
// 使用student_answer代替submission
pub use self::recommendation::Entity as Recommendation;
pub use self::student_answer::Entity as Submission;
pub use self::student_knowledge_progress::Entity as StudentKnowledgeProgress;

// 重导出存储实现
pub use self::student::SeaOrmAbilityStorage;
// 导出新名称

pub use self::knowledge_ability::Entity as KnowledgeAbility;

// 重导出学习计划实体
pub use self::current_plans::Entity as CurrentPlans;
pub use self::plan_history::Entity as PlanHistory;

// 重导出SAT相关实体
pub use self::sat_goal::Entity as SatGoal;
pub use self::sat_win_streak::Entity as SatWinStreak;

// 重导出缓存相关实体
pub use self::user_learning_guidance_cache::Entity as UserLearningGuidanceCache;

// 注: 知识点能力存储实现已移至 crate::infrastructure::persistence::storage_impl::elo::knowledge_ability 模块
// 请使用 crate::infrastructure::persistence::storage_impl::elo::SeaOrmKnowledgeEloStorage

// Declare your SeaORM entity modules here
pub mod flash_card;
pub mod conversation;
// pub mod decks; // etc.

// Optional: Re-export entities for easier access if desired
// pub use flash_cards::Entity as FlashCardsEntity;
// pub use user_card_progress::Entity as UserCardProgressEntity;

pub mod batch_ability_record;
pub mod recommendation_batch; // 推荐批次实体 // 批次能力记录实体

// 重导出批次相关实体
pub use self::batch_ability_record::Entity as BatchAbilityRecord;
pub use self::recommendation_batch::Entity as RecommendationBatch;

// 重导出题库会话实体
pub use self::question_bank_session::Entity as QuestionBankSession;

// 重导出考试相关实体
pub use self::exam_session::Entity as ExamSession;
pub use self::exam_answer::Entity as ExamAnswer;
pub use self::exam_module_progress::Entity as ExamModuleProgress;
