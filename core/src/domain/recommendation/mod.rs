//! 推荐领域模块
//!
//! 包含推荐系统的核心领域模型、值对象、服务和仓储接口

// 新的DDD结构
pub mod entities;
pub mod events;
pub mod repositories;
pub mod services;
pub mod value_objects;

// 保持向后兼容的旧结构
pub mod models;
// pub mod repository; // 已删除：未使用的旧接口
pub mod service;

// 重新导出核心类型
pub use entities::{question::Question, recommendation::Recommendation};
pub use value_objects::{Difficulty, DifficultyRange, QuestionId, RecommendationContext, UserId};

// 保持向后兼容
pub use models::*;
// pub use repository::*; // 已删除：未使用的旧接口
pub use service::*;

use serde::{Deserialize, Serialize};

/// 推荐策略枚举
///
/// 定义不同的推荐策略，影响知识点选择和题目分配
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum RecommendationStrategy {
    /// 顺序学习策略
    /// 按照知识点的顺序进行推荐，优先推荐新知识点
    Sequential,

    /// 混合模式策略
    /// 混合新知识点和已学知识点，平衡学习和复习
    Mixed,

    /// 复习模式策略
    /// 主要推荐已学知识点，用于复习和巩固
    Review,
}

impl RecommendationStrategy {
    /// 获取策略的代码
    pub fn code(&self) -> &'static str {
        match self {
            Self::Sequential => "sequential",
            Self::Mixed => "mixed",
            Self::Review => "review",
        }
    }

    /// 从字符串解析策略
    pub fn from_str(s: &str) -> Option<Self> {
        match s.to_lowercase().as_str() {
            "sequential" => Some(Self::Sequential),
            "mixed" => Some(Self::Mixed),
            "review" => Some(Self::Review),
            _ => None,
        }
    }
}

impl Default for RecommendationStrategy {
    fn default() -> Self {
        Self::Sequential
    }
}

impl std::fmt::Display for RecommendationStrategy {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.code())
    }
}

impl std::str::FromStr for RecommendationStrategy {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Self::from_str(s).ok_or_else(|| format!("未知的推荐策略: {}", s))
    }
}

/// 知识点分配信息
///
/// 记录推荐中每个知识点的分配情况
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct KnowledgeAllocation {
    /// 知识点ID
    pub knowledge_id: i32,
    /// 知识点编码
    pub knowledge_code: String,
    /// 知识点名称
    pub knowledge_name: String,
    /// 分配的题目数量
    pub question_count: usize,
    /// 是否为新知识点
    pub is_new: bool,
}

impl KnowledgeAllocation {
    /// 创建新的知识点分配
    pub fn new(
        knowledge_id: i32,
        knowledge_code: String,
        knowledge_name: String,
        question_count: usize,
        is_new: bool,
    ) -> Self {
        Self {
            knowledge_id,
            knowledge_code,
            knowledge_name,
            question_count,
            is_new,
        }
    }
}
