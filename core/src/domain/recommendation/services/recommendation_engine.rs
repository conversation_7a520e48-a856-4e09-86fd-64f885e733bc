//! 推荐引擎领域服务
//!
//! 包含所有推荐相关的核心业务逻辑和算法
//! 这是推荐系统的核心，包含从原有系统迁移过来的所有算法逻辑

use async_trait::async_trait;
use std::collections::{HashMap, HashSet};

use super::super::RecommendationStrategy;
use crate::{Result, storage::dto::question::QuestionMetadata};

/// 知识点信息
///
/// 在推荐引擎中使用的知识点数据结构
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct KnowledgePoint {
    /// 知识点ID
    pub id: i32,
    /// 知识点名称
    pub name: String,
    /// 知识点编码
    pub code: String,
    /// 是否为新知识点（用户未学习过）
    pub is_new: bool,
}

impl KnowledgePoint {
    /// 创建新的知识点
    pub fn new(id: i32, name: String, code: String, is_new: bool) -> Self {
        Self {
            id,
            name,
            code,
            is_new,
        }
    }
}

/// 题目查询请求
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct QuestionQueryRequest {
    pub knowledge_id: i32,
    pub difficulty_range: (f64, f64),
    pub limit: usize,
    pub status: Option<i32>,
}

/// 题目提供者接口
///
/// 领域层定义的接口，用于获取题目数据
/// 基础设施层需要实现这个接口
#[async_trait]
pub trait QuestionProvider: Send + Sync {
    /// 获取指定知识点的题目
    async fn get_questions_for_knowledge_point(
        &self,
        knowledge_id: i32,
        difficulty_range: (f64, f64),
        limit: usize,
        exclude_ids: &[i32],
        status: Option<i32>,
    ) -> Result<Vec<QuestionMetadata>>;

    /// 批量获取多个知识点的题目
    /// 优化：解决N+1查询问题
    async fn batch_get_questions_for_knowledge_points(
        &self,
        requests: &[QuestionQueryRequest],
        exclude_ids: &[i32],
    ) -> Result<HashMap<i32, Vec<QuestionMetadata>>> {
        // 默认实现：逐个调用单个查询方法（向后兼容）
        let mut results = HashMap::new();
        for request in requests {
            let questions = self
                .get_questions_for_knowledge_point(
                    request.knowledge_id,
                    request.difficulty_range,
                    request.limit,
                    exclude_ids,
                    request.status,
                )
                .await?;
            results.insert(request.knowledge_id, questions);
        }
        Ok(results)
    }
}

/// 能力提供者接口
///
/// 领域层定义的接口，用于获取用户能力数据
/// 基础设施层需要实现这个接口
#[async_trait]
pub trait AbilityProvider: Send + Sync {
    /// 获取用户在指定知识点的能力值
    async fn get_user_ability(&self, user_id: &str, knowledge_id: i32) -> Result<f64>;

    /// 获取知识点的K值因子
    /// 与V1保持一致：支持动态K值获取
    async fn get_knowledge_k_factor(&self, user_id: &str, knowledge_id: i32) -> Result<f64>;

    /// 批量获取用户在多个知识点的能力值
    /// 优化：解决N+1查询问题
    async fn batch_get_user_abilities(
        &self,
        user_id: &str,
        knowledge_ids: &[i32],
    ) -> Result<HashMap<i32, f64>> {
        // 默认实现：逐个调用单个查询方法（向后兼容）
        let mut results = HashMap::new();
        for &knowledge_id in knowledge_ids {
            let ability = self.get_user_ability(user_id, knowledge_id).await?;
            results.insert(knowledge_id, ability);
        }
        Ok(results)
    }

    /// 批量获取多个知识点的K值因子
    /// 优化：解决N+1查询问题
    async fn batch_get_knowledge_k_factors(
        &self,
        user_id: &str,
        knowledge_ids: &[i32],
    ) -> Result<HashMap<i32, f64>> {
        // 默认实现：逐个调用单个查询方法（向后兼容）
        let mut results = HashMap::new();
        for &knowledge_id in knowledge_ids {
            let k_factor = self.get_knowledge_k_factor(user_id, knowledge_id).await?;
            results.insert(knowledge_id, k_factor);
        }
        Ok(results)
    }
}

/// 推荐结果
///
/// 包含推荐的题目和相关元数据
#[derive(Debug, Clone)]
pub struct RecommendationResult {
    /// 推荐的题目元数据
    pub questions: Vec<QuestionMetadata>,
    /// 知识点分配信息
    pub knowledge_allocations: Vec<KnowledgeAllocation>,
    /// 实际获取的题目统计
    pub actual_question_counts: HashMap<i32, usize>,
}

/// 知识点分配信息
#[derive(Debug, Clone)]
pub struct KnowledgeAllocation {
    pub knowledge_id: i32,
    pub knowledge_code: String,
    pub knowledge_name: String,
    pub question_count: usize,
    pub is_new: bool,
}

/// 用户能力信息
#[derive(Debug, Clone)]
pub struct UserAbility {
    /// 用户ID
    pub user_id: String,
    /// 整体ELO评分
    pub overall_elo: f64,
    /// 各知识点的能力值
    pub knowledge_abilities: HashMap<i32, f64>,
}

impl UserAbility {
    /// 创建新的用户能力
    pub fn new(user_id: String, overall_elo: f64) -> Self {
        Self {
            user_id,
            overall_elo,
            knowledge_abilities: HashMap::new(),
        }
    }

    /// 获取指定知识点的能力值
    pub fn get_knowledge_ability(&self, knowledge_id: i32) -> f64 {
        self.knowledge_abilities
            .get(&knowledge_id)
            .copied()
            .unwrap_or(self.overall_elo) // 如果没有特定知识点能力，使用整体能力
    }

    /// 设置知识点能力
    pub fn set_knowledge_ability(&mut self, knowledge_id: i32, ability: f64) {
        self.knowledge_abilities.insert(knowledge_id, ability);
    }
}

// 移除重复的KnowledgePoint定义，使用上面的定义

/// 推荐引擎领域服务
///
/// 包含所有推荐相关的核心业务逻辑和算法
/// 这是从原有MasteryRecommendationServiceImpl迁移过来的核心算法
/// 增强了容错机制和降级策略
pub struct RecommendationEngine {
    /// 自适应掌握度算法（用于难度计算）
    adaptive_algo: crate::algorithms::adaptive_mastery::AdaptiveMasteryAlgorithm,
    /// 容错配置
    fallback_config: FallbackConfig,
    // 双塔模型特征提取器（可选，用于增强推荐）
    //feature_extractor: Option<Arc<dyn FeatureExtractor>>
}

/// 推荐请求参数
#[derive(Debug, Clone)]
pub struct RecommendationRequest {
    /// 学生ID
    pub student_id: String,
    /// 小节ID
    pub section_id: i32,
    /// 推荐题目数量限制
    pub limit: usize,
    /// 推荐策略
    pub strategy: Option<RecommendationStrategy>,
    /// 题目状态过滤
    pub status: Option<i32>,
}

/// 推荐数据快照
///
/// 在推荐开始时获取一致性快照，确保整个推荐过程中数据的一致性
#[derive(Debug, Clone)]
pub struct RecommendationSnapshot {
    /// 已答题目ID列表（推荐开始时的快照）
    pub answered_question_ids: Vec<i32>,
    /// 用户在各知识点的能力值（推荐开始时的快照）
    pub user_abilities: HashMap<i32, f64>,
    /// 各知识点的K因子（推荐开始时的快照）
    pub k_factors: HashMap<i32, f64>,
    /// 快照创建时间
    pub timestamp: std::time::Instant,
    /// 快照版本号（用于检测数据变更）
    pub version: u64,
}

/// 知识点分组信息
#[derive(Debug, Clone)]
pub struct KnowledgePointGroups {
    /// 新知识点（未学习）
    pub new_points: Vec<KnowledgePoint>,
    /// 已学习知识点
    pub learned_points: Vec<KnowledgePoint>,
    /// 所有知识点
    pub all_points: Vec<KnowledgePoint>,
    /// 最后学习的知识点代码
    pub last_code: String,
}

impl RecommendationEngine {
    /// 创建新的推荐引擎
    pub fn new(
        adaptive_algo: crate::algorithms::adaptive_mastery::AdaptiveMasteryAlgorithm,
    ) -> Self {
        Self {
            adaptive_algo,
            fallback_config: FallbackConfig::default(),
        }
    }

    /// 创建带自定义容错配置的推荐引擎
    pub fn with_fallback_config(
        adaptive_algo: crate::algorithms::adaptive_mastery::AdaptiveMasteryAlgorithm,
        fallback_config: FallbackConfig,
    ) -> Self {
        Self {
            adaptive_algo,
            fallback_config,
        }
    }

    /// 创建推荐数据快照
    ///
    /// 在推荐开始时获取一致性快照，确保整个推荐过程中数据的一致性
    pub async fn create_recommendation_snapshot(
        &self,
        request: &RecommendationRequest,
        knowledge_points: &[KnowledgePoint],
        recommendation_repository: &dyn SnapshotRecommendationRepository,
        ability_provider: &dyn AbilityProvider,
    ) -> Result<RecommendationSnapshot> {
        let start_time = std::time::Instant::now();

        // 1. 获取已答题目ID列表（带版本号）
        let (answered_question_ids, version) = recommendation_repository
            .get_answered_question_ids_with_version(&request.student_id)
            .await?;

        // 2. 批量获取用户能力值和K因子
        let knowledge_ids: Vec<i32> = knowledge_points.iter().map(|kp| kp.id).collect();

        let (user_abilities, k_factors) = tokio::try_join!(
            ability_provider.batch_get_user_abilities(&request.student_id, &knowledge_ids),
            ability_provider.batch_get_knowledge_k_factors(&request.student_id, &knowledge_ids)
        )?;

        let snapshot = RecommendationSnapshot {
            answered_question_ids,
            user_abilities,
            k_factors,
            timestamp: start_time,
            version,
        };

        tracing::debug!(
            "Created recommendation snapshot for student {} with {} knowledge points, version {}, took {:?}",
            request.student_id,
            knowledge_ids.len(),
            version,
            start_time.elapsed()
        );

        Ok(snapshot)
    }

    /// 验证快照是否仍然有效
    ///
    /// 检查数据版本是否发生变化，如果变化则快照可能已过期
    pub async fn validate_snapshot(
        &self,
        snapshot: &RecommendationSnapshot,
        student_id: &str,
        recommendation_repository: &dyn SnapshotRecommendationRepository,
    ) -> Result<bool> {
        // 检查快照是否过期（超过5分钟认为过期）
        if snapshot.timestamp.elapsed() > std::time::Duration::from_secs(300) {
            tracing::warn!("Recommendation snapshot expired for student {}", student_id);
            return Ok(false);
        }

        // 检查数据版本是否发生变化
        let (_, current_version) = recommendation_repository
            .get_answered_question_ids_with_version(student_id)
            .await?;

        if current_version != snapshot.version {
            tracing::warn!(
                "Recommendation snapshot version mismatch for student {}: expected {}, got {}",
                student_id, snapshot.version, current_version
            );
            return Ok(false);
        }

        Ok(true)
    }

    /// 使用快照进行推荐（数据一致性优化版本）
    ///
    /// 这个方法确保整个推荐过程中使用一致的数据快照
    pub async fn recommend_with_snapshot(
        &self,
        request: &RecommendationRequest,
        knowledge_points: &[KnowledgePoint],
        last_knowledge_code: &str,
        question_provider: &dyn QuestionProvider,
        recommendation_repository: &dyn SnapshotRecommendationRepository,
        ability_provider: &dyn AbilityProvider,
    ) -> Result<RecommendationResult> {
        // 1. 创建数据快照
        let snapshot = self
            .create_recommendation_snapshot(
                request,
                knowledge_points,
                recommendation_repository,
                ability_provider,
            )
            .await?;

        // 2. 使用快照数据进行推荐
        let result = self
            .recommend_with_snapshot_data(
                request,
                knowledge_points,
                last_knowledge_code,
                &snapshot,
                question_provider,
            )
            .await?;

        // 3. 验证快照在推荐完成时是否仍然有效
        let is_valid = self
            .validate_snapshot(&snapshot, &request.student_id, recommendation_repository)
            .await?;

        if !is_valid {
            tracing::warn!(
                "Recommendation snapshot became invalid during processing for student {}",
                request.student_id
            );
            // 可以选择重新推荐或返回警告
        }

        Ok(result)
    }

    /// 使用快照数据进行推荐的方法
    ///
    /// 公开方法，用于测试和特殊场景
    pub async fn recommend_with_snapshot_data(
        &self,
        request: &RecommendationRequest,
        knowledge_points: &[KnowledgePoint],
        last_knowledge_code: &str,
        snapshot: &RecommendationSnapshot,
        question_provider: &dyn QuestionProvider,
    ) -> Result<RecommendationResult> {
        // 1. 知识点分组 - 核心业务逻辑
        let groups = self.group_knowledge_points(knowledge_points, last_knowledge_code);

        // 2. 知识点选择 - 核心业务逻辑
        let strategy = request
            .strategy
            .unwrap_or(RecommendationStrategy::Sequential);
        let knowledge_question_counts =
            self.select_knowledge_points(&groups, request.limit, strategy);

        // 3. 为每个知识点获取题目 - 使用快照数据
        let mut questions_map: HashMap<i32, Vec<QuestionMetadata>> = HashMap::new();
        let mut all_question_ids = Vec::new();
        let mut knowledge_map: HashMap<String, (i32, String)> = HashMap::new();
        let mut actual_question_counts: HashMap<i32, usize> = HashMap::new();

        for (kp, needed) in &knowledge_question_counts {
            if *needed > 0 {
                // 从快照中获取能力值和K因子（确保数据一致性）
                let user_ability = snapshot.user_abilities.get(&kp.id).copied().unwrap_or(1700.0);
                let k_factor = snapshot.k_factors.get(&kp.id).copied().unwrap_or(150.0);

                // 计算动态难度区间 - 核心算法
                let (min_diff, max_diff) = self
                    .adaptive_algo
                    .calculate_difficulty_range(user_ability, k_factor);

                // 使用扩展难度区间策略获取题目 - 使用快照中的已答题目列表
                let questions = self
                    .get_questions_with_expanding_difficulty(
                        kp.id,
                        *needed,
                        min_diff,
                        max_diff,
                        &snapshot.answered_question_ids, // 使用快照数据
                        request.status,
                        question_provider,
                    )
                    .await?;

                // 记录实际获取的题目数量
                actual_question_counts.insert(kp.id, questions.len());

                for q in &questions {
                    knowledge_map.insert(q.id.clone(), (kp.id, kp.code.clone()));
                    all_question_ids.push(q.id.clone());
                }

                if !questions.is_empty() {
                    questions_map.insert(kp.id, questions);
                }
            }
        }

        // 4. 构建推荐结果 - 收集所有题目元数据
        let mut all_questions = Vec::new();
        for questions in questions_map.values() {
            all_questions.extend(questions.clone());
        }

        // 构建知识点分配信息
        let mut knowledge_allocations = Vec::new();
        for (kp, _requested) in &knowledge_question_counts {
            let actual = actual_question_counts.get(&kp.id).copied().unwrap_or(0);
            knowledge_allocations.push(KnowledgeAllocation {
                knowledge_id: kp.id,
                knowledge_code: kp.code.clone(),
                knowledge_name: kp.name.clone(),
                question_count: actual,
                is_new: kp.is_new,
            });
        }

        Ok(RecommendationResult {
            questions: all_questions,
            knowledge_allocations,
            actual_question_counts,
        })
    }

    /// 核心推荐方法
    ///
    /// 这是从MasteryRecommendationServiceImpl迁移过来的完整推荐逻辑
    /// 包含知识点选择、能力计算、难度区间计算、题目筛选等所有核心算法
    pub async fn recommend(
        &self,
        request: &RecommendationRequest,
        knowledge_points: &[KnowledgePoint],
        last_knowledge_code: &str,
        answered_ids: &[i32],
        question_provider: &dyn QuestionProvider,
        ability_provider: &dyn AbilityProvider,
    ) -> Result<RecommendationResult> {
        // 🔍 调试日志：推荐开始（仅在debug级别）
        tracing::debug!(
            "🚀 开始推荐 - 学生: {}, 小节: {}, 需要题目数: {}, 已答题目数: {}, 可选知识点数: {}",
            request.student_id,
            request.section_id,
            request.limit,
            answered_ids.len(),
            knowledge_points.len()
        );
        // 1. 知识点分组 - 核心业务逻辑
        let groups = self.group_knowledge_points(knowledge_points, last_knowledge_code);

        // 2. 知识点选择 - 核心业务逻辑
        let strategy = request
            .strategy
            .unwrap_or(RecommendationStrategy::Sequential);
        let knowledge_question_counts =
            self.select_knowledge_points(&groups, request.limit, strategy);

        // 3. 批量获取能力值和K因子 - 优化：解决N+1查询问题
        let knowledge_ids: Vec<i32> = knowledge_question_counts.keys().map(|kp| kp.id).collect();

        let (user_abilities, k_factors) = tokio::try_join!(
            ability_provider.batch_get_user_abilities(&request.student_id, &knowledge_ids),
            ability_provider.batch_get_knowledge_k_factors(&request.student_id, &knowledge_ids)
        )?;

        // 4. 为每个知识点获取题目 - 核心算法逻辑（并发优化）
        let mut questions_map: HashMap<i32, Vec<QuestionMetadata>> = HashMap::new();
        let mut all_question_ids = Vec::new();
        let mut knowledge_map: HashMap<String, (i32, String)> = HashMap::new();
        let mut actual_question_counts: HashMap<i32, usize> = HashMap::new();

        // 🚀 性能优化：并发获取所有知识点的题目
        let question_futures: Vec<_> = knowledge_question_counts
            .iter()
            .filter(|(_, needed)| **needed > 0)
            .map(|(kp, needed)| {
                let user_ability = user_abilities.get(&kp.id).copied().unwrap_or(1700.0);
                let k_factor = k_factors.get(&kp.id).copied().unwrap_or(150.0);

                // 计算动态难度区间 - 核心算法
                let (min_diff, max_diff) = self
                    .adaptive_algo
                    .calculate_difficulty_range(user_ability, k_factor);

                let kp_clone = kp.clone();
                let needed_clone = *needed;
                let answered_ids_clone = answered_ids.to_vec();
                let status = request.status;

                async move {
                    let questions = self
                        .get_questions_with_expanding_difficulty(
                            kp_clone.id,
                            needed_clone,
                            min_diff,
                            max_diff,
                            &answered_ids_clone,
                            status,
                            question_provider,
                        )
                        .await?;
                    Ok::<_, crate::Error>((kp_clone, questions))
                }
            })
            .collect();

        // 并发执行所有查询
        let question_results = futures::future::try_join_all(question_futures).await?;

        // 处理查询结果
        for (kp, questions) in question_results {
            // 🔍 调试日志：打印每个知识点获取的题目（仅在debug级别）
            if tracing::enabled!(tracing::Level::DEBUG) {
                let question_ids: Vec<&String> = questions.iter().map(|q| &q.id).collect();
                tracing::debug!(
                    "📖 知识点: {} (ID: {}) - 获取: {}, 题目ID: {:?}",
                    kp.name,
                    kp.id,
                    questions.len(),
                    question_ids
                );
            }

            // 记录实际获取的题目数量
            actual_question_counts.insert(kp.id, questions.len());

            for q in &questions {
                knowledge_map.insert(q.id.clone(), (kp.id, kp.code.clone()));
                all_question_ids.push(q.id.clone());
            }

            if !questions.is_empty() {
                questions_map.insert(kp.id, questions);
            }
        }

        // 4. 补充题目逻辑 - 如果题目不足，从其他知识点补充
        let total_questions: usize = questions_map.values().map(|v| v.len()).sum();
        if total_questions < request.limit {
            let remaining_needed = request.limit - total_questions;

            // 与V1保持一致：收集已使用的知识点ID
            let used_knowledge_point_ids: std::collections::HashSet<i32> =
                knowledge_question_counts.keys().map(|kp| kp.id).collect();

            tracing::debug!(
                "🔄 题目不足，开始补充题目 - 当前: {}, 需要: {}, 缺少: {}",
                total_questions,
                request.limit,
                remaining_needed
            );

            let supplementary_questions = self
                .get_supplementary_questions(
                    &groups.all_points,
                    &used_knowledge_point_ids,
                    remaining_needed,
                    answered_ids,
                    &all_question_ids,
                    request.status,
                    question_provider,
                    ability_provider,
                    &request.student_id,
                )
                .await?;

            // 🔍 调试日志：打印补充题目（仅在debug级别）
            if tracing::enabled!(tracing::Level::DEBUG) {
                let supplementary_ids: Vec<&String> = supplementary_questions.iter().map(|q| &q.id).collect();
                tracing::debug!(
                    "➕ 补充题目获取完成 - 数量: {}, 题目ID: {:?}",
                    supplementary_questions.len(),
                    supplementary_ids
                );
            }

            // 将补充题目添加到结果中（去重处理）
            let mut added_question_ids = std::collections::HashSet::new();
            for q in &supplementary_questions {
                // 根据题目的knowledge_id找到对应的知识点
                if let Some(kp) = groups.all_points.iter().find(|kp| kp.id == q.knowledge_id) {
                    // 只有当题目ID未被添加过时才进行计数和添加
                    if added_question_ids.insert(q.id.clone()) {
                        knowledge_map.insert(q.id.clone(), (kp.id, kp.code.clone()));
                        all_question_ids.push(q.id.clone());
                        *actual_question_counts.entry(kp.id).or_insert(0) += 1;
                        questions_map
                            .entry(kp.id)
                            .or_insert_with(Vec::new)
                            .push(q.clone());
                    }
                }
            }
        }

        // 5. 最终fallback检查：如果题目总数仍然不足，忽略已答题目进行全局fallback推荐
        let mut all_questions: Vec<QuestionMetadata> = questions_map.into_values().flatten().collect();

        if all_questions.len() < request.limit {
            let final_needed = request.limit - all_questions.len();

            tracing::warn!(
                "最终题目数量仍不足 ({}/{}), 尝试全局fallback推荐（忽略已答题目）",
                all_questions.len(),
                request.limit
            );

            // 从所有知识点中获取题目，忽略已答题目
            for (kp, _needed) in &knowledge_question_counts {
                if all_questions.len() >= request.limit {
                    break;
                }

                // 全局fallback：不考虑难度限制，直接获取知识点的所有可用题目
                let global_fallback_questions = question_provider
                    .get_questions_for_knowledge_point(
                        kp.id,
                        (900.0, 2500.0), // 使用极宽的难度范围，覆盖所有题目
                        final_needed,
                        &[], // 空的排除列表，不排除任何题目
                        request.status,
                    )
                    .await?;

                // 过滤掉已存在的题目（避免重复）
                let existing_ids: std::collections::HashSet<String> =
                    all_questions.iter().map(|q| q.id.clone()).collect();

                let filtered_global_fallback: Vec<_> = global_fallback_questions
                    .into_iter()
                    .filter(|q| !existing_ids.contains(&q.id))
                    .take(final_needed)
                    .collect();

                // 🔍 调试日志：打印fallback题目（仅在debug级别）
                if tracing::enabled!(tracing::Level::DEBUG) && !filtered_global_fallback.is_empty() {
                    let fallback_ids: Vec<&String> = filtered_global_fallback.iter().map(|q| &q.id).collect();
                    tracing::debug!(
                        "🆘 知识点 {} fallback题目 - 数量: {}, 题目ID: {:?}",
                        kp.name,
                        fallback_ids.len(),
                        fallback_ids
                    );
                }

                all_questions.extend(filtered_global_fallback);
            }

            tracing::debug!(
                "全局fallback推荐完成，最终获取到 {} 道题目",
                all_questions.len()
            );
        }

        // 6. 构建知识点分配信息
        let knowledge_allocations = self.build_knowledge_allocations(
            &knowledge_question_counts,
            &actual_question_counts,
            &groups.all_points,
            last_knowledge_code,
        );

        // 🔍 调试日志：打印推荐的题目ID（保留info级别，但优化性能）
        tracing::info!(
            "🎯 推荐结果 - 学生: {}, 总题目数: {}",
            request.student_id,
            all_questions.len()
        );

        // 🔍 详细的题目ID日志仅在debug级别显示
        if tracing::enabled!(tracing::Level::DEBUG) {
            let question_ids: Vec<&String> = all_questions.iter().map(|q| &q.id).collect();
            tracing::debug!("题目ID: {:?}", question_ids);

            // 按知识点分组打印
            let mut questions_by_knowledge: std::collections::HashMap<i32, Vec<&String>> = std::collections::HashMap::new();
            for q in &all_questions {
                questions_by_knowledge.entry(q.knowledge_id).or_insert_with(Vec::new).push(&q.id);
            }

            for (knowledge_id, ids) in &questions_by_knowledge {
                if let Some(kp) = groups.all_points.iter().find(|kp| kp.id == *knowledge_id) {
                    tracing::debug!(
                        "📚 知识点: {} (ID: {}) - 题目数: {}, 题目ID: {:?}",
                        kp.name,
                        knowledge_id,
                        ids.len(),
                        ids
                    );
                }
            }
        }

        Ok(RecommendationResult {
            questions: all_questions,
            knowledge_allocations,
            actual_question_counts,
        })
    }

    /// 知识点分组 - 核心业务逻辑
    ///
    /// 将知识点分为新知识点和已学知识点
    fn group_knowledge_points(
        &self,
        knowledge_points: &[KnowledgePoint],
        last_knowledge_code: &str,
    ) -> KnowledgePointGroups {
        let all_points = knowledge_points.to_vec();

        let (new_points, learned_points): (Vec<_>, Vec<_>) = knowledge_points
            .iter()
            .cloned()
            .partition(|kp| kp.code.as_str() > last_knowledge_code);

        KnowledgePointGroups {
            new_points,
            learned_points,
            all_points,
            last_code: last_knowledge_code.to_string(),
        }
    }

    /// 知识点选择 - 核心业务逻辑
    ///
    /// 根据推荐策略选择要推荐的知识点和对应的题目数量
    /// 修改为与V1保持一致：统一使用随机选择算法
    fn select_knowledge_points(
        &self,
        groups: &KnowledgePointGroups,
        total_limit: usize,
        _strategy: RecommendationStrategy,
    ) -> HashMap<KnowledgePoint, usize> {
        // 与V1保持一致：统一使用随机选择算法，忽略策略参数
        self.select_knowledge_points_random(&groups.new_points, &groups.learned_points, total_limit)
    }

    /// 随机选择知识点 - 按照用户期望的逻辑实现
    ///
    /// 用户期望的逻辑：
    /// 1. 新知识点选择1-2个，每个新知识点2-3道题
    /// 2. 剩余题目优先从旧知识点选择
    /// 3. 如果没有旧知识点，就从已选的新知识点继续选择
    pub fn select_knowledge_points_random(
        &self,
        new_points: &[KnowledgePoint],
        learned_points: &[KnowledgePoint],
        total_limit: usize,
    ) -> HashMap<KnowledgePoint, usize> {
        let mut result = HashMap::new();
        // 使用fastrand提高性能

        // 如果没有知识点可选，直接返回空集
        if new_points.is_empty() && learned_points.is_empty() {
            return result;
        }

        // 按顺序选择新知识点（先按code排序，确保从左到右学习）
        let mut sorted_new_points = new_points.to_vec();
        sorted_new_points.sort_by(|a, b| a.code.cmp(&b.code));

        // 第一步：选择1-2个新知识点
        let max_new_knowledge_points = if total_limit <= 3 { 1 } else { 2 }; // 根据总题目数动态调整
        let selected_new_points: Vec<_> = sorted_new_points
            .into_iter()
            .take(max_new_knowledge_points.min(new_points.len()))
            .collect();

        // 第二步：为每个新知识点分配2-3道题
        let mut allocated_questions = 0;
        let questions_per_new_point = if total_limit <= 4 { 2 } else { 3 }; // 每个新知识点2-3题

        for point in &selected_new_points {
            let questions_for_this_point =
                questions_per_new_point.min(total_limit - allocated_questions);
            if questions_for_this_point > 0 {
                result.insert(point.clone(), questions_for_this_point);
                allocated_questions += questions_for_this_point;
            }
        }

        // 第三步：处理剩余题目
        let remaining_questions = total_limit - allocated_questions;
        if remaining_questions > 0 {
            if !learned_points.is_empty() {
                // 优先从旧知识点选择
                let mut learned_points_copy = learned_points.to_vec();
                fastrand::shuffle(&mut learned_points_copy);

                // 选择1-2个旧知识点
                let max_review_points = if remaining_questions <= 2 { 1 } else { 2 };
                let selected_review_points: Vec<_> = learned_points_copy
                    .into_iter()
                    .take(max_review_points)
                    .collect();

                // 为旧知识点分配剩余题目
                let questions_per_review =
                    (remaining_questions / selected_review_points.len()).max(1);
                let mut remaining = remaining_questions;
                let review_points_count = selected_review_points.len();

                for (i, point) in selected_review_points.into_iter().enumerate() {
                    let questions_for_this_point = if i == review_points_count - 1 {
                        remaining // 最后一个知识点分配所有剩余题目
                    } else {
                        questions_per_review.min(remaining)
                    };

                    if questions_for_this_point > 0 {
                        result.insert(point, questions_for_this_point);
                        remaining -= questions_for_this_point;
                    }
                }
            } else {
                // 如果没有旧知识点，从已选的新知识点继续选择
                if !selected_new_points.is_empty() {
                    // 将剩余题目平均分配给已选的新知识点
                    let additional_per_point = remaining_questions / selected_new_points.len();
                    let mut extra_remainder = remaining_questions % selected_new_points.len();

                    for point in &selected_new_points {
                        if let Some(current_count) = result.get_mut(point) {
                            *current_count += additional_per_point;
                            if extra_remainder > 0 {
                                *current_count += 1;
                                extra_remainder -= 1;
                            }
                        }
                    }
                }
            }
        }

        result
    }

    /// 获取题目，采用一次性查询+内存筛选策略（超级性能优化版本）
    ///
    /// 新的优化策略：
    /// 1. 一次性查询知识点的所有激活题目
    /// 2. 在内存中进行难度筛选和扩展
    /// 3. 避免多次数据库往返，大幅提升性能
    ///
    /// 性能优化：
    /// 1. 单次数据库查询替代多次扩展查询
    /// 2. 内存筛选速度远快于数据库查询
    /// 3. 减少网络往返开销
    /// 4. 支持更灵活的筛选逻辑
    async fn get_questions_with_expanding_difficulty(
        &self,
        knowledge_id: i32,
        count: usize,
        min_diff: f64,
        max_diff: f64,
        answered_ids: &[i32],
        status: Option<i32>,
        question_provider: &dyn QuestionProvider,
    ) -> Result<Vec<QuestionMetadata>> {
        // 🚀 超级优化：一次性获取知识点的所有激活题目
        let all_questions = self
            .get_all_knowledge_questions(knowledge_id, status, question_provider)
            .await?;

        if all_questions.is_empty() {
            return Ok(Vec::new());
        }

        // 转换已答题目ID为HashSet，提升查找性能
        let answered_set: HashSet<i32> = answered_ids.iter().cloned().collect();

        // 🚀 内存筛选：按难度区间筛选，支持动态扩展
        let filtered_questions = self.filter_questions_by_difficulty_in_memory(
            &all_questions,
            min_diff,
            max_diff,
            count,
            &answered_set,
        );

        Ok(filtered_questions)
    }

    /// 一次性获取知识点的所有激活题目
    async fn get_all_knowledge_questions(
        &self,
        knowledge_id: i32,
        status: Option<i32>,
        question_provider: &dyn QuestionProvider,
    ) -> Result<Vec<QuestionMetadata>> {
        // 使用一个很大的limit来获取所有题目，实际上大部分知识点题目数量都不会超过1000
        const MAX_QUESTIONS_PER_KNOWLEDGE: usize = 200;

        question_provider
            .get_questions_for_knowledge_point(
                knowledge_id,
                (900.0, 2500.0), // 覆盖所有可能的难度范围
                MAX_QUESTIONS_PER_KNOWLEDGE,
                &[],    // 不排除任何题目，在内存中处理
                status,
            )
            .await
    }

    /// 在内存中按难度区间筛选题目，支持动态扩展
    fn filter_questions_by_difficulty_in_memory(
        &self,
        all_questions: &[QuestionMetadata],
        initial_min_diff: f64,
        initial_max_diff: f64,
        needed_count: usize,
        answered_set: &HashSet<i32>,
    ) -> Vec<QuestionMetadata> {
        let mut current_min_diff = initial_min_diff;
        let mut current_max_diff = initial_max_diff;
        let mut expansion_attempts = 0;
        const MAX_EXPANSION: usize = 5;


        while expansion_attempts < MAX_EXPANSION {
            // 🚀 内存筛选：按当前难度区间筛选题目
            let mut filtered_questions: Vec<QuestionMetadata> = all_questions
                .iter()
                .filter(|q| {
                    // 检查ELO范围
                    if let Some(elo) = q.elo_rating {
                        elo >= current_min_diff && elo <= current_max_diff
                    } else {
                        // 如果没有ELO评分，使用difficulty字段
                        q.difficulty >= current_min_diff && q.difficulty <= current_max_diff
                    }
                })
                .filter(|q| {
                    // 排除已答题目
                    let question_id = q.id.parse::<i32>().unwrap_or(0);
                    !answered_set.contains(&question_id)
                })
                .cloned()
                .collect();

            // 如果题目足够，返回结果
            if filtered_questions.len() >= needed_count {
                // 随机打乱并返回所需数量
                fastrand::shuffle(&mut filtered_questions);
                return filtered_questions.into_iter().take(needed_count).collect();
            }

            // 扩展难度区间
            let diff_range = initial_max_diff - initial_min_diff;
            let expansion = diff_range * 0.5 * (expansion_attempts + 1) as f64;
            current_min_diff = (initial_min_diff - expansion).max(900.0);
            current_max_diff = (initial_max_diff + expansion).min(2500.0);

            expansion_attempts += 1;
        }

        // 如果扩展后仍不足，返回所有符合条件的题目
        let mut final_questions: Vec<QuestionMetadata> = all_questions
            .iter()
            .filter(|q| {
                let question_id = q.id.parse::<i32>().unwrap_or(0);
                !answered_set.contains(&question_id)
            })
            .cloned()
            .collect();

        fastrand::shuffle(&mut final_questions);
        final_questions.into_iter().take(needed_count).collect()

    }



    /// 获取补充题目（性能优化版本）
    ///
    /// 当主要知识点的题目不足时，从其他知识点补充题目
    /// 修改为与V1保持一致：只从未使用的知识点补充，避免覆盖原有题目
    ///
    /// 性能优化：
    /// 1. 预分配容量
    /// 2. 使用HashSet进行快速查找
    /// 3. 减少不必要的克隆操作
    async fn get_supplementary_questions(
        &self,
        all_knowledge_points: &[KnowledgePoint],
        used_knowledge_point_ids: &std::collections::HashSet<i32>,
        needed_count: usize,
        answered_ids: &[i32],
        existing_question_ids: &[String],
        status: Option<i32>,
        question_provider: &dyn QuestionProvider,
        ability_provider: &dyn AbilityProvider,
        student_id: &str,
    ) -> Result<Vec<QuestionMetadata>> {
        // 性能优化：预分配容量
        let mut supplementary_questions = Vec::with_capacity(needed_count);

        // 性能优化：将existing_question_ids转换为HashSet，提高查找效率
        let existing_ids_set: std::collections::HashSet<&String> = existing_question_ids.iter().collect();

        // 与V1保持一致：过滤出未使用的知识点（避免克隆，使用引用）
        let mut unused_knowledge_points: Vec<&KnowledgePoint> = all_knowledge_points
            .iter()
            .filter(|kp| !used_knowledge_point_ids.contains(&kp.id))
            .collect();

        // 与V1保持一致：按知识点code排序未使用的知识点，确保补充题目也按顺序
        unused_knowledge_points.sort_by(|a, b| a.code.cmp(&b.code));

        if unused_knowledge_points.is_empty() {
            return Ok(Vec::new());
        }

        // 第一轮：正常补充（排除已答题目）
        for kp in &unused_knowledge_points {
            if supplementary_questions.len() >= needed_count {
                break;
            }

            // 获取用户在该知识点的能力
            let user_ability = ability_provider.get_user_ability(student_id, kp.id).await?;

            // 获取知识点的K值因子 - 与V1保持一致：支持动态K值
            let k_factor = ability_provider
                .get_knowledge_k_factor(student_id, kp.id)
                .await?;

            // 计算难度区间
            let (min_diff, max_diff) = self
                .adaptive_algo
                .calculate_difficulty_range(user_ability, k_factor);

            // 获取题目
            let questions = question_provider
                .get_questions_for_knowledge_point(
                    kp.id,
                    (min_diff, max_diff),
                    needed_count - supplementary_questions.len(),
                    answered_ids,
                    status,
                )
                .await?;

            // 性能优化：使用HashSet进行快速查找，避免重复过滤
            supplementary_questions.extend(
                questions
                    .into_iter()
                    .filter(|q| !existing_ids_set.contains(&q.id))
            );
        }

        // 第二轮：如果补充题目仍不足，忽略已答题目进行fallback推荐
        if supplementary_questions.len() < needed_count {
            let remaining_needed = needed_count - supplementary_questions.len();

            tracing::warn!(
                "补充题目仍不足 ({}/{}), 尝试忽略已答题目进行fallback推荐",
                supplementary_questions.len(),
                needed_count
            );

            for kp in &unused_knowledge_points {
                if supplementary_questions.len() >= needed_count {
                    break;
                }

                // 获取用户在该知识点的能力
                let user_ability = ability_provider.get_user_ability(student_id, kp.id).await?;
                let k_factor = ability_provider
                    .get_knowledge_k_factor(student_id, kp.id)
                    .await?;

                // 计算难度区间
                let (_min_diff, _max_diff) = self
                    .adaptive_algo
                    .calculate_difficulty_range(user_ability, k_factor);

                // 忽略已答题目，重新获取题目（fallback模式：不考虑难度限制）
                let fallback_questions = question_provider
                    .get_questions_for_knowledge_point(
                        kp.id,
                        (900.0, 2500.0), // 使用极宽的难度范围，覆盖所有题目
                        remaining_needed,
                        &[], // 空的排除列表，不排除任何题目
                        status,
                    )
                    .await?;

                // 性能优化：使用HashSet进行快速查找（但不过滤已答题目）
                supplementary_questions.extend(
                    fallback_questions
                        .into_iter()
                        .filter(|q| !existing_ids_set.contains(&q.id))
                );
            }

            tracing::info!(
                "fallback补充推荐完成，最终获取到 {} 道补充题目",
                supplementary_questions.len()
            );
        }

        Ok(supplementary_questions
            .into_iter()
            .take(needed_count)
            .collect())
    }

    /// 构建知识点分配信息
    ///
    /// 根据实际获取的题目数量构建知识点分配信息
    fn build_knowledge_allocations(
        &self,
        knowledge_question_counts: &HashMap<KnowledgePoint, usize>,
        actual_question_counts: &HashMap<i32, usize>,
        all_knowledge_points: &[KnowledgePoint],
        last_code: &str,
    ) -> Vec<KnowledgeAllocation> {
        let mut knowledge_allocations = Vec::new();

        // 处理主要知识点
        for (kp, _) in knowledge_question_counts {
            let actual_count = actual_question_counts.get(&kp.id).cloned().unwrap_or(0);
            if actual_count > 0 {
                knowledge_allocations.push(KnowledgeAllocation {
                    knowledge_id: kp.id,
                    knowledge_code: kp.code.clone(),
                    knowledge_name: kp.name.clone(),
                    question_count: actual_count,
                    is_new: kp.code.as_str() > last_code,
                });
            }
        }

        // 处理补充题目的知识点
        for (knowledge_id, count) in actual_question_counts {
            if !knowledge_question_counts
                .iter()
                .any(|(kp, _)| kp.id == *knowledge_id)
            {
                // 这是一个补充题目的知识点
                if let Some(kp) = all_knowledge_points
                    .iter()
                    .find(|kp| kp.id == *knowledge_id)
                {
                    knowledge_allocations.push(KnowledgeAllocation {
                        knowledge_id: kp.id,
                        knowledge_code: kp.code.clone(),
                        knowledge_name: kp.name.clone(),
                        question_count: *count,
                        is_new: kp.code.as_str() > last_code,
                    });
                }
            }
        }

        // 按知识点代码排序
        knowledge_allocations.sort_by(|a, b| a.knowledge_code.cmp(&b.knowledge_code));

        knowledge_allocations
    }

    // 移除了重复的方法定义，使用上面的实现

    // 移除了旧的简化版本的select_questions方法
    // 现在使用完整的recommend方法

    // 移除了旧的select_knowledge_points_by_strategy方法
    // 现在使用新的select_knowledge_points方法

    // 移除了旧的辅助方法，现在使用新的核心推荐方法
}

impl Default for RecommendationEngine {
    fn default() -> Self {
        let config = crate::algorithms::adaptive_mastery::AdaptiveMasteryConfig::default();
        Self::new(crate::algorithms::adaptive_mastery::AdaptiveMasteryAlgorithm::new(config))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use async_trait::async_trait;
    use mockall::mock;

    // Mock Provider实现
    mock! {
        TestQuestionProvider {}

        #[async_trait]
        impl QuestionProvider for TestQuestionProvider {
            async fn get_questions_for_knowledge_point(
                &self,
                knowledge_id: i32,
                difficulty_range: (f64, f64),
                limit: usize,
                exclude_ids: &[i32],
                status: Option<i32>,
            ) -> Result<Vec<QuestionMetadata>>;
        }
    }

    mock! {
        TestAbilityProvider {}

        #[async_trait]
        impl AbilityProvider for TestAbilityProvider {
            async fn get_user_ability(&self, user_id: &str, knowledge_id: i32) -> Result<f64>;
            async fn get_knowledge_k_factor(&self, user_id: &str, knowledge_id: i32) -> Result<f64>;
        }
    }

    fn create_test_knowledge_points() -> Vec<KnowledgePoint> {
        vec![
            KnowledgePoint {
                id: 1,
                name: "二次函数".to_string(),
                code: "1.2.1".to_string(),
                is_new: true,
            },
            KnowledgePoint {
                id: 2,
                name: "一次函数".to_string(),
                code: "1.1.1".to_string(),
                is_new: false,
            },
            KnowledgePoint {
                id: 3,
                name: "三次函数".to_string(),
                code: "1.3.1".to_string(),
                is_new: true,
            },
        ]
    }

    /// 创建测试用的题目元数据
    ///
    /// 这是测试辅助函数，用于生成测试数据
    fn create_test_questions(count: usize, knowledge_id: i32) -> Vec<QuestionMetadata> {
        (1..=count)
            .map(|i| QuestionMetadata {
                id: format!("q{}", i),
                subject_id: 1,
                knowledge_id,
                knowledge_name: Some(format!("知识点{}", knowledge_id)),
                type_id: 1,
                difficulty: 2.0 + (i as f64) * 2.0, // 2.0, 4.0, 6.0, 8.0...
                elo_rating: Some(2.0 + (i as f64) * 2.0), // 与difficulty相同，确保测试一致性
                correct_ratio: Some(0.8 - (i as f64) * 0.1),
            })
            .collect()
    }

    fn create_test_request() -> RecommendationRequest {
        RecommendationRequest {
            student_id: "test_user".to_string(),
            section_id: 5,
            limit: 5,
            strategy: Some(RecommendationStrategy::Sequential),
            status: Some(5),
        }
    }

    #[test]
    fn test_recommendation_engine_creation() {
        let adaptive_algo =
            crate::algorithms::adaptive_mastery::AdaptiveMasteryAlgorithm::default();
        let _engine = RecommendationEngine::new(adaptive_algo);

        // 验证引擎创建成功
        assert!(true); // 如果能创建就说明成功
    }

    #[test]
    fn test_group_knowledge_points() {
        let engine = RecommendationEngine::default();
        let knowledge_points = create_test_knowledge_points();

        let groups = engine.group_knowledge_points(&knowledge_points, "1.1.5");

        assert_eq!(groups.new_points.len(), 2); // 1.2.1 > 1.1.5, 1.3.1 > 1.1.5
        assert_eq!(groups.learned_points.len(), 1); // 1.1.1 < 1.1.5
        assert_eq!(groups.all_points.len(), 3);
    }

    #[test]
    fn test_group_knowledge_points_empty() {
        let engine = RecommendationEngine::default();
        let knowledge_points = vec![];

        let groups = engine.group_knowledge_points(&knowledge_points, "1.1.5");

        assert_eq!(groups.new_points.len(), 0);
        assert_eq!(groups.learned_points.len(), 0);
        assert_eq!(groups.all_points.len(), 0);
    }

    #[test]
    fn test_group_knowledge_points_all_new() {
        let engine = RecommendationEngine::default();
        let knowledge_points = vec![KnowledgePoint {
            id: 1,
            name: "高级知识点".to_string(),
            code: "2.1.1".to_string(),
            is_new: true,
        }];

        let groups = engine.group_knowledge_points(&knowledge_points, "1.9.9");

        assert_eq!(groups.new_points.len(), 1);
        assert_eq!(groups.learned_points.len(), 0);
        assert_eq!(groups.all_points.len(), 1);
    }

    #[test]
    fn test_group_knowledge_points_all_learned() {
        let engine = RecommendationEngine::default();
        let knowledge_points = vec![KnowledgePoint {
            id: 1,
            name: "基础知识点".to_string(),
            code: "1.1.1".to_string(),
            is_new: false,
        }];

        let groups = engine.group_knowledge_points(&knowledge_points, "1.9.9");

        assert_eq!(groups.new_points.len(), 0);
        assert_eq!(groups.learned_points.len(), 1);
        assert_eq!(groups.all_points.len(), 1);
    }

    #[test]
    fn test_select_knowledge_points_sequential() {
        let engine = RecommendationEngine::default();
        let knowledge_points = create_test_knowledge_points();
        let groups = engine.group_knowledge_points(&knowledge_points, "1.1.5");

        let result = engine.select_knowledge_points(&groups, 5, RecommendationStrategy::Sequential);

        assert!(!result.is_empty());

        // Sequential策略应该优先新知识点
        let has_new_points = result.keys().any(|kp| kp.is_new);
        assert!(has_new_points);
    }

    #[test]
    fn test_select_knowledge_points_sequential_empty() {
        let engine = RecommendationEngine::default();
        let groups = KnowledgePointGroups {
            new_points: vec![],
            learned_points: vec![],
            all_points: vec![],
            last_code: "1.1.5".to_string(),
        };

        let result = engine.select_knowledge_points(&groups, 5, RecommendationStrategy::Sequential);

        assert!(result.is_empty());
    }

    #[test]
    fn test_select_knowledge_points_sequential_only_new() {
        let engine = RecommendationEngine::default();
        let knowledge_points = create_test_knowledge_points();
        let groups = engine.group_knowledge_points(&knowledge_points, "1.0.0");

        let result = engine.select_knowledge_points(&groups, 5, RecommendationStrategy::Sequential);

        assert!(!result.is_empty());
        // 应该优先选择新知识点
        let new_count = result.keys().filter(|kp| kp.is_new).count();
        assert!(new_count > 0);
    }

    #[tokio::test]
    async fn test_fallback_when_insufficient_questions() {
        let engine = RecommendationEngine::default();
        let mut question_provider = MockTestQuestionProvider::new();

        // 设置Mock：多次调用，前几次返回空结果，最后一次fallback调用返回题目
        question_provider
            .expect_get_questions_for_knowledge_point()
            .returning(|_, _, _, exclude_ids, _| {
                if exclude_ids.is_empty() {
                    // fallback调用（不排除任何题目）
                    Ok(vec![
                        QuestionMetadata {
                            id: "fallback1".to_string(),
                            subject_id: 1,
                            knowledge_id: 1,
                            knowledge_name: Some("测试知识点".to_string()),
                            type_id: 1,
                            difficulty: 3.0,
                            elo_rating: Some(1600.0),
                            correct_ratio: Some(0.7),
                        },
                        QuestionMetadata {
                            id: "fallback2".to_string(),
                            subject_id: 1,
                            knowledge_id: 1,
                            knowledge_name: Some("测试知识点".to_string()),
                            type_id: 1,
                            difficulty: 4.0,
                            elo_rating: Some(1700.0),
                            correct_ratio: Some(0.6),
                        },
                    ])
                } else {
                    // 正常调用（排除已答题目），返回空结果模拟题目不足
                    Ok(vec![])
                }
            });

        let result = engine
            .get_questions_with_expanding_difficulty(
                1,
                2,
                1500.0,
                1700.0,
                &[1, 2, 3], // 模拟已答题目
                Some(5),
                &question_provider,
            )
            .await;

        assert!(result.is_ok());
        let questions = result.unwrap();

        // 验证fallback机制工作，返回了题目
        assert_eq!(questions.len(), 2);

        // 验证返回的题目ID包含预期的题目（不依赖顺序）
        let question_ids: Vec<&String> = questions.iter().map(|q| &q.id).collect();
        assert!(question_ids.contains(&&"fallback1".to_string()));
        assert!(question_ids.contains(&&"fallback2".to_string()));
    }

    #[test]
    fn test_select_knowledge_points_mixed_strategy() {
        let engine = RecommendationEngine::default();
        let knowledge_points = create_test_knowledge_points();
        let groups = engine.group_knowledge_points(&knowledge_points, "1.1.5");

        let result = engine.select_knowledge_points(&groups, 5, RecommendationStrategy::Mixed);

        assert!(!result.is_empty());
    }

    #[test]
    fn test_select_knowledge_points_review_strategy() {
        let engine = RecommendationEngine::default();
        let knowledge_points = create_test_knowledge_points();
        let groups = engine.group_knowledge_points(&knowledge_points, "1.1.5");

        let result = engine.select_knowledge_points(&groups, 5, RecommendationStrategy::Review);

        assert!(!result.is_empty());
    }

    #[test]
    fn test_select_questions_by_difficulty_tiers() {
        let engine = RecommendationEngine::default();

        let questions = vec![
            QuestionMetadata {
                id: "q1".to_string(),
                subject_id: 1,
                knowledge_id: 1,
                knowledge_name: Some("知识点1".to_string()),
                type_id: 1,
                difficulty: 2.0,
                elo_rating: Some(2.0), // 使用与难度范围匹配的ELO值
                correct_ratio: Some(0.8),
            },
            QuestionMetadata {
                id: "q2".to_string(),
                subject_id: 1,
                knowledge_id: 1,
                knowledge_name: Some("知识点1".to_string()),
                type_id: 1,
                difficulty: 5.0,
                elo_rating: Some(5.0), // 使用与难度范围匹配的ELO值
                correct_ratio: Some(0.7),
            },
            QuestionMetadata {
                id: "q3".to_string(),
                subject_id: 1,
                knowledge_id: 1,
                knowledge_name: Some("知识点1".to_string()),
                type_id: 1,
                difficulty: 8.0,
                elo_rating: Some(8.0), // 使用与难度范围匹配的ELO值
                correct_ratio: Some(0.6),
            },
        ];

        let answered_set = HashSet::new();
        let result = engine.filter_questions_by_difficulty_in_memory(&questions, 2.0, 8.0, 2, &answered_set);

        assert_eq!(result.len(), 2);
    }

    #[test]
    fn test_select_questions_by_difficulty_tiers_empty() {
        let engine = RecommendationEngine::default();
        let questions = vec![];

        let answered_set = HashSet::new();
        let result = engine.filter_questions_by_difficulty_in_memory(&questions, 1.0, 10.0, 5, &answered_set);

        assert!(result.is_empty());
    }

    #[test]
    fn test_select_questions_by_difficulty_tiers_single_tier() {
        let engine = RecommendationEngine::default();
        let questions = create_test_questions(6, 1);

        let answered_set = HashSet::new();
        let result = engine.filter_questions_by_difficulty_in_memory(&questions, 2.0, 12.0, 3, &answered_set);

        assert_eq!(result.len(), 3);
    }

    #[test]
    fn test_select_questions_by_difficulty_tiers_multiple_tiers() {
        let engine = RecommendationEngine::default();
        let questions = create_test_questions(9, 1); // 难度从2.0到18.0

        let answered_set = HashSet::new();
        let result = engine.filter_questions_by_difficulty_in_memory(&questions, 2.0, 18.0, 6, &answered_set);

        assert_eq!(result.len(), 6);

        // 验证难度分布 - 由于随机选择，我们只验证结果在预期范围内
        let difficulties: Vec<f64> = result.iter().map(|q| q.difficulty).collect();

        // 所有题目都应该在指定范围内
        assert!(difficulties.iter().all(|&d| d >= 2.0 && d <= 18.0));

        // 由于是随机选择，我们不能保证特定的难度分布
        // 但可以验证选择的题目确实来自原始题目集合
        let original_difficulties: Vec<f64> = questions.iter().map(|q| q.difficulty).collect();
        for &diff in &difficulties {
            assert!(original_difficulties.contains(&diff));
        }
    }

    #[test]
    fn test_select_questions_by_difficulty_tiers_exact_count() {
        let engine = RecommendationEngine::default();
        let questions = create_test_questions(10, 1);

        let answered_set = HashSet::new();
        let result = engine.filter_questions_by_difficulty_in_memory(&questions, 2.0, 20.0, 7, &answered_set);

        assert_eq!(result.len(), 7);
    }

    #[test]
    fn test_select_questions_by_difficulty_tiers_more_than_available() {
        let engine = RecommendationEngine::default();
        let questions = create_test_questions(3, 1);

        let answered_set = HashSet::new();
        let result = engine.filter_questions_by_difficulty_in_memory(&questions, 2.0, 6.0, 10, &answered_set);

        assert_eq!(result.len(), 3); // 只能返回可用的数量
    }

    // 异步测试需要tokio运行时
    #[tokio::test]
    async fn test_recommend_full_flow() {
        let engine = RecommendationEngine::default();
        let knowledge_points = create_test_knowledge_points();
        let request = create_test_request();
        let answered_ids = vec![1, 2, 3];
        let last_knowledge_code = "1.1.5";

        // 创建Mock Provider
        let mut question_provider = MockTestQuestionProvider::new();
        let mut ability_provider = MockTestAbilityProvider::new();

        // 设置Mock期望
        question_provider
            .expect_get_questions_for_knowledge_point()
            .returning(
                |knowledge_id, _difficulty_range, _limit, _exclude_ids, _status| {
                    Ok(create_test_questions(3, knowledge_id))
                },
            );

        ability_provider
            .expect_get_user_ability()
            .returning(|_user_id, _knowledge_id| Ok(1500.0));

        ability_provider
            .expect_get_knowledge_k_factor()
            .returning(|_user_id, _knowledge_id| Ok(0.8));

        // 调用推荐方法
        let result = engine
            .recommend(
                &request,
                &knowledge_points,
                last_knowledge_code,
                &answered_ids,
                &question_provider,
                &ability_provider,
            )
            .await;

        // 验证结果
        assert!(result.is_ok());
        let recommendation_result = result.unwrap();
        assert!(!recommendation_result.questions.is_empty());
        assert!(!recommendation_result.knowledge_allocations.is_empty());
    }

    #[tokio::test]
    async fn test_recommend_empty_knowledge_points() {
        let engine = RecommendationEngine::default();
        let knowledge_points = vec![];
        let request = create_test_request();
        let answered_ids = vec![];
        let last_knowledge_code = "1.1.5";

        let question_provider = MockTestQuestionProvider::new();
        let ability_provider = MockTestAbilityProvider::new();

        let result = engine
            .recommend(
                &request,
                &knowledge_points,
                last_knowledge_code,
                &answered_ids,
                &question_provider,
                &ability_provider,
            )
            .await;

        assert!(result.is_ok());
        let recommendation_result = result.unwrap();
        assert!(recommendation_result.questions.is_empty());
        assert!(recommendation_result.knowledge_allocations.is_empty());
    }

    #[tokio::test]
    async fn test_get_questions_with_expanding_difficulty() {
        let engine = RecommendationEngine::default();
        let mut question_provider = MockTestQuestionProvider::new();

        // 设置Mock期望：返回在指定难度范围内的题目
        question_provider
            .expect_get_questions_for_knowledge_point()
            .returning(|knowledge_id, difficulty_range, _, _, _| {
                let (min_diff, max_diff) = difficulty_range;
                // 总是返回在范围内的题目
                Ok(vec![
                    QuestionMetadata {
                        id: format!("q{}_1", knowledge_id),
                        subject_id: 1,
                        knowledge_id,
                        knowledge_name: Some(format!("知识点{}", knowledge_id)),
                        type_id: 1,
                        difficulty: min_diff + 0.5,
                        elo_rating: Some(min_diff + 0.5), // 确保在范围内
                        correct_ratio: Some(0.7),
                    },
                    QuestionMetadata {
                        id: format!("q{}_2", knowledge_id),
                        subject_id: 1,
                        knowledge_id,
                        knowledge_name: Some(format!("知识点{}", knowledge_id)),
                        type_id: 1,
                        difficulty: max_diff - 0.5,
                        elo_rating: Some(max_diff - 0.5), // 确保在范围内
                        correct_ratio: Some(0.6),
                    },
                ])
            });

        let result = engine
            .get_questions_with_expanding_difficulty(
                1,
                2,
                1000.0,
                2000.0,
                &[],
                Some(5),
                &question_provider,
            )
            .await;

        assert!(result.is_ok());
        let questions = result.unwrap();
        // 应该返回题目
        assert_eq!(questions.len(), 2);
    }

    #[tokio::test]
    async fn test_get_questions_with_expanding_difficulty_max_expansion() {
        let engine = RecommendationEngine::default();
        let mut question_provider = MockTestQuestionProvider::new();

        // 所有调用都返回空，测试最大扩展次数
        // 注意：实际的扩展次数可能会超过5次，因为算法会继续尝试
        question_provider
            .expect_get_questions_for_knowledge_point()
            .returning(|_, _, _, _, _| Ok(vec![])); // 不限制调用次数

        let result = engine
            .get_questions_with_expanding_difficulty(
                1,
                3,
                2.0,
                6.0,
                &[],
                Some(5),
                &question_provider,
            )
            .await;

        assert!(result.is_ok());
        let questions = result.unwrap();
        assert!(questions.is_empty());
    }

    #[tokio::test]
    async fn test_get_supplementary_questions() {
        let engine = RecommendationEngine::default();
        let mut question_provider = MockTestQuestionProvider::new();

        let knowledge_points = create_test_knowledge_points();
        let answered_ids = vec![1, 2];
        let mut ability_provider = MockTestAbilityProvider::new();

        question_provider
            .expect_get_questions_for_knowledge_point()
            .returning(|knowledge_id, _, _, _, _| Ok(create_test_questions(2, knowledge_id)));

        ability_provider
            .expect_get_user_ability()
            .returning(|_user_id, _knowledge_id| Ok(1500.0));

        ability_provider
            .expect_get_knowledge_k_factor()
            .returning(|_user_id, _knowledge_id| Ok(0.8));

        // 模拟已使用的知识点（假设知识点1已被使用）
        let mut used_knowledge_point_ids = std::collections::HashSet::new();
        used_knowledge_point_ids.insert(1);

        let existing_question_ids: Vec<String> = vec![];

        let result = engine
            .get_supplementary_questions(
                &knowledge_points,
                &used_knowledge_point_ids,
                3,
                &answered_ids,
                &existing_question_ids,
                Some(5),
                &question_provider,
                &ability_provider,
                "test_user",
            )
            .await;

        assert!(result.is_ok());
        let questions = result.unwrap();
        assert!(!questions.is_empty());
        // 应该从未使用的知识点获取题目
        assert!(questions.iter().any(|q| q.knowledge_id != 1));
    }

    // 测试边界条件
    #[test]
    fn test_select_knowledge_points_random_empty_points() {
        let engine = RecommendationEngine::default();

        let result = engine.select_knowledge_points_random(&[], &[], 5);

        assert!(result.is_empty());
    }

    #[test]
    fn test_select_knowledge_points_random_single_point() {
        let engine = RecommendationEngine::default();
        let knowledge_points = vec![create_test_knowledge_points()[0].clone()];

        let result = engine.select_knowledge_points_random(&knowledge_points, &[], 5);

        assert_eq!(result.len(), 1);
        assert_eq!(result.values().sum::<usize>(), 5);
    }

    #[test]
    fn test_select_knowledge_points_random_multiple_points() {
        let engine = RecommendationEngine::default();
        let knowledge_points = create_test_knowledge_points();

        let result = engine.select_knowledge_points_random(&knowledge_points, &[], 7);

        assert!(!result.is_empty());
        assert!(result.len() <= 3); // 最多3个知识点
        assert_eq!(result.values().sum::<usize>(), 7); // 总题目数应该等于请求数
    }

    #[test]
    fn test_select_knowledge_points_random_limit_exceeded() {
        let engine = RecommendationEngine::default();
        let knowledge_points = create_test_knowledge_points();

        let result = engine.select_knowledge_points_random(&knowledge_points, &[], 100);

        assert!(!result.is_empty());
        assert!(result.len() <= 3); // 最多3个知识点
        assert_eq!(result.values().sum::<usize>(), 100);
    }

    #[test]
    fn test_select_knowledge_points_random_5_questions_new_logic() {
        let engine = RecommendationEngine::default();

        // 创建4个新知识点，模拟您的场景
        let knowledge_points = vec![
            KnowledgePoint {
                id: 1,
                name: "Equivalence preserving operations".to_string(),
                code: "1001-01-01-01".to_string(),
                is_new: true,
            },
            KnowledgePoint {
                id: 117,
                name: "Solve linear equations with intergers".to_string(),
                code: "1001-01-01-02".to_string(),
                is_new: true,
            },
            KnowledgePoint {
                id: 6,
                name: "Number of solutions to a linear equation".to_string(),
                code: "1001-01-01-03".to_string(),
                is_new: true,
            },
            KnowledgePoint {
                id: 4,
                name: "Solve linear equations with variables on both side".to_string(),
                code: "1001-01-01-04".to_string(),
                is_new: true,
            },
        ];

        let result = engine.select_knowledge_points_random(&knowledge_points, &[], 5);

        println!("V2版本新逻辑分配结果:");
        for (kp, count) in &result {
            println!("知识点{}: {}题", kp.id, count);
        }

        // 验证用户期望的逻辑
        assert!(!result.is_empty());
        assert!(result.len() <= 2); // 最多2个新知识点
        assert_eq!(result.values().sum::<usize>(), 5); // 总共5题

        // 验证是否按code排序选择前2个知识点
        let selected_ids: Vec<i32> = result.keys().map(|kp| kp.id).collect();
        assert!(selected_ids.contains(&1)); // 应该包含知识点1
        assert!(selected_ids.contains(&117)); // 应该包含知识点117
        assert!(!selected_ids.contains(&6)); // 不应该包含知识点6（超过2个限制）
        assert!(!selected_ids.contains(&4)); // 不应该包含知识点4（超过2个限制）

        // 验证每个知识点的题目数量
        for (_, count) in &result {
            assert!(*count >= 2); // 每个知识点至少2题
        }
    }

    #[test]
    fn test_select_knowledge_points_with_learned_points() {
        let engine = RecommendationEngine::default();

        // 创建2个新知识点和2个已学知识点
        let new_points = vec![
            KnowledgePoint {
                id: 1,
                name: "新知识点1".to_string(),
                code: "1001-01-01-01".to_string(),
                is_new: true,
            },
            KnowledgePoint {
                id: 2,
                name: "新知识点2".to_string(),
                code: "1001-01-01-02".to_string(),
                is_new: true,
            },
        ];

        let learned_points = vec![
            KnowledgePoint {
                id: 3,
                name: "已学知识点1".to_string(),
                code: "1001-01-01-03".to_string(),
                is_new: false,
            },
            KnowledgePoint {
                id: 4,
                name: "已学知识点2".to_string(),
                code: "1001-01-01-04".to_string(),
                is_new: false,
            },
        ];

        let result = engine.select_knowledge_points_random(&new_points, &learned_points, 7);

        println!("有已学知识点的分配结果:");
        for (kp, count) in &result {
            println!("知识点{}: {}题", kp.id, count);
        }

        // 验证逻辑
        assert!(!result.is_empty());
        assert_eq!(result.values().sum::<usize>(), 7); // 总共7题

        // 应该包含新知识点
        let selected_ids: Vec<i32> = result.keys().map(|kp| kp.id).collect();
        assert!(selected_ids.contains(&1)); // 应该包含新知识点1
        assert!(selected_ids.contains(&2)); // 应该包含新知识点2

        // 应该包含一些已学知识点
        let has_learned = selected_ids.contains(&3) || selected_ids.contains(&4);
        assert!(has_learned); // 应该包含至少一个已学知识点
    }

    #[test]
    fn test_select_knowledge_points_edge_cases() {
        let engine = RecommendationEngine::default();

        // 测试1：只有1道题
        let knowledge_points = vec![KnowledgePoint {
            id: 1,
            name: "知识点1".to_string(),
            code: "1001-01-01-01".to_string(),
            is_new: true,
        }];

        let result = engine.select_knowledge_points_random(&knowledge_points, &[], 1);
        assert_eq!(result.len(), 1);
        assert_eq!(result.values().sum::<usize>(), 1);

        // 测试2：只有2道题
        let result = engine.select_knowledge_points_random(&knowledge_points, &[], 2);
        assert_eq!(result.len(), 1);
        assert_eq!(result.values().sum::<usize>(), 2);

        // 测试3：只有3道题
        let result = engine.select_knowledge_points_random(&knowledge_points, &[], 3);
        assert_eq!(result.len(), 1);
        assert_eq!(result.values().sum::<usize>(), 3);

        // 测试4：空知识点列表
        let result = engine.select_knowledge_points_random(&[], &[], 5);
        assert!(result.is_empty());
    }

    #[test]
    fn test_select_knowledge_points_large_numbers() {
        let engine = RecommendationEngine::default();

        // 创建多个新知识点
        let knowledge_points = vec![
            KnowledgePoint {
                id: 1,
                name: "知识点1".to_string(),
                code: "1001-01-01-01".to_string(),
                is_new: true,
            },
            KnowledgePoint {
                id: 2,
                name: "知识点2".to_string(),
                code: "1001-01-01-02".to_string(),
                is_new: true,
            },
            KnowledgePoint {
                id: 3,
                name: "知识点3".to_string(),
                code: "1001-01-01-03".to_string(),
                is_new: true,
            },
        ];

        // 测试10道题
        let result = engine.select_knowledge_points_random(&knowledge_points, &[], 10);
        println!("10道题分配结果:");
        for (kp, count) in &result {
            println!("知识点{}: {}题", kp.id, count);
        }

        assert!(result.len() <= 2); // 最多2个新知识点
        assert_eq!(result.values().sum::<usize>(), 10);

        // 验证每个知识点至少2题
        for (_, count) in &result {
            assert!(*count >= 2);
        }
    }

    #[test]
    fn test_select_knowledge_points_only_learned() {
        let engine = RecommendationEngine::default();

        // 只有已学知识点，没有新知识点
        let learned_points = vec![
            KnowledgePoint {
                id: 1,
                name: "已学知识点1".to_string(),
                code: "1001-01-01-01".to_string(),
                is_new: false,
            },
            KnowledgePoint {
                id: 2,
                name: "已学知识点2".to_string(),
                code: "1001-01-01-02".to_string(),
                is_new: false,
            },
        ];

        let result = engine.select_knowledge_points_random(&[], &learned_points, 5);

        println!("只有已学知识点的分配结果:");
        for (kp, count) in &result {
            println!("知识点{}: {}题", kp.id, count);
        }

        assert!(!result.is_empty());
        assert_eq!(result.values().sum::<usize>(), 5);

        // 验证选择的都是已学知识点
        for (kp, _) in &result {
            assert!(learned_points.iter().any(|lp| lp.id == kp.id));
        }
    }

    // 测试难度分层的边界情况
    #[test]
    fn test_select_questions_by_difficulty_tiers_same_difficulty() {
        let engine = RecommendationEngine::default();

        // 所有题目都是相同难度
        let questions = vec![
            QuestionMetadata {
                id: "q1".to_string(),
                subject_id: 1,
                knowledge_id: 1,
                knowledge_name: Some("知识点1".to_string()),
                type_id: 1,
                difficulty: 5.0,
                elo_rating: Some(1500.0),
                correct_ratio: Some(0.8),
            },
            QuestionMetadata {
                id: "q2".to_string(),
                subject_id: 1,
                knowledge_id: 1,
                knowledge_name: Some("知识点1".to_string()),
                type_id: 1,
                difficulty: 5.0,
                elo_rating: Some(1600.0),
                correct_ratio: Some(0.7),
            },
        ];

        let answered_set = HashSet::new();
        let result = engine.filter_questions_by_difficulty_in_memory(&questions, 2.0, 8.0, 2, &answered_set);

        assert_eq!(result.len(), 2);
    }

    #[test]
    fn test_select_questions_by_difficulty_tiers_narrow_range() {
        let engine = RecommendationEngine::default();
        let questions = create_test_questions(5, 1); // 难度为 2.0, 4.0, 6.0, 8.0, 10.0

        // 非常窄的难度范围，只包含4.0这一个题目
        let answered_set = HashSet::new();
        let result = engine.filter_questions_by_difficulty_in_memory(&questions, 3.9, 4.1, 3, &answered_set);

        // 由于算法有补充逻辑，当分层选择的题目不够时，会从剩余题目中补充
        // 所以结果可能包含范围外的题目
        assert!(result.len() <= 3);

        // 验证至少有一个题目在指定范围内（如果有的话）
        let in_range_count = result
            .iter()
            .filter(|q| q.elo_rating.unwrap_or(0.0) >= 3.9 && q.elo_rating.unwrap_or(0.0) <= 4.1)
            .count();

        // 如果有题目在范围内，应该至少有1个
        if in_range_count > 0 {
            assert!(in_range_count >= 1);
        }

        // 由于有补充逻辑，总题目数应该尽量满足请求的数量
        assert!(result.len() >= 1); // 至少应该有一些题目
    }

    #[test]
    fn test_select_questions_by_difficulty_tiers_inverted_range() {
        let engine = RecommendationEngine::default();
        let questions = create_test_questions(3, 1);

        // 颠倒的难度范围（max < min）
        let answered_set = HashSet::new();
        let result = engine.filter_questions_by_difficulty_in_memory(&questions, 9.0, 1.0, 2, &answered_set);

        // 应该能够处理这种情况（可能返回空或处理为正常范围）
        assert!(result.len() <= 2);
    }

    // 测试知识点分组的边界情况
    #[test]
    fn test_group_knowledge_points_boundary_code() {
        let engine = RecommendationEngine::default();
        let knowledge_points = vec![KnowledgePoint {
            id: 1,
            name: "边界知识点".to_string(),
            code: "1.1.5".to_string(), // 正好等于last_knowledge_code
            is_new: false,
        }];

        let groups = engine.group_knowledge_points(&knowledge_points, "1.1.5");

        // code等于last_knowledge_code的应该被归类为learned
        assert_eq!(groups.new_points.len(), 0);
        assert_eq!(groups.learned_points.len(), 1);
    }

    #[test]
    fn test_group_knowledge_points_mixed_order() {
        let engine = RecommendationEngine::default();
        let knowledge_points = vec![
            KnowledgePoint {
                id: 3,
                name: "高级知识点".to_string(),
                code: "2.1.1".to_string(),
                is_new: true,
            },
            KnowledgePoint {
                id: 1,
                name: "基础知识点".to_string(),
                code: "1.1.1".to_string(),
                is_new: false,
            },
            KnowledgePoint {
                id: 2,
                name: "中级知识点".to_string(),
                code: "1.5.1".to_string(),
                is_new: true,
            },
        ];

        let groups = engine.group_knowledge_points(&knowledge_points, "1.3.0");

        // 验证分组正确性
        assert_eq!(groups.new_points.len(), 2); // 1.5.1, 2.1.1 > 1.3.0
        assert_eq!(groups.learned_points.len(), 1); // 1.1.1 < 1.3.0
        assert_eq!(groups.all_points.len(), 3);
    }
}

/// 快照推荐仓储接口
///
/// 专门用于支持数据一致性快照的推荐仓储接口
/// 基础设施层需要实现这个接口
#[async_trait]
pub trait SnapshotRecommendationRepository: Send + Sync {
    /// 获取用户已答题目ID列表
    async fn get_answered_question_ids(&self, student_id: &str) -> Result<Vec<i32>>;

    /// 获取用户已答题目ID列表（带版本号，用于检测数据变更）
    ///
    /// 返回 (已答题目ID列表, 版本号)
    /// 版本号可以是时间戳、序列号或其他能够检测数据变更的标识
    async fn get_answered_question_ids_with_version(&self, student_id: &str) -> Result<(Vec<i32>, u64)>;
}

/// 容错策略枚举
#[derive(Debug, Clone, PartialEq)]
pub enum FallbackStrategy {
    /// 忽略已答题目限制，重新推荐
    IgnoreAnsweredQuestions,
    /// 扩大难度范围
    ExpandDifficultyRange,
    /// 降低推荐数量要求
    ReduceQuestionCount,
    /// 使用默认题目
    UseDefaultQuestions,
    /// 跨知识点推荐
    CrossKnowledgeRecommendation,
}

/// 容错配置
#[derive(Debug, Clone)]
pub struct FallbackConfig {
    /// 启用的容错策略（按优先级排序）
    pub strategies: Vec<FallbackStrategy>,
    /// 最大重试次数
    pub max_retries: usize,
    /// 最小推荐题目数量
    pub min_question_count: usize,
    /// 难度范围扩展倍数
    pub difficulty_expansion_factor: f64,
    /// 是否启用跨知识点推荐
    pub enable_cross_knowledge: bool,
}

impl Default for FallbackConfig {
    fn default() -> Self {
        Self {
            strategies: vec![
                FallbackStrategy::ExpandDifficultyRange,
                FallbackStrategy::ReduceQuestionCount,
                FallbackStrategy::IgnoreAnsweredQuestions,
                FallbackStrategy::CrossKnowledgeRecommendation,
                FallbackStrategy::UseDefaultQuestions,
            ],
            max_retries: 3,
            min_question_count: 1,
            difficulty_expansion_factor: 1.5,
            enable_cross_knowledge: true,
        }
    }
}

impl RecommendationEngine {
    /// 容错推荐方法
    ///
    /// 当标准推荐失败或题目不足时，使用多级降级策略确保推荐成功
    pub async fn recommend_with_fallback(
        &self,
        request: &RecommendationRequest,
        knowledge_points: &[KnowledgePoint],
        last_knowledge_code: &str,
        answered_question_ids: &[i32],
        question_provider: &dyn QuestionProvider,
        ability_provider: &dyn AbilityProvider,
    ) -> Result<RecommendationResult> {
        let mut current_request = request.clone();
        let mut current_answered_ids = answered_question_ids.to_vec();
        let mut retry_count = 0;

        tracing::info!(
            "Starting fallback recommendation for student {} with {} strategies",
            request.student_id,
            self.fallback_config.strategies.len()
        );

        // 首先尝试标准推荐
        match self.recommend(
            &current_request,
            knowledge_points,
            last_knowledge_code,
            &current_answered_ids,
            question_provider,
            ability_provider,
        ).await {
            Ok(result) if self.is_result_satisfactory(&result, &current_request) => {
                tracing::info!(
                    "Standard recommendation succeeded for student {}",
                    request.student_id
                );
                return Ok(result);
            }
            Ok(partial_result) => {
                tracing::warn!(
                    "Standard recommendation returned insufficient results for student {}: got {} questions, needed {}",
                    request.student_id,
                    partial_result.questions.len(),
                    current_request.limit
                );
                // 继续尝试容错策略
            }
            Err(e) => {
                tracing::warn!(
                    "Standard recommendation failed for student {}: {}",
                    request.student_id,
                    e
                );
                // 继续尝试容错策略
            }
        }

        // 依次尝试容错策略
        for strategy in &self.fallback_config.strategies {
            if retry_count >= self.fallback_config.max_retries {
                break;
            }

            tracing::info!(
                "Trying fallback strategy {:?} for student {} (attempt {})",
                strategy,
                request.student_id,
                retry_count + 1
            );

            match self.apply_fallback_strategy(
                strategy,
                &mut current_request,
                &mut current_answered_ids,
                knowledge_points,
                last_knowledge_code,
                question_provider,
                ability_provider,
            ).await {
                Ok(result) if self.is_result_satisfactory(&result, &current_request) => {
                    tracing::info!(
                        "Fallback strategy {:?} succeeded for student {}",
                        strategy,
                        request.student_id
                    );
                    return Ok(result);
                }
                Ok(partial_result) => {
                    tracing::warn!(
                        "Fallback strategy {:?} returned insufficient results for student {}: got {} questions",
                        strategy,
                        request.student_id,
                        partial_result.questions.len()
                    );
                    // 继续尝试下一个策略
                }
                Err(e) => {
                    tracing::warn!(
                        "Fallback strategy {:?} failed for student {}: {}",
                        strategy,
                        request.student_id,
                        e
                    );
                    // 继续尝试下一个策略
                }
            }

            retry_count += 1;
        }

        // 如果所有策略都失败，返回最后的尝试结果或错误
        tracing::error!(
            "All fallback strategies failed for student {}",
            request.student_id
        );

        Err(crate::Error::service(format!(
            "Unable to generate recommendations for student {} after {} attempts",
            request.student_id,
            retry_count
        )))
    }

    /// 应用特定的容错策略
    async fn apply_fallback_strategy(
        &self,
        strategy: &FallbackStrategy,
        request: &mut RecommendationRequest,
        answered_question_ids: &mut Vec<i32>,
        knowledge_points: &[KnowledgePoint],
        last_knowledge_code: &str,
        question_provider: &dyn QuestionProvider,
        ability_provider: &dyn AbilityProvider,
    ) -> Result<RecommendationResult> {
        match strategy {
            FallbackStrategy::IgnoreAnsweredQuestions => {
                tracing::info!("Applying IgnoreAnsweredQuestions strategy");
                answered_question_ids.clear();
                self.recommend(
                    request,
                    knowledge_points,
                    last_knowledge_code,
                    answered_question_ids,
                    question_provider,
                    ability_provider,
                ).await
            }

            FallbackStrategy::ExpandDifficultyRange => {
                tracing::info!("Applying ExpandDifficultyRange strategy");
                // 这个策略在get_questions_with_expanding_difficulty中已经实现
                self.recommend(
                    request,
                    knowledge_points,
                    last_knowledge_code,
                    answered_question_ids,
                    question_provider,
                    ability_provider,
                ).await
            }

            FallbackStrategy::ReduceQuestionCount => {
                tracing::info!("Applying ReduceQuestionCount strategy");
                let original_limit = request.limit;
                request.limit = std::cmp::max(
                    self.fallback_config.min_question_count,
                    request.limit / 2
                );

                let result = self.recommend(
                    request,
                    knowledge_points,
                    last_knowledge_code,
                    answered_question_ids,
                    question_provider,
                    ability_provider,
                ).await;

                // 恢复原始限制
                request.limit = original_limit;
                result
            }

            FallbackStrategy::UseDefaultQuestions => {
                tracing::info!("Applying UseDefaultQuestions strategy");
                self.get_default_questions(
                    request,
                    knowledge_points,
                    question_provider,
                ).await
            }

            FallbackStrategy::CrossKnowledgeRecommendation => {
                tracing::info!("Applying CrossKnowledgeRecommendation strategy");
                if self.fallback_config.enable_cross_knowledge {
                    self.recommend_across_knowledge_points(
                        request,
                        knowledge_points,
                        answered_question_ids,
                        question_provider,
                        ability_provider,
                    ).await
                } else {
                    Err(crate::Error::service("Cross-knowledge recommendation disabled".to_string()))
                }
            }
        }
    }

    /// 检查推荐结果是否满意
    fn is_result_satisfactory(&self, result: &RecommendationResult, request: &RecommendationRequest) -> bool {
        let min_questions = std::cmp::max(
            self.fallback_config.min_question_count,
            request.limit / 2
        );

        result.questions.len() >= min_questions
    }

    /// 获取默认题目（最后的容错策略）
    async fn get_default_questions(
        &self,
        request: &RecommendationRequest,
        knowledge_points: &[KnowledgePoint],
        question_provider: &dyn QuestionProvider,
    ) -> Result<RecommendationResult> {
        tracing::info!("Getting default questions for student {}", request.student_id);

        let mut all_questions = Vec::new();
        let mut knowledge_allocations = Vec::new();
        let mut actual_question_counts = std::collections::HashMap::new();

        // 为每个知识点获取一些默认题目（不考虑难度和已答限制）
        for kp in knowledge_points.iter().take(2) { // 限制为前2个知识点
            let questions = question_provider
                .get_questions_for_knowledge_point(
                    kp.id,
                    (0.0, 10000.0), // 极宽的难度范围
                    request.limit / 2, // 每个知识点分配一半题目
                    &[], // 不排除任何题目
                    request.status,
                )
                .await
                .unwrap_or_default();

            let question_count = questions.len();
            all_questions.extend(questions);
            actual_question_counts.insert(kp.id, question_count);

            knowledge_allocations.push(KnowledgeAllocation {
                knowledge_id: kp.id,
                knowledge_code: kp.code.clone(),
                knowledge_name: kp.name.clone(),
                question_count,
                is_new: kp.is_new,
            });
        }

        Ok(RecommendationResult {
            questions: all_questions,
            knowledge_allocations,
            actual_question_counts,
        })
    }

    /// 跨知识点推荐（当单个知识点题目不足时）
    async fn recommend_across_knowledge_points(
        &self,
        request: &RecommendationRequest,
        knowledge_points: &[KnowledgePoint],
        answered_question_ids: &[i32],
        question_provider: &dyn QuestionProvider,
        _ability_provider: &dyn AbilityProvider,
    ) -> Result<RecommendationResult> {
        tracing::info!("Recommending across knowledge points for student {}", request.student_id);

        let mut all_questions = Vec::new();
        let mut knowledge_allocations = Vec::new();
        let mut actual_question_counts = std::collections::HashMap::new();
        let questions_per_kp = std::cmp::max(1, request.limit / knowledge_points.len());

        // 为所有知识点平均分配题目（跨知识点推荐：不考虑难度限制）
        for kp in knowledge_points {
            let questions = question_provider
                .get_questions_for_knowledge_point(
                    kp.id,
                    (900.0, 2500.0), // 使用极宽的难度范围，覆盖所有题目
                    questions_per_kp,
                    answered_question_ids,
                    request.status,
                )
                .await
                .unwrap_or_default();

            let question_count = questions.len();
            all_questions.extend(questions);
            actual_question_counts.insert(kp.id, question_count);

            knowledge_allocations.push(KnowledgeAllocation {
                knowledge_id: kp.id,
                knowledge_code: kp.code.clone(),
                knowledge_name: kp.name.clone(),
                question_count,
                is_new: kp.is_new,
            });
        }

        Ok(RecommendationResult {
            questions: all_questions,
            knowledge_allocations,
            actual_question_counts,
        })
    }
}
