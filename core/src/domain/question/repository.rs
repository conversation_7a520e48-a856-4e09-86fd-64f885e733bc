//! 问题仓储接口
//!
//! 定义问题相关的数据访问契约，遵循DDD架构原则

use async_trait::async_trait;
use std::collections::HashMap;

use super::entity::{Question, QuestionBank, QuestionBankSession};
use super::value_objects::{QuestionBankConfig, Subject};
use crate::error::Result;

/// 问题仓储接口
///
/// 定义问题领域的数据访问契约，由基础设施层实现
#[async_trait]
pub trait QuestionRepository: Send + Sync {
    /// 根据ID查找问题
    ///
    /// # 参数
    /// * `id` - 问题ID
    ///
    /// # 返回
    /// 返回查询到的问题，如不存在返回None
    async fn find_by_id(&self, id: &str) -> Result<Option<Question>>;

    /// 根据多个ID批量查找问题
    ///
    /// # 参数
    /// * `ids` - 问题ID列表
    ///
    /// # 返回
    /// 返回查询到的问题列表
    async fn find_by_ids(&self, ids: &[String]) -> Result<Vec<Question>>;

    /// 根据知识点ID查找问题
    ///
    /// # 参数
    /// * `knowledge_id` - 知识点ID
    ///
    /// # 返回
    /// 返回该知识点下的所有问题
    async fn find_by_knowledge(&self, knowledge_id: i32) -> Result<Vec<Question>>;

    /// 根据知识点ID和难度范围查找问题
    ///
    /// # 参数
    /// * `knowledge_id` - 知识点ID
    /// * `min_difficulty` - 最小难度
    /// * `max_difficulty` - 最大难度
    /// * `status` - 可选的题目状态过滤
    ///
    /// # 返回
    /// 返回符合条件的问题列表
    async fn find_by_knowledge_and_difficulty(
        &self,
        knowledge_id: i32,
        min_difficulty: f64,
        max_difficulty: f64,
        status: Option<i32>,
    ) -> Result<Vec<Question>>;

    /// 根据多个知识点ID查找问题
    ///
    /// # 参数
    /// * `knowledge_ids` - 知识点ID列表
    ///
    /// # 返回
    /// 返回这些知识点下的所有问题
    async fn find_by_knowledge_ids(&self, knowledge_ids: &[i32]) -> Result<Vec<Question>>;

    /// 根据学科ID查找问题
    ///
    /// # 参数
    /// * `subject_id` - 学科ID
    ///
    /// # 返回
    /// 返回该学科下的所有问题
    async fn find_by_subject(&self, subject_id: i32) -> Result<Vec<Question>>;

    /// 根据多个小节ID查找问题
    ///
    /// # 参数
    /// * `section_ids` - 小节ID列表
    ///
    /// # 返回
    /// 返回这些小节下的所有问题
    async fn find_by_sections(&self, section_ids: &[i32]) -> Result<Vec<Question>>;

    /// 检查问题是否属于指定章节
    ///
    /// # 参数
    /// * `question_id` - 问题ID
    /// * `chapter_id` - 章节ID
    ///
    /// # 返回
    /// 如果问题属于该章节返回true，否则返回false
    async fn is_in_chapter(&self, question_id: &str, chapter_id: i32) -> Result<bool>;

    /// 检查问题是否属于指定小节
    ///
    /// # 参数
    /// * `question_id` - 问题ID
    /// * `section_id` - 小节ID
    ///
    /// # 返回
    /// 如果问题属于该小节返回true，否则返回false
    async fn is_in_section(&self, question_id: &str, section_id: i32) -> Result<bool>;

    /// 获取问题元数据
    ///
    /// # 参数
    /// * `ids` - 问题ID列表
    ///
    /// # 返回
    /// 返回问题ID到元数据的映射
    async fn get_metadata(&self, ids: &[String]) -> Result<HashMap<String, QuestionMetadata>>;

    // 删除了不需要的题目增删改方法：
    // - async fn save(&self, question: &Question) -> Result<()>;
    // - async fn delete(&self, id: &str) -> Result<bool>;
}

/// 问题元数据
#[derive(Debug, Clone)]
pub struct QuestionMetadata {
    /// 问题ID
    pub id: String,
    /// 学科ID
    pub subject_id: i32,
    /// 知识点ID
    pub knowledge_id: i32,
    /// 知识点名称
    pub knowledge_name: Option<String>,
    /// 题型ID
    pub type_id: i32,
    /// 难度
    pub difficulty: f64,
    /// ELO评分
    pub elo_rating: Option<f64>,
    /// 正确率
    pub correct_ratio: Option<f64>,
}

/// 题库仓储接口
///
/// 定义题库相关的数据访问契约
#[async_trait]
pub trait QuestionBankRepository: Send + Sync {
    /// 保存题库
    async fn save(&self, question_bank: &QuestionBank) -> Result<()>;

    /// 根据ID获取题库
    async fn find_by_id(&self, id: &str) -> Result<Option<QuestionBank>>;

    /// 根据用户ID获取最近的题库
    async fn find_recent_by_user(&self, user_id: &str, limit: usize) -> Result<Vec<QuestionBank>>;

    /// 删除题库
    async fn delete(&self, id: &str) -> Result<bool>;
}

/// 题目提供者接口
///
/// 用于获取题目数据，解耦题库生成与具体的题目存储实现
#[async_trait]
pub trait QuestionProvider: Send + Sync {
    /// 根据小节ID获取题目
    ///
    /// # 参数
    /// * `section_id` - 小节ID
    /// * `config` - 题库配置
    ///
    /// # 返回
    /// 返回该小节下的题目列表
    async fn get_questions_by_section(
        &self,
        section_id: i32,
        config: &QuestionBankConfig,
    ) -> Result<Vec<Question>>;

    /// 批量获取多个小节的题目
    ///
    /// # 参数
    /// * `section_ids` - 小节ID列表
    /// * `config` - 题库配置
    ///
    /// # 返回
    /// 返回这些小节下的题目列表
    async fn get_questions_by_sections(
        &self,
        section_ids: &[i32],
        config: &QuestionBankConfig,
    ) -> Result<Vec<Question>>;

    /// 根据学科获取题目
    ///
    /// # 参数
    /// * `subject` - 学科
    /// * `config` - 题库配置
    ///
    /// # 返回
    /// 返回该学科下的题目列表
    async fn get_questions_by_subject(
        &self,
        subject: &Subject,
        config: &QuestionBankConfig,
    ) -> Result<Vec<Question>>;
}

/// 用户上下文提供者接口
///
/// 用于获取用户相关信息，支持个性化题库生成
#[async_trait]
pub trait UserContextProvider: Send + Sync {

    /// 获取用户已答题目ID列表
    async fn get_answered_question_ids(&self, user_id: &str) -> Result<Vec<i32>>;

    /// 获取用户能力水平
    async fn get_user_ability(&self, user_id: &str, subject: &Subject) -> Result<Option<f64>>;
}

/// SAT 组卷会话仓储接口
#[async_trait]
pub trait QuestionBankSessionRepository: Send + Sync {
    /// 保存会话
    async fn save(&self, session: &QuestionBankSession) -> Result<()>;

    /// 根据会话ID查找会话
    async fn find_by_id(&self, session_id: &str) -> Result<Option<QuestionBankSession>>;

    /// 根据用户ID和学科查找最新会话
    async fn find_latest_by_user_and_subject(&self, user_id: i64, subject: &str) -> Result<Option<QuestionBankSession>>;

    /// 根据用户ID查找所有科目的最新会话
    async fn find_latest_by_user_all_subjects(&self, user_id: i64) -> Result<Vec<QuestionBankSession>>;

    /// 删除过期会话
    async fn cleanup_expired(&self) -> Result<u64>;

    /// 检查会话是否存在
    async fn exists(&self, session_id: &str) -> Result<bool>;
}
