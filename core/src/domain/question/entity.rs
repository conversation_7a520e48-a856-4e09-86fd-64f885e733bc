use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use serde_json::Value;
use uuid::Uuid;

use super::value_objects::{GenerateType, QuestionCount, Subject, UserContext};
use crate::infrastructure::dto::question::QuestionContent;

/// 问题领域实体
///
/// 统一的问题实体定义，作为系统中所有问题相关操作的核心实体
/// 包含完整的问题信息和业务逻辑方法
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Question {
    /// 问题ID (统一使用字符串格式以支持不同的ID生成策略)
    pub id: String,
    /// 学科ID
    pub subject_id: i32,
    /// 知识点ID
    pub knowledge_id: i32,
    /// 题型ID
    pub type_id: i32,
    /// 难度 (使用 f64 以支持更精确的难度计算)
    pub difficulty: f64,
    /// 问题内容 (JSON格式以支持富文本内容)
    pub content: Value,
    /// 选项 (可选，用于选择题)
    pub options: Option<Value>,
    /// 答案 (JSON格式以支持复杂答案结构)
    pub answer: Value,
    /// 解析 (可选的解题说明)
    pub explanation: Option<Value>,
    /// ELO评分 (动态难度评分)
    pub elo_rating: f64,
    /// IRT难度参数 (可选)
    pub irt_difficulty: Option<f64>,
    /// IRT区分度参数 (可选)
    pub irt_discrimination: Option<f64>,
    /// IRT猜测参数 (可选)
    pub irt_guessing: Option<f64>,
    /// 使用次数
    pub usage_count: i32,
    /// 正确回答次数
    pub correct_count: i32,
    /// 小节ID (可选，用于SAT等分小节的考试)
    pub section_id: Option<i32>,
    /// 章节ID (可选)
    pub chapter_id: Option<i32>,
    /// 题目集合标识 (可选)
    pub question_set: Option<String>,
    /// 题目URL (可选)
    pub url: Option<String>,
    /// 状态 (用于题目的生命周期管理)
    pub status: Option<i32>,
    /// 是否激活
    pub is_active: bool,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

impl Question {
    /// 创建新问题实例 (完整构造函数)
    #[allow(clippy::too_many_arguments)]
    pub fn new(
        id: String,
        subject_id: i32,
        knowledge_id: i32,
        type_id: i32,
        difficulty: f64,
        content: Value,
        options: Option<Value>,
        answer: Value,
        explanation: Option<Value>,
        elo_rating: f64,
        is_active: bool,
        created_at: DateTime<Utc>,
        updated_at: DateTime<Utc>,
    ) -> Self {
        Self {
            id,
            subject_id,
            knowledge_id,
            type_id,
            difficulty,
            content,
            options,
            answer,
            explanation,
            elo_rating,
            irt_difficulty: None,
            irt_discrimination: None,
            irt_guessing: None,
            usage_count: 0,
            correct_count: 0,
            section_id: None,
            chapter_id: None,
            question_set: None,
            url: None,
            status: None,
            is_active,
            created_at,
            updated_at,
        }
    }

    /// 创建基础问题实例 (简化构造函数)
    pub fn new_basic(
        id: String,
        subject_id: i32,
        knowledge_id: i32,
        content: Value,
        answer: Value,
    ) -> Self {
        let now = Utc::now();
        Self {
            id,
            subject_id,
            knowledge_id,
            type_id: 0,
            difficulty: 1.0,
            content,
            options: None,
            answer,
            explanation: None,
            elo_rating: 1700.0, // 默认ELO评分
            irt_difficulty: None,
            irt_discrimination: None,
            irt_guessing: None,
            usage_count: 0,
            correct_count: 0,
            section_id: None,
            chapter_id: None,
            question_set: None,
            url: None,
            status: Some(1), // 默认状态为激活
            is_active: true,
            created_at: now,
            updated_at: now,
        }
    }

    /// 从完整数据创建问题实例 (用于从数据库加载)
    #[allow(clippy::too_many_arguments)]
    pub fn from_full_data(
        id: String,
        subject_id: i32,
        knowledge_id: i32,
        type_id: i32,
        difficulty: f64,
        content: Value,
        options: Option<Value>,
        answer: Value,
        explanation: Option<Value>,
        elo_rating: f64,
        irt_difficulty: Option<f64>,
        irt_discrimination: Option<f64>,
        irt_guessing: Option<f64>,
        usage_count: i32,
        correct_count: i32,
        section_id: Option<i32>,
        chapter_id: Option<i32>,
        question_set: Option<String>,
        url: Option<String>,
        status: Option<i32>,
        is_active: bool,
        created_at: DateTime<Utc>,
        updated_at: DateTime<Utc>,
    ) -> Self {
        Self {
            id,
            subject_id,
            knowledge_id,
            type_id,
            difficulty,
            content,
            options,
            answer,
            explanation,
            elo_rating,
            irt_difficulty,
            irt_discrimination,
            irt_guessing,
            usage_count,
            correct_count,
            section_id,
            chapter_id,
            question_set,
            url,
            status,
            is_active,
            created_at,
            updated_at,
        }
    }

    /// 检查问题是否属于指定学科
    pub fn belongs_to_subject(&self, subject: &Subject) -> bool {
        self.subject_id == subject.id()
    }



    /// 检查问题是否在指定ELO评分范围内
    pub fn is_in_elo_range(&self, min: f64, max: f64) -> bool {
        self.elo_rating >= min && self.elo_rating <= max
    }

    /// 获取正确率
    pub fn correct_ratio(&self) -> f64 {
        if self.usage_count == 0 {
            0.0
        } else {
            self.correct_count as f64 / self.usage_count as f64
        }
    }

    /// 更新使用统计
    pub fn update_usage_stats(&mut self, is_correct: bool) {
        self.usage_count += 1;
        if is_correct {
            self.correct_count += 1;
        }
        self.updated_at = Utc::now();
    }

    /// 更新ELO评分
    pub fn update_elo_rating(&mut self, new_rating: f64) {
        self.elo_rating = new_rating;
        self.updated_at = Utc::now();
    }

    /// 检查问题是否属于指定小节
    pub fn belongs_to_section(&self, section_id: i32) -> bool {
        self.section_id == Some(section_id)
    }

    /// 检查问题是否属于指定章节
    pub fn belongs_to_chapter(&self, chapter_id: i32) -> bool {
        self.chapter_id == Some(chapter_id)
    }

    /// 检查问题是否处于指定状态
    pub fn has_status(&self, status: i32) -> bool {
        self.status == Some(status)
    }

    /// 获取问题的数字ID (如果ID是数字格式)
    pub fn numeric_id(&self) -> Option<i32> {
        self.id.parse().ok()
    }
}

/// 题库实体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QuestionBank {
    /// 题库ID
    pub id: String,
    /// 题库名称
    pub name: String,
    /// 题库描述
    pub description: Option<String>,
    /// 小节ID列表
    pub section_ids: Vec<i32>,
    /// 题目列表
    pub questions: Vec<QuestionContent>,
    /// 实际题目数量
    pub actual_count: usize,
    /// 请求的题目数量
    pub requested_count: QuestionCount,
    /// 生成类型
    pub generate_type: GenerateType,
    /// 学科
    pub subject: Subject,
    /// 创建时间
    pub created_at: DateTime<Utc>,
}

impl QuestionBank {
    /// 创建新的题库
    pub fn new(
        name: String,
        section_ids: Vec<i32>,
        questions: Vec<QuestionContent>,
        requested_count: QuestionCount,
        generate_type: GenerateType,
        subject: Subject,
    ) -> Self {
        let actual_count = questions.len();

        Self {
            id: Uuid::new_v4().to_string(),
            name,
            description: None,
            section_ids,
            questions,
            actual_count,
            requested_count,
            generate_type,
            subject,
            created_at: Utc::now(),
        }
    }

    /// 设置描述
    pub fn with_description(mut self, description: String) -> Self {
        self.description = Some(description);
        self
    }

    /// 检查是否满足请求数量
    pub fn is_complete(&self) -> bool {
        self.actual_count >= self.requested_count.value()
    }

    /// 获取完成度百分比
    pub fn completion_percentage(&self) -> f64 {
        if self.requested_count.value() == 0 {
            return 100.0;
        }
        (self.actual_count as f64 / self.requested_count.value() as f64) * 100.0
    }



    /// 获取题目ID列表
    pub fn question_ids(&self) -> Vec<String> {
        self.questions.iter().map(|q| q.id.clone()).collect()
    }

    /// 获取题目ID列表（转换为i32）
    pub fn question_ids_as_i32(&self) -> Vec<i32> {
        self.questions.iter()
            .filter_map(|q| q.id.parse::<i32>().ok())
            .collect()
    }
}

/// 题库生成请求实体
#[derive(Debug, Clone, PartialEq)]
pub struct QuestionBankRequest {
    /// 小节ID列表
    pub section_ids: Vec<i32>,
    /// 生成类型
    pub generate_type: GenerateType,
    /// 题目数量
    pub question_count: QuestionCount,
    /// 学科
    pub subject: Subject,
    /// 用户上下文（个性化生成时使用）
    pub user_context: Option<UserContext>,
    /// 请求时间
    pub requested_at: DateTime<Utc>,
}

impl QuestionBankRequest {
    /// 创建新的题库请求
    pub fn new(
        section_ids: Vec<i32>,
        generate_type: GenerateType,
        question_count: QuestionCount,
        subject: Subject,
    ) -> Self {
        Self {
            section_ids,
            generate_type,
            question_count,
            subject,
            user_context: None,
            requested_at: Utc::now(),
        }
    }

    /// 设置用户上下文
    pub fn with_user_context(mut self, context: UserContext) -> Self {
        self.user_context = Some(context);
        self
    }



    /// 获取用户ID（如果是个性化请求）
    pub fn user_id(&self) -> Option<&str> {
        // 现在没有用户ID的概念，返回None
        None
    }

    /// 验证请求的有效性
    pub fn validate(&self) -> crate::error::Result<()> {
        use crate::error::Error;

        // 验证小节ID不为空
        if self.section_ids.is_empty() {
            return Err(Error::InvalidInput("section_ids不能为空".to_string()));
        }



        Ok(())
    }

    /// 生成题库名称
    pub fn generate_bank_name(&self) -> String {
        let type_str = match &self.generate_type {
            GenerateType::Random => "随机",
            GenerateType::Adaptive => "自适应",
        };

        let subject_str = match self.subject {
            Subject::Math => "数学",
            Subject::Reading => "阅读",
        };

        format!(
            "{}{}题库-{}-{}题",
            type_str,
            subject_str,
            self.requested_at.format("%Y%m%d"),
            self.question_count.value()
        )
    }
}

/// SAT 组卷会话实体
#[derive(Debug, Clone)]
pub struct QuestionBankSession {
    /// 会话ID
    session_id: String,
    /// 用户ID
    user_id: Option<i64>,
    /// 题目ID列表（按顺序）
    question_ids: Vec<i32>,
    /// 生成类型
    generate_type: GenerateType,
    /// 学科
    subject: Subject,
    /// 小节ID列表
    section_ids: Vec<i32>,
    /// 总题目数量
    total_count: i32,
    /// 创建时间
    created_at: DateTime<Utc>,
    /// 过期时间
    expires_at: DateTime<Utc>,
}

impl QuestionBankSession {
    /// 创建新的组卷会话
    pub fn new(
        user_id: Option<i64>,
        question_ids: Vec<i32>,
        generate_type: GenerateType,
        subject: Subject,
        section_ids: Vec<i32>,
    ) -> crate::error::Result<Self> {
        // 业务规则验证
        Self::validate_question_ids(&question_ids)?;
        Self::validate_section_ids(&section_ids)?;

        let now = Utc::now();
        let session_id = Uuid::new_v4().to_string();
        let total_count = question_ids.len() as i32;

        // 默认24小时后过期
        let expires_at = now + chrono::Duration::hours(24);

        Ok(Self {
            session_id,
            user_id,
            question_ids,
            generate_type,
            subject,
            section_ids,
            total_count,
            created_at: now,
            expires_at,
        })
    }

    /// 从现有数据重建会话（用于从数据库加载）
    pub fn from_existing(
        session_id: String,
        user_id: Option<i64>,
        question_ids: Vec<i32>,
        generate_type: GenerateType,
        subject: Subject,
        section_ids: Vec<i32>,
        total_count: i32,
        created_at: DateTime<Utc>,
        expires_at: DateTime<Utc>,
    ) -> Self {
        Self {
            session_id,
            user_id,
            question_ids,
            generate_type,
            subject,
            section_ids,
            total_count,
            created_at,
            expires_at,
        }
    }

    /// 获取前N道题目
    pub fn get_first_questions(&self, count: usize) -> Vec<i32> {
        self.question_ids.iter().take(count).cloned().collect()
    }

    /// 根据指定题目ID获取后续题目
    pub fn get_questions_after(&self, question_id: i32, count: usize) -> crate::error::Result<Vec<i32>> {
        use crate::error::Error;

        // 找到指定题目的位置
        let position = self.question_ids
            .iter()
            .position(|id| *id == question_id)
            .ok_or_else(|| Error::NotFound(format!("题目ID {} 在会话中不存在", question_id)))?;

        // 返回该位置之后的题目
        let start_index = position + 1;
        let questions = self.question_ids
            .iter()
            .skip(start_index)
            .take(count)
            .cloned()
            .collect();

        Ok(questions)
    }

    /// 检查会话是否已过期
    pub fn is_expired(&self) -> bool {
        Utc::now() > self.expires_at
    }

    /// 获取剩余题目数量
    pub fn remaining_questions_count(&self, current_question_id: i32) -> crate::error::Result<usize> {
        use crate::error::Error;

        let position = self.question_ids
            .iter()
            .position(|id| *id == current_question_id)
            .ok_or_else(|| Error::NotFound(format!("题目ID {} 在会话中不存在", current_question_id)))?;

        Ok(self.question_ids.len() - position - 1)
    }

    // Getters
    pub fn session_id(&self) -> &str {
        &self.session_id
    }

    pub fn user_id(&self) -> Option<i64> {
        self.user_id
    }

    pub fn question_ids(&self) -> &[i32] {
        &self.question_ids
    }

    pub fn generate_type(&self) -> &GenerateType {
        &self.generate_type
    }

    pub fn subject(&self) -> &Subject {
        &self.subject
    }

    pub fn section_ids(&self) -> &[i32] {
        &self.section_ids
    }

    pub fn total_count(&self) -> i32 {
        self.total_count
    }

    pub fn created_at(&self) -> DateTime<Utc> {
        self.created_at
    }

    pub fn expires_at(&self) -> DateTime<Utc> {
        self.expires_at
    }

    // 私有验证方法
    fn validate_question_ids(question_ids: &[i32]) -> crate::error::Result<()> {
        use crate::error::Error;

        if question_ids.is_empty() {
            return Err(Error::InvalidInput("题目ID列表不能为空".to_string()));
        }

        if question_ids.len() > 50 {
            return Err(Error::InvalidInput("题目数量不能超过50个".to_string()));
        }

        // 检查是否有重复的题目ID
        let mut unique_ids = std::collections::HashSet::new();
        for id in question_ids {
            if !unique_ids.insert(id) {
                return Err(Error::InvalidInput(format!("题目ID {} 重复", id)));
            }
        }

        Ok(())
    }

    fn validate_section_ids(section_ids: &[i32]) -> crate::error::Result<()> {
        use crate::error::Error;

        if section_ids.is_empty() {
            return Err(Error::InvalidInput("小节ID列表不能为空".to_string()));
        }

        if section_ids.len() > 50 {
            return Err(Error::InvalidInput("小节数量不能超过50个".to_string()));
        }

        Ok(())
    }
}
