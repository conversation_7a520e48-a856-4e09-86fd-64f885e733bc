//! 问题领域模块
//!
//! 定义问题相关的领域实体、值对象和仓储接口

pub mod entity;
pub mod repository;
pub mod service;
pub mod value_objects;
pub mod converter;

pub use entity::{Question, QuestionBank, QuestionBankRequest, QuestionBankSession};
pub use repository::{
    QuestionMetadata, QuestionRepository, QuestionBankRepository,
    QuestionBankSessionRepository, QuestionProvider, UserContextProvider
};
pub use service::QuestionDomainService;
pub use value_objects::{GenerateType, QuestionCount, Subject, QuestionBankConfig};
pub use converter::QuestionConverter;
