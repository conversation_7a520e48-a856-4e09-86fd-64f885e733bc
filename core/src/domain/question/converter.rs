//! 问题实体转换器
//!
//! 提供统一的转换逻辑，处理领域层 Question 实体与其他层 DTO 之间的转换
//! 避免在多个地方重复转换代码，确保转换逻辑的一致性

use chrono::Utc;
use serde_json::Value;

use super::entity::Question;
use crate::infrastructure::dto::question::QuestionContent;
use crate::storage::entities::question as QuestionModel;
use crate::error::{Error, Result};

/// 问题转换器
/// 
/// 统一处理 Question 实体与各种 DTO、模型之间的转换
pub struct QuestionConverter;

impl QuestionConverter {
    /// 创建新的转换器实例
    pub fn new() -> Self {
        Self
    }

    /// 从数据库模型转换为领域实体
    pub fn from_db_model(model: &QuestionModel::Model) -> Question {
        Question::from_full_data(
            model.question_id.to_string(),
            model.subject_id,
            model.knowledge_id,
            model.type_id,
            model.difficulty as f64,
            model.question_content.clone(),
            model.options.clone(),
            model.answer.clone(),
            model.explanation.clone(),
            model.elo_rating,
            model.irt_difficulty,
            model.irt_discrimination,
            model.irt_guessing,
            model.usage_count,
            model.correct_count,
            model.section_id,
            None, // chapter_id 在当前模型中不存在
            model.question_set.clone(),
            model.url.clone(),
            Some(model.status), // 直接使用status字段
            model.is_active,
            model.created_at.with_timezone(&Utc),
            model.updated_at.with_timezone(&Utc),
        )
    }

    /// 从领域实体转换为数据库模型 (用于保存)
    pub fn to_db_model(question: &Question) -> Result<QuestionModel::ActiveModel> {
        use sea_orm::ActiveValue::Set;

        let question_id = question.numeric_id()
            .ok_or_else(|| Error::service("问题ID必须是数字格式".to_string()))?;

        Ok(QuestionModel::ActiveModel {
            question_id: Set(question_id),
            subject_id: Set(question.subject_id),
            knowledge_id: Set(question.knowledge_id),
            type_id: Set(question.type_id),
            difficulty: Set(question.difficulty as i16),
            question_content: Set(question.content.clone()),
            options: Set(question.options.clone()),
            answer: Set(question.answer.clone()),
            explanation: Set(question.explanation.clone()),
            elo_rating: Set(question.elo_rating),
            irt_difficulty: Set(question.irt_difficulty),
            irt_discrimination: Set(question.irt_discrimination),
            irt_guessing: Set(question.irt_guessing),
            usage_count: Set(question.usage_count),
            correct_count: Set(question.correct_count),
            section_id: Set(question.section_id),
            question_set: Set(question.question_set.clone()),
            url: Set(question.url.clone()),
            status: Set(question.status.unwrap_or(1)), // 默认状态为1
            edit_status: Set(0), // 默认编辑状态为0
            is_active: Set(question.is_active),
            created_at: Set(question.created_at.into()),
            updated_at: Set(question.updated_at.into()),
        })
    }

    /// 从领域实体转换为 QuestionContent DTO
    pub fn to_question_content(question: &Question) -> QuestionContent {
        QuestionContent {
            id: question.id.clone(),
            subject_id: question.subject_id,
            knowledge_id: question.knowledge_id,
            type_id: question.type_id,
            difficulty: question.difficulty,
            question_content: question.content.clone(),
            options: question.options.clone().unwrap_or(Value::Null),
            answer: question.answer.clone(),
            explanation: question.explanation.clone().unwrap_or(Value::Null),
            elo_rating: question.elo_rating,
            usage_count: question.usage_count,
            correct_count: question.correct_count,
            question_set: question.question_set.clone(),
            url: question.url.clone(),
            is_active: question.is_active,
            created_at: question.created_at,
            updated_at: question.updated_at,
            section_id: question.section_id,
            metadata: None, // 默认为空
        }
    }

    /// 从 QuestionContent DTO 转换为领域实体
    pub fn from_question_content(content: &QuestionContent) -> Question {
        Question::from_full_data(
            content.id.clone(),
            content.subject_id,
            content.knowledge_id,
            content.type_id,
            content.difficulty,
            content.question_content.clone(),
            if content.options == Value::Null { None } else { Some(content.options.clone()) },
            content.answer.clone(),
            if content.explanation == Value::Null { None } else { Some(content.explanation.clone()) },
            content.elo_rating,
            None, // IRT参数在QuestionContent中不存在
            None,
            None,
            content.usage_count,
            content.correct_count,
            None, // section_id 在QuestionContent中不存在
            None, // chapter_id 在QuestionContent中不存在
            content.question_set.clone(),
            content.url.clone(),
            None, // status 在QuestionContent中不存在
            content.is_active,
            content.created_at,
            content.updated_at,
        )
    }

    /// 从领域实体转换为 QuestionMetadata
    pub fn to_question_metadata(
        question: &Question, 
        knowledge_name: Option<String>
    ) -> crate::domain::question::QuestionMetadata {
        crate::domain::question::QuestionMetadata {
            id: question.id.clone(),
            subject_id: question.subject_id,
            knowledge_id: question.knowledge_id,
            knowledge_name,
            type_id: question.type_id,
            difficulty: question.difficulty,
            elo_rating: Some(question.elo_rating),
            correct_ratio: Some(question.correct_ratio()),
        }
    }

    /// 批量转换数据库模型为领域实体
    pub fn batch_from_db_models(models: &[QuestionModel::Model]) -> Vec<Question> {
        models.iter().map(Self::from_db_model).collect()
    }

    /// 批量转换领域实体为 QuestionContent DTO
    pub fn batch_to_question_content(questions: &[Question]) -> Vec<QuestionContent> {
        questions.iter().map(Self::to_question_content).collect()
    }

    /// 批量从数据库模型转换为旧的 models::Question (用于存储层兼容)
    pub fn batch_from_db_models_to_legacy(models: &[QuestionModel::Model]) -> Vec<crate::models::Question> {
        models.iter().map(Self::from_db_model_to_legacy).collect()
    }

    /// 批量从数据库模型转换为 QuestionContent DTO (用于存储层兼容)
    pub fn batch_from_db_models_to_content(models: &[QuestionModel::Model]) -> Vec<QuestionContent> {
        let questions = Self::batch_from_db_models(models);
        Self::batch_to_question_content(&questions)
    }

    /// 从旧的 models::Question 转换为统一的领域实体
    pub fn from_legacy_model(legacy: &crate::models::Question) -> Question {
        Question::from_full_data(
            legacy.id.to_string(),
            legacy.subject_id,
            legacy.knowledge_id,
            legacy.type_id,
            legacy.difficulty as f64,
            Value::String(legacy.content.clone()),
            legacy.options.clone(),
            Value::String(legacy.answer.clone()),
            legacy.explanation.as_ref().map(|e| Value::String(e.clone())),
            legacy.elo_rating,
            legacy.irt_difficulty,
            legacy.irt_discrimination,
            legacy.irt_guessing,
            legacy.usage_count,
            legacy.correct_count,
            None, // section_id 在旧模型中不存在
            None, // chapter_id 在旧模型中不存在
            None, // question_set 在旧模型中不存在
            None, // url 在旧模型中不存在
            None, // status 在旧模型中不存在
            legacy.is_active,
            Utc::now(), // 旧模型中没有时间字段，使用当前时间
            Utc::now(),
        )
    }

    /// 从数据库模型转换为旧的 models::Question (用于存储层兼容)
    pub fn from_db_model_to_legacy(model: &QuestionModel::Model) -> crate::models::Question {
        crate::models::Question {
            id: model.question_id,
            content: serde_json::to_string(&model.question_content).unwrap_or_default(),
            options: model.options.clone(),
            answer: serde_json::to_string(&model.answer).unwrap_or_default(),
            explanation: model
                .explanation
                .as_ref()
                .map(|e| serde_json::to_string(e).unwrap_or_default()),
            knowledge_id: model.knowledge_id,
            subject_id: model.subject_id,
            type_id: model.type_id,
            difficulty: model.difficulty as i32,
            elo_rating: model.elo_rating,
            irt_difficulty: model.irt_difficulty,
            irt_discrimination: model.irt_discrimination,
            irt_guessing: model.irt_guessing,
            usage_count: model.usage_count,
            correct_count: model.correct_count,
            is_active: model.is_active,
        }
    }

    /// 转换为旧的 models::Question (用于向后兼容)
    pub fn to_legacy_model(question: &Question) -> Result<crate::models::Question> {
        let id = question.numeric_id()
            .ok_or_else(|| Error::service("问题ID必须是数字格式".to_string()))?;

        let content = match &question.content {
            Value::String(s) => s.clone(),
            _ => question.content.to_string(),
        };

        let answer = match &question.answer {
            Value::String(s) => s.clone(),
            _ => question.answer.to_string(),
        };

        let explanation = question.explanation.as_ref().and_then(|e| match e {
            Value::String(s) => Some(s.clone()),
            _ => Some(e.to_string()),
        });

        Ok(crate::models::Question {
            id,
            content,
            options: question.options.clone(),
            answer,
            explanation,
            knowledge_id: question.knowledge_id,
            subject_id: question.subject_id,
            type_id: question.type_id,
            difficulty: question.difficulty as i32,
            elo_rating: question.elo_rating,
            irt_difficulty: question.irt_difficulty,
            irt_discrimination: question.irt_discrimination,
            irt_guessing: question.irt_guessing,
            usage_count: question.usage_count,
            correct_count: question.correct_count,
            is_active: question.is_active,
        })
    }
}

impl Default for QuestionConverter {
    fn default() -> Self {
        Self::new()
    }
}
