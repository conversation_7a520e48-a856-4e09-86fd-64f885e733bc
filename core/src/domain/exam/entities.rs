//! 考试领域实体
//!
//! 定义考试系统的核心业务实体

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

use super::value_objects::{ExamType, SessionStatus, ModuleType, Subject, ModuleStatus, AnswerStatus};

/// 考试会话实体
/// 
/// 聚合根，管理整个考试的生命周期
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExamSession {
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
    /// 试卷ID
    pub paper_id: i64,
    /// 考试类型
    pub exam_type: ExamType,
    /// 当前学科
    pub current_subject: Subject,
    /// 当前模块类型
    pub current_module_type: Option<ModuleType>,
    /// 会话状态
    pub session_status: SessionStatus,
    /// 总用时（秒）
    pub total_time_seconds: i32,
    /// 语言部分用时（秒）
    pub reading_time_seconds: i32,
    /// 数学部分用时（秒）
    pub math_time_seconds: i32,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
    /// 完成时间
    pub completed_at: Option<DateTime<Utc>>,
}

impl ExamSession {
    /// 创建新的考试会话
    pub fn new(
        session_id: String,
        user_id: i64,
        paper_id: i64,
        exam_type: ExamType,
    ) -> Self {
        let now = Utc::now();

        // 根据考试类型确定初始学科
        let current_subject = match exam_type {
            ExamType::Full => Subject::Reading,      // 全长考试从阅读开始
            ExamType::Reading => Subject::Reading,   // 阅读考试
            ExamType::Math => Subject::Math,         // 数学考试从数学开始
        };

        Self {
            session_id,
            user_id,
            paper_id,
            exam_type,
            current_subject,
            current_module_type: None,
            session_status: SessionStatus::InProgress,
            total_time_seconds: 0,
            reading_time_seconds: 0,
            math_time_seconds: 0,
            created_at: now,
            updated_at: now,
            completed_at: None,
        }
    }

    /// 开始模块
    pub fn start_module(&mut self, module_type: ModuleType) {
        self.current_module_type = Some(module_type);
        self.updated_at = Utc::now();
    }

    /// 切换到下一个学科
    pub fn switch_to_next_subject(&mut self) {
        match self.current_subject {
            Subject::Reading => self.current_subject = Subject::Math,
            Subject::Math => {
                // 数学部分完成，考试结束
                self.session_status = SessionStatus::Completed;
                self.completed_at = Some(Utc::now());
            }
        }
        self.updated_at = Utc::now();
    }

    /// 完成考试
    pub fn complete(&mut self) {
        self.session_status = SessionStatus::Completed;
        self.completed_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    /// 检查是否可以开始指定模块
    pub fn can_start_module(&self, module_type: &ModuleType) -> bool {
        match module_type {
            ModuleType::Module1 => true, // 第一模块总是可以开始
            ModuleType::Module2E | ModuleType::Module2H => {
                // 第二模块需要第一模块完成
                self.current_module_type.is_some()
            }
        }
    }
}

/// 考试答题记录实体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExamAnswer {
    /// 记录ID
    pub id: Option<i64>,
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
    /// 试卷ID
    pub paper_id: i64,
    /// 题目ID
    pub question_id: i32,
    /// 模块类型
    pub module_type: ModuleType,
    /// 模块内题目顺序
    pub module_sequence: i32,
    /// 用户答案
    pub user_answer: Option<String>,
    /// 是否正确
    pub is_correct: Option<bool>,
    /// 答题用时（秒）
    pub response_time_seconds: Option<i32>,
    /// 答题状态
    pub answer_status: AnswerStatus,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

impl ExamAnswer {
    /// 创建新的答题记录
    pub fn new(
        session_id: String,
        user_id: i64,
        paper_id: i64,
        question_id: i32,
        module_type: ModuleType,
        module_sequence: i32,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: None,
            session_id,
            user_id,
            paper_id,
            question_id,
            module_type,
            module_sequence,
            user_answer: None,
            is_correct: None,
            response_time_seconds: None,
            answer_status: AnswerStatus::Unanswered,
            created_at: now,
            updated_at: now,
        }
    }

    /// 提交答案
    pub fn submit_answer(&mut self, answer: String, is_correct: bool, response_time: i32) {
        self.user_answer = Some(answer);
        self.is_correct = Some(is_correct);
        self.response_time_seconds = Some(response_time);
        self.answer_status = AnswerStatus::Answered;
        self.updated_at = Utc::now();
    }

    /// 跳过题目
    pub fn skip(&mut self) {
        self.answer_status = AnswerStatus::Skipped;
        self.updated_at = Utc::now();
    }
}

/// 考试模块进度实体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExamModuleProgress {
    /// 记录ID
    pub id: Option<i64>,
    /// 会话ID
    pub session_id: String,
    /// 用户ID
    pub user_id: i64,
    /// 试卷ID
    pub paper_id: i64,
    /// 模块类型
    pub module_type: ModuleType,
    /// 学科
    pub subject: Subject,
    /// 总题数
    pub total_questions: i32,
    /// 已答题数
    pub answered_questions: i32,
    /// 答对题数
    pub correct_questions: i32,
    /// 时间限制（秒）
    pub time_limit_seconds: i32,
    /// 已用时间（秒）
    pub time_used_seconds: i32,
    /// 剩余时间（秒）
    pub remaining_time_seconds: Option<i32>,
    /// 模块状态
    pub module_status: ModuleStatus,
    /// 开始时间
    pub started_at: Option<DateTime<Utc>>,
    /// 完成时间
    pub completed_at: Option<DateTime<Utc>>,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

impl ExamModuleProgress {
    /// 创建新的模块进度
    pub fn new(
        session_id: String,
        user_id: i64,
        paper_id: i64,
        module_type: ModuleType,
        subject: Subject,
        total_questions: i32,
        time_limit_seconds: i32,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: None,
            session_id,
            user_id,
            paper_id,
            module_type,
            subject,
            total_questions,
            answered_questions: 0,
            correct_questions: 0,
            time_limit_seconds,
            time_used_seconds: 0,
            remaining_time_seconds: Some(time_limit_seconds),
            module_status: ModuleStatus::NotStarted,
            started_at: None,
            completed_at: None,
            created_at: now,
            updated_at: now,
        }
    }

    /// 开始模块
    pub fn start(&mut self) {
        self.module_status = ModuleStatus::InProgress;
        self.started_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    /// 更新进度
    pub fn update_progress(&mut self, answered: i32, correct: i32, time_used: i32) {
        self.answered_questions = answered;
        self.correct_questions = correct;
        self.time_used_seconds = time_used;
        self.remaining_time_seconds = Some((self.time_limit_seconds - time_used).max(0));
        self.updated_at = Utc::now();
    }

    /// 完成模块
    pub fn complete(&mut self) {
        self.module_status = ModuleStatus::Completed;
        self.completed_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    /// 提交模块
    pub fn submit(&mut self) {
        self.module_status = ModuleStatus::Submitted;
        self.updated_at = Utc::now();
    }

    /// 检查是否超时
    pub fn is_timeout(&self) -> bool {
        self.remaining_time_seconds.map_or(false, |remaining| remaining <= 0)
    }
}
