use dashmap::DashMap;
use std::collections::HashMap;
use std::fmt::{self, Debug};
use std::sync::Arc;
use tracing::warn;

use crate::Error;
use crate::Result;

use crate::algorithms::base::{AbilityAlgorithm, AlgorithmConfig, RecommenderAlgorithm};
use crate::algorithms::elo::EloAlgorithm;

use crate::algorithms::irt::IrtAlgorithm;
// use crate::algorithms::random::RandomRecommender; // 暂时禁用，依赖已删除的 QuestionStorage
// use crate::storage::QuestionStorage; // 已删除

/// 算法注册表
///
/// 用于管理系统中的算法实现，支持动态注册和检索算法。
/// 提供能力评估算法和推荐算法的注册、实例化和缓存功能。
pub struct AlgorithmRegistry {
    // 能力评估算法工厂 - 接受配置并返回算法实例
    ability_factories: HashMap<
        String,
        Box<
            dyn Fn(Box<dyn AlgorithmConfig>) -> Result<Arc<dyn AbilityAlgorithm + Send + Sync>>
                + Send
                + Sync,
        >,
    >,

    // 推荐算法工厂 - 接受配置并返回算法实例
    recommender_factories: HashMap<
        String,
        Box<
            dyn Fn(Box<dyn AlgorithmConfig>) -> Result<Arc<dyn RecommenderAlgorithm + Send + Sync>>
                + Send
                + Sync,
        >,
    >,

    // 能力评估算法实例缓存 - 使用DashMap替代RwLock<HashMap>
    ability_instances: DashMap<String, Arc<dyn AbilityAlgorithm + Send + Sync>>,

    // 推荐算法实例缓存 - 使用DashMap替代RwLock<HashMap>
    recommender_instances: DashMap<String, Arc<dyn RecommenderAlgorithm + Send + Sync>>,

    // 题目存储 - 已删除，RandomRecommender 暂时不可用
    // question_storage: Option<Arc<dyn QuestionStorage>>,
}

impl Debug for AlgorithmRegistry {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("AlgorithmRegistry")
            .field(
                "ability_factories",
                &format!("{} items", self.ability_factories.len()),
            )
            .field(
                "recommender_factories",
                &format!("{} items", self.recommender_factories.len()),
            )
            .field(
                "ability_instances",
                &format!("{} items", self.ability_instances.len()),
            )
            .field(
                "recommender_instances",
                &format!("{} items", self.recommender_instances.len()),
            )
            .finish()
    }
}

impl Default for AlgorithmRegistry {
    fn default() -> Self {
        Self {
            ability_factories: HashMap::new(),
            recommender_factories: HashMap::new(),
            ability_instances: DashMap::new(),
            recommender_instances: DashMap::new(),
            // question_storage: None, // 已删除
        }
    }
}

impl AlgorithmRegistry {
    /// 创建新的算法注册表
    pub fn new() -> Self {
        Self::default()
    }

    /// 设置题目存储 - 已删除，RandomRecommender 暂时不可用
    // pub fn with_question_storage(mut self, storage: Arc<dyn QuestionStorage>) -> Self {
    //     self.question_storage = Some(storage);
    //     self
    // }

    /// 获取题目存储 - 已删除，RandomRecommender 暂时不可用
    // pub fn get_question_storage(&self) -> Option<Arc<dyn QuestionStorage>> {
    //     self.question_storage.clone()
    // }

    /// 注册能力评估算法
    ///
    /// # 参数
    /// * `name` - 算法名称
    /// * `factory` - 创建算法实例的工厂函数
    ///
    /// # 返回
    /// * 返回自身引用，支持链式调用
    pub fn register_ability_algorithm<F, A>(&mut self, name: &str, factory: F) -> &mut Self
    where
        F: Fn() -> A + Send + Sync + 'static,
        A: AbilityAlgorithm + Send + Sync + 'static,
    {
        let name = name.to_string();
        let factory_boxed = Box::new(move |_config: Box<dyn AlgorithmConfig>| -> Result<Arc<dyn AbilityAlgorithm + Send + Sync>> {
            // 创建算法实例 - 不再在这里调用初始化
            // 初始化将在后续的异步流程中进行
            let algorithm = factory();

            // 返回Arc包装的实例
            Ok(Arc::new(algorithm))
        });

        self.ability_factories.insert(name, factory_boxed);
        self
    }

    /// 注册推荐算法
    ///
    /// # 参数
    /// * `name` - 算法名称
    /// * `factory` - 创建算法实例的工厂函数
    ///
    /// # 返回
    /// * 返回自身引用，支持链式调用
    pub fn register_recommender_algorithm<F, R>(&mut self, name: &str, factory: F) -> &mut Self
    where
        F: Fn() -> R + Send + Sync + 'static,
        R: RecommenderAlgorithm + Send + Sync + 'static,
    {
        let name = name.to_string();
        let factory_boxed = Box::new(move |_config: Box<dyn AlgorithmConfig>| -> Result<Arc<dyn RecommenderAlgorithm + Send + Sync>> {
            // 创建算法实例 - 不再在这里调用初始化
            // 初始化将在后续的异步流程中进行
            let algorithm = factory();

            // 返回Arc包装的实例
            Ok(Arc::new(algorithm))
        });

        self.recommender_factories.insert(name, factory_boxed);
        self
    }

    /// 获取能力评估算法实例
    ///
    /// 如果指定名称的算法实例已存在，则直接返回；否则创建新实例并初始化。
    ///
    /// # 参数
    /// * `name` - 算法名称
    /// * `config` - 算法配置
    ///
    /// # 返回
    /// * 返回算法实例的Arc引用
    pub async fn get_ability_algorithm<C>(
        &self,
        name: &str,
        config: C,
    ) -> Result<Arc<dyn AbilityAlgorithm + Send + Sync>>
    where
        C: AlgorithmConfig + Clone,
    {
        let instance_key = format!("ability:{}", name);

        // 使用DashMap的get方法检查已有实例
        if let Some(entry) = self.ability_instances.get(&instance_key) {
            return Ok(entry.value().clone());
        }

        // 创建新实例
        let factory = self
            .ability_factories
            .get(name)
            .ok_or_else(|| Error::algorithm(format!("未找到能力评估算法: {}", name)))?;

        // 创建配置
        let boxed_config = Box::new(config.clone()) as Box<dyn AlgorithmConfig>;

        // 获取未初始化的算法实例
        let algorithm = factory(boxed_config)?;

        // 记录日志
        warn!(
            "返回未初始化的算法实例: {}。算法应当实现内部异步初始化。",
            name
        );

        // 使用DashMap的insert方法存储实例
        self.ability_instances
            .insert(instance_key, algorithm.clone());

        Ok(algorithm)
    }

    /// 获取推荐算法实例
    ///
    /// 如果指定名称的算法实例已存在，则直接返回；否则创建新实例并初始化。
    ///
    /// # 参数
    /// * `name` - 算法名称
    /// * `config` - 算法配置
    ///
    /// # 返回
    /// * 返回算法实例的Arc引用
    pub async fn get_recommender_algorithm<C>(
        &self,
        name: &str,
        config: C,
    ) -> Result<Arc<dyn RecommenderAlgorithm + Send + Sync>>
    where
        C: AlgorithmConfig + Clone,
    {
        let instance_key = format!("recommender:{}", name);

        // 使用DashMap的get方法检查已有实例
        if let Some(entry) = self.recommender_instances.get(&instance_key) {
            return Ok(entry.value().clone());
        }

        // 创建新实例
        let factory = self
            .recommender_factories
            .get(name)
            .ok_or_else(|| Error::algorithm(format!("未找到推荐算法: {}", name)))?;

        // 创建配置
        let boxed_config = Box::new(config.clone()) as Box<dyn AlgorithmConfig>;

        // 获取未初始化的算法实例
        let algorithm = factory(boxed_config)?;

        // 记录日志
        warn!(
            "返回未初始化的算法实例: {}。算法应当实现内部异步初始化。",
            name
        );

        // 使用DashMap的insert方法存储实例
        self.recommender_instances
            .insert(instance_key, algorithm.clone());

        Ok(algorithm)
    }

    /// 列出所有已注册的能力评估算法
    pub fn list_ability_algorithms(&self) -> Vec<String> {
        self.ability_factories.keys().cloned().collect()
    }

    /// 列出所有已注册的推荐算法
    pub fn list_recommender_algorithms(&self) -> Vec<String> {
        self.recommender_factories.keys().cloned().collect()
    }

    /// 注册所有算法
    pub fn register_all_algorithms(&mut self) -> Result<()> {
        // 注册ELO算法
        let elo_factory = || EloAlgorithm::new();
        self.register_ability_algorithm("elo", elo_factory);

        // 注册IRT算法
        let irt_factory = || IrtAlgorithm::new("irt");
        self.register_ability_algorithm("irt", irt_factory);



        // 注册随机推荐算法 - 暂时禁用，依赖已删除的 QuestionStorage
        // if let Some(question_storage) = self.question_storage.as_ref() {
        //     let question_storage = Arc::clone(question_storage);
        //     let random_factory = move || {
        //         let storage = Arc::clone(&question_storage);
        //         RandomRecommender::new("random", storage)
        //     };
        //     self.register_recommender_algorithm("random", random_factory);
        // } else {
        //     warn!("题目存储不可用，随机推荐算法未注册");
        // }
        warn!("RandomRecommender 暂时不可用，因为依赖的 QuestionStorage 已被删除");

        // 注意：AdaptiveMasteryAlgorithm 不再实现 RecommenderAlgorithm trait
        // 它现在只提供工具方法，由服务层直接使用，不需要在算法注册表中注册

        tracing::info!("算法注册完成: {} 个能力算法, {} 个推荐算法",
            self.ability_factories.len(),
            self.recommender_factories.len());

        Ok(())
    }
}
