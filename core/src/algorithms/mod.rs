//! 算法模块
//!
//! 该模块提供了各种算法的实现，包括：
//! - 能力评估算法（ELO、IRT等）
//! - 推荐算法

pub mod adaptive_mastery;
pub mod base;
pub mod elo; // 现在指向子模块
pub mod irt; // IRT模型算法模块
// pub mod random; // 随机推荐算法模块 - 已删除，依赖的 QuestionStorage 已被移除
pub mod recommenders; // 推荐算法（已清理未使用的推荐器）
pub mod registry;

// 重导出常用类型
pub use adaptive_mastery::{AdaptiveMasteryAlgorithm, AdaptiveMasteryConfig};
pub use base::{
    AbilityAlgorithm, Algorithm, AlgorithmConfig, RecommendationContext, RecommendationItem,
    RecommenderAlgorithm, StudentAnswerRecord,
};
pub use elo::{EloCalculator, EloConfig}; // 导出ELO配置和计算器
pub use irt::IrtConfig; // 导出IRT配置
// pub use random::{QueryStats, RandomRecommender, RandomRecommenderConfig}; // 已删除
pub use registry::AlgorithmRegistry;
